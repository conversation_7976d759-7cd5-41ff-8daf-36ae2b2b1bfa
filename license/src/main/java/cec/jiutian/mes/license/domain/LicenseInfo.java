package cec.jiutian.mes.license.domain;

import cec.jiutian.core.comn.util.DateUtils;
import cec.jiutian.core.comn.util.StringUtils;
import cec.jiutian.core.entity.Domain;
import cec.jiutian.mes.license.model.AuthenticationLicenseExt;
import cec.jiutian.mes.license.util.Base64Utils;
import cec.jiutian.mes.license.util.RSAUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

public class LicenseInfo implements Domain<AuthenticationLicenseExt> {

    private AuthenticationLicenseExt authenticationLicenseExt;

    private static final String LICENSE_SEPARATOR = "CECJIUTIAN";

    @Override
    public void setAuditField() {

    }

    @Override
    public AuthenticationLicenseExt getEntity() {
        return authenticationLicenseExt;
    }

    @Override
    public void setEntity(AuthenticationLicenseExt authenticationLicense) {
        this.authenticationLicenseExt = authenticationLicense;
    }

    public LicenseInfo(AuthenticationLicenseExt authenticationLicenseExt) {
        this.authenticationLicenseExt = authenticationLicenseExt;
    }

    public LicenseInfo(String license) {
        AuthenticationLicenseExt authenticationLicenseExt = new AuthenticationLicenseExt();
        String[] licenseInfo = license.split(LICENSE_SEPARATOR);
        authenticationLicenseExt.setLicenseData(licenseInfo[0]);
        authenticationLicenseExt.setLicenseSignData(licenseInfo[1]);
        authenticationLicenseExt.setLicensePublicKey(licenseInfo[2]);
        this.authenticationLicenseExt = authenticationLicenseExt;
    }

    public void setDecryptData() throws Exception {
        AuthenticationLicenseExt authenticationLicenseExt = this.getEntity();

        StringBuffer reasonText = new StringBuffer();
        byte[] encodeDate;
        String[] licenseInfo;

        if (StringUtils.isBlank(authenticationLicenseExt.getLicenseData())) {
            authenticationLicenseExt.setValid(false);
            authenticationLicenseExt.setReasonText(reasonText.append("授权信息为空").toString());
            return;
        }

        try {
            encodeDate = Base64Utils.decode(authenticationLicenseExt.getLicenseData());
            licenseInfo = new String(RSAUtils.decryptByPublicKey(encodeDate, authenticationLicenseExt.getLicensePublicKey())).split("\\+");
        } catch (Exception e) {
            authenticationLicenseExt.setValid(false);
            authenticationLicenseExt.setReasonText(reasonText.append("授权信息异常").toString());
            return;
        }

        String decryptSystemCode = licenseInfo[2];
        String decryptSystemVersion = licenseInfo[3];
        authenticationLicenseExt.setDecryptSystemCode(decryptSystemCode);
        authenticationLicenseExt.setDecryptSystemVersion(decryptSystemVersion);
        if (StringUtils.isNotBlank(decryptSystemCode) && StringUtils.isNotBlank(authenticationLicenseExt.getSystemCode())) {
            if (!StringUtils.equals(decryptSystemCode, authenticationLicenseExt.getSystemCode())) {
                authenticationLicenseExt.setValid(false);
                reasonText.append("授权的系统代码与平台保存的代码不符合。");
            }
            if (!StringUtils.equals(decryptSystemVersion, authenticationLicenseExt.getSystemVersion())) {
                authenticationLicenseExt.setValid(false);
                reasonText.append("授权的系统代码版本与平台保存的代码版本不符合。");
            }
        }
        if(StringUtils.isNotBlank(decryptSystemCode) && StringUtils.isBlank(authenticationLicenseExt.getLicenseKey())) {
            String cpuId = licenseInfo[0];
            authenticationLicenseExt.setLicenseKey(cpuId + ":" + decryptSystemCode);
        }
        Date expireTime = DateUtils.parseStringToDate(licenseInfo[1]);
        authenticationLicenseExt.setDecryptExpireDate(expireTime);
        Date now = new Date();
        int dayDiffer = getDayDiffer(now, expireTime);

        if (now.after(expireTime)) {
            authenticationLicenseExt.setValid(false);
            reasonText.append("授权的系统已经过期。");
        }

        authenticationLicenseExt.setRemainDay(dayDiffer);
        authenticationLicenseExt.setReasonText(reasonText.toString());
        try {
            if (!RSAUtils.verify(encodeDate, authenticationLicenseExt.getLicensePublicKey(), authenticationLicenseExt.getLicenseSignData())) {
                authenticationLicenseExt.setValid(false);
                authenticationLicenseExt.setReasonText(reasonText.append("授权信息无效。").toString());
            }
        } catch (Exception e) {
            authenticationLicenseExt.setValid(false);
            authenticationLicenseExt.setReasonText(reasonText.append("授权信息异常。").toString());
            return;
        }
    }

    public static int getDayDiffer(Date startDate, Date endDate) throws ParseException {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        try {
            long startDateTime = dateFormat.parse(dateFormat.format(startDate)).getTime();
            long endDateTime = dateFormat.parse(dateFormat.format(endDate)).getTime();
            return (int) ((endDateTime - startDateTime) / (1000 * 3600 * 24));
        } catch (Exception e) {
            throw e;
        }
    }

}
