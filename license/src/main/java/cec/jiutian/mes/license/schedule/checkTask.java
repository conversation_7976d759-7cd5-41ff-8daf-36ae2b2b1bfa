package cec.jiutian.mes.license.schedule;

import cec.jiutian.mes.license.service.AuthenticationLicenseService;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.stereotype.Component;


@Component
public class checkTask {
    private final AuthenticationLicenseService authenticationLicenseService;

    public checkTask(AuthenticationLicenseService authenticationLicenseService) {
        this.authenticationLicenseService = authenticationLicenseService;
    }


    /**
     * <p>检查平台所有有效系统携带license，判断已经失效的license和即将失效(默认提前十天)的license</p>
     * <p>每天23点</p>
     * <p>cron：0 0 23 * * ?</p>
     */
    @XxlJob("checkLicenseTask")
    public void checkLicenseTask() {
        authenticationLicenseService.checkLicenseTask();
    }
}
