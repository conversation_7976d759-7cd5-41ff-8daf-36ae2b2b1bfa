package cec.jiutian.mes.license.mapper;

import cec.jiutian.core.base.BaseMapper;
import cec.jiutian.mes.license.model.AuthenticationLicense;
import cec.jiutian.mes.license.model.AuthenticationLicenseExt;
import cec.jiutian.mes.license.model.PasswordDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/5/8
 */
@Mapper
public interface AuthenticationLicenseMapper extends BaseMapper<AuthenticationLicense> {
    List<AuthenticationLicense> getBySystem(String systemCode, String systemVersion);

    @Select("select PASS_DGST AS passwordDigest, PSWRD as password from fd_acnt where ID = 'ROOT'")
    PasswordDTO getAdminPassword();

    @Update("update fd_systm_cmpnt set SYSTM_LCNS = #{licenseKey} where SYSTM_CD = #{systemCode} and SYSTM_VRSN = #{systemVersion}")
    void updateLicense(String licenseKey, String systemCode, String systemVersion);

    @Select("<script>"
            + "select SYSTM_LCNS AS systemLicense from fd_systm_cmpnt where 1=1 "
            + "<if test='_parameter != null and _parameter != &quot;&quot;'>"
            + "and SYSTM_CD = #{_parameter,jdbcType=VARCHAR}"
            + "</if>"
            + "</script>")
    String getSystemLicense(String systemCode);

    @Select("select VLD_FLG from fd_systm_cmpnt where SYSTM_CD = #{systemCode}")
    String getSystemValidFlag(String systemCode);

    List<AuthenticationLicenseExt> getLicenseData();

    @Select("SELECT SYSTM_CNFGTN_VLU FROM FD_SYSTM_CNFGTN WHERE 1=1 and SYSTM_CNFGTN_CD = #{dayAdvanceCheckLicense} ")
    String getSystemConfiguration(String dayAdvanceCheckLicense);
}
