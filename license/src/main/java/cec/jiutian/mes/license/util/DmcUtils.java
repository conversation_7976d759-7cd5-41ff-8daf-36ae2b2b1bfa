package cec.jiutian.mes.license.util;

import cec.jiutian.core.comn.errorCode.BaseErrorCode;
import cec.jiutian.core.exception.MesErrorCodeException;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.Scanner;

/**
 * <AUTHOR>
 * @date 2020/5/8
 */
@Slf4j
public class DmcUtils {
    /**
     * 获取当前操作系统名称
     */
    public static String getOSName() {
        return System.getProperty("os.name").toLowerCase();
    }

    /**
     * 获取linux服务器UUID
     * @return
     */
    public static String getUUID_linux() throws IOException {
        String UUID_CMD = "dmidecode -s system-uuid";
        Process process = Runtime.getRuntime().exec(UUID_CMD);
        process.getOutputStream().close();
        Scanner sc = new Scanner(process.getInputStream());
        return sc.next().toLowerCase();
    }

    /**
     * 获取windows服务器UUID
     * @return
     */
    public static String getUUID_windows() throws IOException {
        Process process = Runtime.getRuntime().exec(new String[] { "wmic", "csproduct", "get", "UUID" });
        process.getOutputStream().close();
        Scanner sc = new Scanner(process.getInputStream());
        sc.next();
        return sc.next().toLowerCase();
    }

    public static String getUUID() {
        String uuid;
        try {
            if (DmcUtils.getOSName().contains("linux")) {
                uuid = DmcUtils.getUUID_linux();
            } else if (DmcUtils.getOSName().contains("windows")) {
                uuid = DmcUtils.getUUID_windows();
            } else {
                uuid = "";
            }
        } catch (IOException e) {
            MesErrorCodeException exception = new MesErrorCodeException(e, BaseErrorCode.EXCEPTION.getCode());
            log.error("{}", exception.getMessage(), exception);
            throw exception;
        }
        return uuid;
    }
}
