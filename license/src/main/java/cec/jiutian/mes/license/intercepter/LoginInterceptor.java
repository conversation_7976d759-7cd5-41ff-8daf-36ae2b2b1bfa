package cec.jiutian.mes.license.intercepter;

import cec.jiutian.core.exception.MesErrorCodeException;
import cec.jiutian.mes.license.model.LoginDTO;
import cec.jiutian.mes.license.service.AuthenticationLicenseService;
import cec.jiutian.mes.license.util.AESUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 2020/9/29
 */
@Slf4j
@Component
public class LoginInterceptor implements HandlerInterceptor {
    private static final String DATA_DECRYPT_ERROR = "CORE-000027";
    private static final ObjectMapper objectMapper = new ObjectMapper();

    private final AuthenticationLicenseService authenticationLicenseService;

    public LoginInterceptor(AuthenticationLicenseService authenticationLicenseService) {
        this.authenticationLicenseService = authenticationLicenseService;
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        LoginDTO loginDTO;
        String login = objectMapper.readTree(request.getInputStream()).get("login").textValue();
        if (request.getRequestURI().equals("/getLoginInfo")) {
            loginDTO = AESUtils.serverDecrypt(login);
            if (loginDTO == null) {
                MesErrorCodeException exception = new MesErrorCodeException(DATA_DECRYPT_ERROR);
                log.error("{}", exception.getMessage(), exception);
                throw exception;
            }
        } else {
            loginDTO = AESUtils.decrypt(login);
        }
        return authenticationLicenseService.verifyLicense(loginDTO.getSystemCode(), loginDTO.getSystemVersion());
    }
}
