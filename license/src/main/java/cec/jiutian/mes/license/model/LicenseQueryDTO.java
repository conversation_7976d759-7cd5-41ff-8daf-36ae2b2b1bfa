package cec.jiutian.mes.license.model;

import cec.jiutian.security.base.dto.BaseQueryDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2022/3/23
 */
@Data
@ApiModel("LicenseUpdateTextDTO")
public class LicenseQueryDTO extends BaseQueryDTO {
    private static final long serialVersionUID = -1838588568559368400L;
    @ApiModelProperty("登录信息")
    @NotBlank
    private String login;

}
