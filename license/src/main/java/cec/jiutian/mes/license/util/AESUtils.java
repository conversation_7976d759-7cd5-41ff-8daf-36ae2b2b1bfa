package cec.jiutian.mes.license.util;

import cec.jiutian.core.comn.util.JsonUtils;
import cec.jiutian.mes.license.model.LoginDTO;
import org.apache.commons.codec.binary.Base64;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.Security;

public class AESUtils {
    /**
     * AES加密
     *
     * @param key  加密需要的KEY
     * @param iv   加密需要的向量
     * @param data 需要加密的数据
     * @return
     * @throws Exception
     */
    public static String encrypt(String key, String iv, String data) throws Exception {
        byte[] encrypted = {};
        byte[] enCodeFormat = key.getBytes();

        //密钥
        SecretKeySpec secretKeySpec = new SecretKeySpec(enCodeFormat, "AES");

        try {
            Security.addProvider(new org.bouncycastle.jce.provider.BouncyCastleProvider());
            //可以指定加密算法，加密模式和填充
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            //密钥+向量 
            cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec, new IvParameterSpec(iv.getBytes()));

            int blockSize = cipher.getBlockSize();
            System.out.println(data.length());
            byte[] dataBytes = data.getBytes();
            int plaintextLength = dataBytes.length;
            if (plaintextLength % blockSize != 0) {
                plaintextLength = plaintextLength + (blockSize - (plaintextLength % blockSize));
            }
            System.out.println(plaintextLength);
            byte[] plaintext = new byte[plaintextLength];
            System.arraycopy(dataBytes, 0, plaintext, 0, dataBytes.length);
            encrypted = cipher.doFinal(plaintext);

        } catch (InvalidKeyException | IllegalBlockSizeException | BadPaddingException | NoSuchPaddingException | NoSuchAlgorithmException | InvalidAlgorithmParameterException e) {
            e.printStackTrace();
        }

        return encryptBASE64(Base64.encodeBase64(encrypted));
    }

    /**
     * @param key 解密需要的KEY 同加密
     * @param iv  解密需要的向量 同加密
     * @return
     * @throws Exception
     */
    public static String decrypt(String key, String iv, String encryptData) throws Exception {
        String content = "";

        byte[] enCodeFormat = key.getBytes();
        SecretKeySpec secretKeySpec = new SecretKeySpec(enCodeFormat, "AES");
        Cipher cipher;// 创建密码器
        try {
            Security.addProvider(new org.bouncycastle.jce.provider.BouncyCastleProvider());
            cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            cipher.init(Cipher.DECRYPT_MODE, secretKeySpec, new IvParameterSpec(iv.getBytes()));// 初始化
            byte[] result = cipher.doFinal(decryptBASE64(encryptData));
            content = new String(result);
        } catch (NoSuchAlgorithmException | NoSuchPaddingException | BadPaddingException | IllegalBlockSizeException | InvalidKeyException | InvalidAlgorithmParameterException e) {
            e.printStackTrace();
        }
        return content; // 加密
    }

    public static byte[] decryptBASE64(String key) throws Exception {
        return Base64.decodeBase64(key.getBytes());
    }

    public static String encryptBASE64(byte[] key) throws Exception {
        return new String(Base64.encodeBase64(key), StandardCharsets.UTF_8);
    }

    public static LoginDTO decrypt(String login) throws Exception {
        return JsonUtils.fromJson(AESUtils.decrypt("Zl5!A3#E9FD60ANa", "1234567812345678", login), LoginDTO.class);
    }

    /**
     * 用于二次解密，二次加密由服务端进行加密，前端不参与加密和解密
     */
    public static LoginDTO serverDecrypt(String login) throws Exception {
        return JsonUtils.fromJson(AESUtils.decrypt("Kdm8LBbUbPCq6fCyGoNePxq7qVcuRZb9", "8765432112345678", login), LoginDTO.class);
    }
}
