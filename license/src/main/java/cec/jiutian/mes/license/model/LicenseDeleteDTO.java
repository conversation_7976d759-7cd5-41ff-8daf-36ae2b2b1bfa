package cec.jiutian.mes.license.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;


@Data
@ApiModel("LicenseDeleteDTO")
public class LicenseDeleteDTO implements Serializable {
    private static final long serialVersionUID = -1838588568559368400L;
    @ApiModelProperty("登录信息")
    @NotBlank
    private String login;

    AuthenticationLicenseExt authenticationLicenseExt;
}
