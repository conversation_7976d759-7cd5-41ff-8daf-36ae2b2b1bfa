package cec.jiutian.mes.license.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/5/8
 */
@Data
public class AuthenticationLicenseExt extends AuthenticationLicense {

    @ApiModelProperty("系统代码")
    private String systemCode;

    @ApiModelProperty("系统名称")
    private String systemName;

    @ApiModelProperty("系统版本")
    private String systemVersion;

    @ApiModelProperty("系统licenseKey，关联License表中的数据,不同的licenseKey用逗号分开")
    private String systemLicense;

    private String license;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date decryptExpireDate;

    private Integer decryptMaxNumber;

    private String decryptSystemCode;

    private String decryptSystemVersion;

    private Integer remainDay;

    private Boolean valid = true;

    private String reasonText;

}
