package cec.jiutian.mes.license.filter;

import cec.jiutian.mes.license.intercepter.BodyReaderHttpServletRequestWrapper;

import jakarta.servlet.*;
import jakarta.servlet.annotation.WebFilter;
import jakarta.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2020/10/15
 */
@WebFilter(urlPatterns = {"/login", "/getLoginInfo"}, filterName = "repeatReadFilter")
public class RepeatReadFilter implements Filter {
    @Override
    public void init(FilterConfig filterConfig) throws ServletException {

    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest httpServletRequest = (HttpServletRequest) request;
        ServletRequest requestWrapper = new BodyReaderHttpServletRequestWrapper(httpServletRequest);
        chain.doFilter(requestWrapper, response);
    }

    @Override
    public void destroy() {

    }
}
