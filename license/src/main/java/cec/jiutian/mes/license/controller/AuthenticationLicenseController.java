package cec.jiutian.mes.license.controller;

import cec.jiutian.core.base.BaseController;
import cec.jiutian.core.comn.util.BeanValidators;
import cec.jiutian.core.comn.util.StringUtils;
import cec.jiutian.mes.license.model.LicenseDeleteDTO;
import cec.jiutian.mes.license.model.LicenseQueryDTO;
import cec.jiutian.mes.license.model.LicenseSubmitDTO;
import cec.jiutian.mes.license.model.LicenseUpdateTextDTO;
import cec.jiutian.mes.license.service.AuthenticationLicenseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.ws.rs.core.Response;

/**
 * <AUTHOR>
 * @date 2020/5/9
 */
@RestController
@RequestMapping("/license")
@Api(tags = "license相关API")
public class AuthenticationLicenseController extends BaseController {
    private final AuthenticationLicenseService authenticationLicenseService;

    public AuthenticationLicenseController(AuthenticationLicenseService authenticationLicenseService) {
        this.authenticationLicenseService = authenticationLicenseService;
    }

    @PostMapping("/update")
    @ApiOperation(value = "许可证上传", notes = "仅ROOT用户允许上传系统license，相关表：")
    public Response upload(@RequestPart MultipartFile[] files, @RequestParam String login) {
        Object result = authenticationLicenseService.uploadLicense(files, login);
        return respSuccessResult(result,
                "需要上传的licenses");
    }

    @PostMapping("/submit")
    @ApiOperation(value = "确定许可证上传", notes = "仅ROOT用户可以上传license，为上传license时，用户点击确定后所使用的接口")
    public Response submit(@RequestBody LicenseSubmitDTO licenseSubmitDTO) {
        BeanValidators.validateWithException(validator, licenseSubmitDTO);
        Boolean result = authenticationLicenseService.submitLicense(licenseSubmitDTO);
        return respResult(result,
                "上传许可证成功！", "上传许可证失败");
    }

    @PostMapping("/delete")
    @ApiOperation(value = "删除授权信息", notes = "仅ROOT用户可以删除license")
    public Response delete(@RequestBody LicenseDeleteDTO licenseDeleteDTO) {
        BeanValidators.validateWithException(validator, licenseDeleteDTO);
        Boolean result = authenticationLicenseService.deleteLicense(licenseDeleteDTO);
        return respResult(result,
                "删除授权信息成功！", "删除授权信息失败");
    }

    @PostMapping("/update/text")
    @ApiOperation(value = "许可证上传", notes = "仅ROOT用户可以上传license，此接口为文本填入形式")
    public Response uploadText(@RequestBody LicenseUpdateTextDTO licenseUpdateTextDTO) {
        BeanValidators.validateWithException(validator, licenseUpdateTextDTO);
        Boolean result = authenticationLicenseService.uploadLicense(licenseUpdateTextDTO);
        return respResult(result,
                "上传许可证成功！", "上传许可证失败");
    }

    @ApiOperation(value = "查询有效系统下的授权信息", notes = "仅ROOT用户可以查询")
    @PostMapping("/query")
    public Response getLicenseData(@RequestBody LicenseQueryDTO queryDTO) {
        StringUtils.doTrim(queryDTO);
        Object result = authenticationLicenseService.getLicenseData(queryDTO);
        return respSuccessResult(result, "查询有效系统下的授权信息成功");
    }

//    @GetMapping("verifyLicense")
//    @ApiOperation("校验license")
//    public Response verifyLicense() {
//        if (!authenticationLicenseService.verifyLicense()) {
//            MesErrorCodeException exception = new MesErrorCodeException(BaseErrorCode.LICENSE_ANALYSIS_FAILED.getCode());
//            log.error("{}", exception.getMessage(), exception);
//            throw exception;
//        } else {
//            return respSuccessResult(null, null, "License Valid",
//                    "/verifyLicense",
//                    null,
//                    null);
//        }
//    }
}
