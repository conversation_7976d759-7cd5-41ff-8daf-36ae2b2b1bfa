package cec.jiutian.mes.license.model;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020/5/8
 */
@Data
@Entity
@Table(name = "fd_lcns")
public class AuthenticationLicense implements Serializable {
    @Id
    @Column(name = "LCNS_KY")
    private String licenseKey;

    @Column(name = "LCNS_DATA")
    private String licenseData;

    @Column(name = "LCNS_SGN_DATA")
    private String licenseSignData;

    @Column(name = "LCNS_PBLC_KY")
    private String licensePublicKey;
}
