package cec.jiutian.mes.license.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020/9/27
 */
@Data
@ApiModel(value = "登录信息解密后的DTO", description = "该DTO仅用于登录接口，且作为解密后接受数据的DTO")
public class LoginDTO implements Serializable {
    private static final long serialVersionUID = -5263334419776199784L;
    @ApiModelProperty("用户登录ID")
    @NotBlank
    private String userLoginID;
    @ApiModelProperty("用户登陆密码")
    @NotBlank
    private String userPassword;
    @ApiModelProperty("系统版本号")
    @NotBlank
    private String systemVersion;
    @ApiModelProperty("系统编码，用于区分平台中的不同的系统，同时授权码以不同系统编码进行授权")
    @NotBlank
    private String systemCode;
    @ApiModelProperty("组织ID")
    private String organizationIdentifier;
    @ApiModelProperty("数据域对象ID")
    private String objectCode;
}
