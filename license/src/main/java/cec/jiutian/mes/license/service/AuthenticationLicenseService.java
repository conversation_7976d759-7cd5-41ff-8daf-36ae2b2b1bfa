package cec.jiutian.mes.license.service;

import cec.jiutian.core.base.BaseService;
import cec.jiutian.core.comn.errorCode.BaseErrorCode;
import cec.jiutian.core.comn.util.DateUtils;
import cec.jiutian.core.comn.util.StringUtils;
import cec.jiutian.core.exception.MesErrorCodeException;
import cec.jiutian.mes.license.domain.LicenseInfo;
import cec.jiutian.mes.license.mapper.AuthenticationLicenseMapper;
import cec.jiutian.mes.license.model.AuthenticationLicense;
import cec.jiutian.mes.license.model.AuthenticationLicenseExt;
import cec.jiutian.mes.license.model.LicenseDeleteDTO;
import cec.jiutian.mes.license.model.LicenseQueryDTO;
import cec.jiutian.mes.license.model.LicenseSubmitDTO;
import cec.jiutian.mes.license.model.LicenseUpdateTextDTO;
import cec.jiutian.mes.license.model.LoginDTO;
import cec.jiutian.mes.license.model.PasswordDTO;
import cec.jiutian.mes.license.util.AESUtils;
import cec.jiutian.mes.license.util.Base64Utils;
import cec.jiutian.mes.license.util.DmcUtils;
import cec.jiutian.mes.license.util.RSAUtils;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/5/8
 */
@Slf4j
@Service
@Transactional
public class AuthenticationLicenseService extends BaseService<AuthenticationLicense, String> {
    private static final String SYS_ERROR = "CORE-000018";
    private static final String DATA_FORMAT_ERROR = "CORE-000067";
    private static final String NO_LICENSE = "CORE-000068";
    private static final String LICENSE_ANALYSIS_FAILED = "CORE-000069";
    private static final String LICENSE_VERSION_NOT_MATCH = "CORE-000070";
    private static final String LICENSE_VERIFY_FAILED = "CORE-000071";
    private static final String NOT_ROOT = "CORE-000072";
    private static final String LICENSE_INVALID = "CORE-000073";
    //    private static final String
    private static final String LICENSE_SEPARATOR = "CECJIUTIAN";
    private static final String ERROR_CODE_KEY = "ErrorCode";
    private static final String DAY_ADVANCE_CHECK_LICENSE = "DayAdvanceCheckLicense";
    //    @Autowired
//    private AuthenticationEmployeeInformationMapper authenticationEmployeeInformationMapper;
    @Autowired
    private AuthenticationLicenseMapper authenticationLicenseMapper;

    /**
     * 计算注册人数是否大于许可证的最大注册人数
     */
    /*
    public void registerNumberCount(int newCount) {
        String uuid = DmcUtils.getUUID();
        AuthenticationLicense authenticationLicense = authenticationLicenseService.getById(uuid);
        if (authenticationLicense == null) {
            MesErrorCodeException exception = new MesErrorCodeException(BaseErrorCode.NO_LICENSE.getCode());
            log.error("{}", exception.getMessage(), exception);
            throw exception;
        }
        try {
            byte[] encodeDate = Base64Utils.decode(authenticationLicense.getLicenseData());
            String[] licenseInfo = new String(RSAUtils.decryptByPublicKey(encodeDate,authenticationLicense.getLicensePublicKey())).split("\\+");
            int count = authenticationEmployeeInformationMapper.getLoginUserCount();
            if (count + newCount > Integer.parseInt(licenseInfo[2])) {
                MesErrorCodeException exception = new MesErrorCodeException(BaseErrorCode.REGISTER_NUMBER_FULL.getCode())
                        .addMsgItem(licenseInfo[2]);
                log.error("{}", exception.getMessage(), exception);
                throw exception;
            }
        } catch (Exception e) {
            MesErrorCodeException exception = new MesErrorCodeException(e,BaseErrorCode.LICENSE_ANALYSIS_FAILED.getCode());
            log.error("{}", exception.getMessage(), exception);
            throw exception;
        }
    }
     */
    public static int getDayDiffer(Date startDate, Date endDate) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        try {
            long startDateTime = dateFormat.parse(dateFormat.format(startDate)).getTime();
            long endDateTime = dateFormat.parse(dateFormat.format(endDate)).getTime();
            return (int) ((endDateTime - startDateTime) / (1000 * 3600 * 24));
        } catch (Exception e) {
            MesErrorCodeException exception = new MesErrorCodeException(e, DATA_FORMAT_ERROR);
            log.error("{}", exception.getMessage(), exception);
            throw exception;
        }
    }

    public Boolean verifyLicense(String systemCode, String systemVersion) throws Exception {
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = Objects.requireNonNull(servletRequestAttributes).getRequest();
        List<AuthenticationLicense> authenticationLicenseList = authenticationLicenseMapper.getBySystem(systemCode, systemVersion);
        String uuid = DmcUtils.getUUID();
        if (CollectionUtils.isEmpty(authenticationLicenseList)) {
            request.setAttribute(ERROR_CODE_KEY, NO_LICENSE);
            return true;
        }
        AuthenticationLicense authenticationLicense = null;
        for (AuthenticationLicense item :
                authenticationLicenseList) {
            byte[] encodeDate;
            String[] licenseInfo;

            encodeDate = Base64Utils.decode(item.getLicenseData());
            licenseInfo = new String(RSAUtils.decryptByPublicKey(encodeDate, item.getLicensePublicKey())).split("\\+");
            if (uuid.equals(licenseInfo[0])) {
                authenticationLicense = item;
                break;
            } else {
                continue;
            }
        }
        if (authenticationLicense == null) {
            request.setAttribute(ERROR_CODE_KEY, NO_LICENSE);
            return true;
        }
        byte[] encodeDate;
        String[] licenseInfo;

        try {
            encodeDate = Base64Utils.decode(authenticationLicense.getLicenseData());
            licenseInfo = new String(RSAUtils.decryptByPublicKey(encodeDate, authenticationLicense.getLicensePublicKey())).split("\\+");
        } catch (Exception e) {
            request.setAttribute(ERROR_CODE_KEY, LICENSE_ANALYSIS_FAILED);
            return true;
        }
        String licenseSystemCode = licenseInfo[2];
        String licenseSystemVersion = licenseInfo[3];
        if (StringUtils.isNotBlank(licenseSystemCode)) {
            if (!StringUtils.equals(licenseSystemCode, systemCode)) {
                request.setAttribute(ERROR_CODE_KEY, NO_LICENSE);
                return true;
            }
            if (!StringUtils.equals(licenseSystemVersion, systemVersion)) {
                request.setAttribute(ERROR_CODE_KEY, LICENSE_VERSION_NOT_MATCH);
                return true;
            }
        }
        Date expireTime = DateUtils.parseStringToDate(licenseInfo[1]);
        Date now = new Date();
        if (now.after(expireTime)) {
            request.setAttribute(ERROR_CODE_KEY, BaseErrorCode.LICENSE_EXPIRED.getCode());
            return true;
        }
        int dayDiffer = getDayDiffer(now, expireTime);
        request.setAttribute("validUntil", dayDiffer);
        try {
            if (!RSAUtils.verify(encodeDate, authenticationLicense.getLicensePublicKey(), authenticationLicense.getLicenseSignData())) {
                request.setAttribute(ERROR_CODE_KEY, LICENSE_VERIFY_FAILED);
                return true;
            }
        } catch (Exception e) {
            request.setAttribute(ERROR_CODE_KEY, LICENSE_ANALYSIS_FAILED);
            return true;
        }
        return true;
    }

    public Object uploadLicense(MultipartFile[] file, String login) {
        checkRoot(login, true);
        if (file.length > 0) {
            List<String> licenses = new ArrayList<>();
            try {
                for (MultipartFile multipartFile : file) {
                    if (!multipartFile.isEmpty() && multipartFile.getSize() > 0) {
                        licenses.add(new String(multipartFile.getBytes()));
                    }
                }
            } catch (Exception e) {
                MesErrorCodeException exception = new MesErrorCodeException(e, LICENSE_ANALYSIS_FAILED);
                log.error("{}", exception.getMessage(), exception);
                throw exception;
            }
            List<AuthenticationLicenseExt> authenticationLicenseList = licenses.stream().map(LicenseInfo::new).peek(x -> {
                try {
                    x.setDecryptData();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }).map(LicenseInfo::getEntity).peek(item -> {
                String licenseSystemCode = item.getDecryptSystemCode();
                String flag = authenticationLicenseMapper.getSystemValidFlag(licenseSystemCode);
                if (StringUtils.isBlank(flag) || "N".equals(flag)) {
                    item.setValid(false);
                    StringBuffer reasonText = new StringBuffer();
                    reasonText.append(item.getReasonText());
                    reasonText.append("授权的系统代码不是有效系统。");
                    item.setReasonText(reasonText.toString());
                }
            }).collect(Collectors.toList());

            return authenticationLicenseList;
        } else {
            return new ArrayList<>();
        }
    }

    private void checkRoot(String login) {
        checkRoot(login, false);
    }

    private void checkRoot(String login, Boolean isSecond) {
        LoginDTO loginDTO;
        try {
            if (BooleanUtils.isTrue(isSecond)) {
                loginDTO = AESUtils.serverDecrypt(login);
            } else {
                loginDTO = AESUtils.decrypt(login);
            }

        } catch (Exception e) {
            MesErrorCodeException exception = new MesErrorCodeException(e, BaseErrorCode.JSON_TO_ENTITY_FAIL.getCode());
            log.error("{}", exception.getMessage(), exception.getCause());
            throw exception;
        }
        if (!StringUtils.equals(loginDTO.getUserLoginID(), "ROOT")) {
            MesErrorCodeException exception = new MesErrorCodeException(NOT_ROOT);
            log.error("{}", exception.getMessage(), exception.getCause());
            throw exception;
        }
        PasswordDTO passwordDTO = authenticationLicenseMapper.getAdminPassword();
        if (passwordDTO == null || !StringUtils.equals(DigestUtils.md5Hex(loginDTO.getUserPassword() + passwordDTO.getPasswordDigest()), passwordDTO.getPassword())) {
            MesErrorCodeException exception = new MesErrorCodeException(BaseErrorCode.USER_PASSWORD_ERROR.getCode());
            log.error("{}", exception.getMessage(), exception.getCause());
            throw exception;
        }
    }

    public Boolean uploadLicense(LicenseUpdateTextDTO licenseUpdateTextDTO) {
        checkRoot(licenseUpdateTextDTO.getLogin());
        return updateLicense(licenseUpdateTextDTO.getLicenses());
    }

    private Boolean updateLicense(List<String> licenses) {
        if (CollectionUtils.isNotEmpty(licenses)) {
            for (String license : licenses) {
                String[] licenseInfo;
                String[] decrypt;
                try {
                    licenseInfo = license.split(LICENSE_SEPARATOR);
                    decrypt = new String(RSAUtils.decryptByPublicKey(Base64Utils.decode(licenseInfo[0]), licenseInfo[2])).split("\\+");
                } catch (Exception e) {
                    MesErrorCodeException exception = new MesErrorCodeException(e, LICENSE_ANALYSIS_FAILED);
                    log.error("{}", exception.getMessage(), exception);
                    throw exception;
                }
                Date expireTime = DateUtils.parseStringToDate(decrypt[1]);
                if (expireTime.before(new Date())) {
                    MesErrorCodeException exception = new MesErrorCodeException(LICENSE_INVALID);
                    log.error("{}", exception.getMessage(), exception.getCause());
                    throw exception;
                }
                String licenseSystemCode = decrypt[2];
                String flag = authenticationLicenseMapper.getSystemValidFlag(licenseSystemCode);
                if (flag.equals("N")) {
                    MesErrorCodeException exception = new MesErrorCodeException(SYS_ERROR);
                    log.error("{}", exception.getMessage(), exception);
                    throw exception;
                }
                String licenseSystemVersion = decrypt[3];
                String cpuId = decrypt[0];
                String licenseKey = cpuId + ":" + licenseSystemCode;
                StringBuilder licenseKeys = new StringBuilder();
                licenseKeys.append(licenseKey);
                String systemLicense = authenticationLicenseMapper.getSystemLicense(licenseSystemCode);
                if (StringUtils.isNotBlank(systemLicense) && !systemLicense.contains(licenseKey)) {
                    licenseKeys.append(",").append(systemLicense);
                }
                if (StringUtils.isNotBlank(licenseSystemCode)) {
                    authenticationLicenseMapper.updateLicense(licenseKeys.toString(), licenseSystemCode, licenseSystemVersion);
                }
                AuthenticationLicense authenticationLicense = new AuthenticationLicense();
                authenticationLicense.setLicenseData(licenseInfo[0]);
                authenticationLicense.setLicenseSignData(licenseInfo[1]);
                authenticationLicense.setLicensePublicKey(licenseInfo[2]);
                authenticationLicense.setLicenseKey(licenseKey);
                AuthenticationLicense check = getById(licenseKey);
                if (check != null) {
                    authenticationLicenseMapper.updateByPrimaryKey(authenticationLicense);
                } else {
                    authenticationLicenseMapper.insert(authenticationLicense);
                }
            }
            return true;
        } else {
            return false;
        }
    }

    public Object getLicenseData(LicenseQueryDTO queryDTO) {
        checkRoot(queryDTO.getLogin(), true);
        int pageSize = queryDTO.getPageSize();
        startPage(queryDTO.getPageNum(), pageSize);
        List<AuthenticationLicenseExt> authenticationLicenseList = authenticationLicenseMapper.getLicenseData();
        authenticationLicenseList = authenticationLicenseList.stream().map(LicenseInfo::new).peek(x -> {
            try {
                x.setDecryptData();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }).map(LicenseInfo::getEntity).collect(Collectors.toList());
        PageInfo<AuthenticationLicenseExt> result = new PageInfo<>(authenticationLicenseList);
        return pageSize == 0 ? result.getList() : result;
    }

    public Boolean submitLicense(LicenseSubmitDTO licenseSubmitDTO) {
        checkRoot(licenseSubmitDTO.getLogin(), true);
        for (AuthenticationLicenseExt authenticationLicenseExt : licenseSubmitDTO.getAuthenticationLicenseExtList()) {
            String licenseSystemCode = authenticationLicenseExt.getDecryptSystemCode();
            String licenseSystemVersion = authenticationLicenseExt.getDecryptSystemVersion();
            String licenseKey = authenticationLicenseExt.getLicenseKey();
            StringBuilder licenseKeys = new StringBuilder();

            if (StringUtils.isNotBlank(licenseSystemCode)) {
                String systemLicense = authenticationLicenseMapper.getSystemLicense(licenseSystemCode);
                if (StringUtils.isNotBlank(systemLicense)) {
                    licenseKeys.append(systemLicense);
                    if(!systemLicense.contains(licenseKey)){
                        licenseKeys.append(",").append(licenseKey);
                    }
                } else {
                    licenseKeys.append(licenseKey);
                }
                authenticationLicenseMapper.updateLicense(licenseKeys.toString(), licenseSystemCode, licenseSystemVersion);
            }
            AuthenticationLicense authenticationLicense = new AuthenticationLicense();
            authenticationLicense.setLicenseData(authenticationLicenseExt.getLicenseData());
            authenticationLicense.setLicenseSignData(authenticationLicenseExt.getLicenseSignData());
            authenticationLicense.setLicensePublicKey(authenticationLicenseExt.getLicensePublicKey());
            authenticationLicense.setLicenseKey(licenseKey);
            AuthenticationLicense check = getById(licenseKey);
            if (check != null) {
                authenticationLicenseMapper.updateByPrimaryKey(authenticationLicense);
            } else {
                authenticationLicenseMapper.insert(authenticationLicense);
            }
        }
        return true;
    }

    public Boolean deleteLicense(LicenseDeleteDTO licenseDeleteDTO) {
        checkRoot(licenseDeleteDTO.getLogin(), true);
        AuthenticationLicenseExt authenticationLicenseExt = licenseDeleteDTO.getAuthenticationLicenseExt();

        if (authenticationLicenseExt == null) {
            return false;
        }

        String licenseSystemCode = StringUtils.isNotBlank(authenticationLicenseExt.getDecryptSystemCode()) ? authenticationLicenseExt.getDecryptSystemCode() : authenticationLicenseExt.getSystemCode();
        String licenseSystemVersion = StringUtils.isNotBlank(authenticationLicenseExt.getDecryptSystemVersion()) ? authenticationLicenseExt.getDecryptSystemVersion() : authenticationLicenseExt.getSystemVersion();
        String licenseKey = StringUtils.isNotBlank(authenticationLicenseExt.getLicenseKey()) ? authenticationLicenseExt.getLicenseKey() : authenticationLicenseExt.getLicense();

        StringBuilder licenseKeys = new StringBuilder();

        if (StringUtils.isNotBlank(licenseSystemCode)) {
            String systemLicense = authenticationLicenseMapper.getSystemLicense(licenseSystemCode);
            if (StringUtils.isNotBlank(systemLicense) && systemLicense.contains(licenseKey)) {
                String[] licenses = systemLicense.split(",");
                if (licenses.length > 0) {
                    for (String key : licenses) {
                        if (!StringUtils.equals(key, licenseKey)) {
                            if (StringUtils.isBlank(licenseKeys.toString())) {
                                licenseKeys.append(key);
                            } else {
                                licenseKeys.append(",").append(key);
                            }
                        }
                    }
                }

                authenticationLicenseMapper.updateLicense(licenseKeys.toString(), licenseSystemCode, licenseSystemVersion);
            }
        }
        if (StringUtils.isNotBlank(licenseKey)) {
            AuthenticationLicense authenticationLicense = new AuthenticationLicense();
            authenticationLicense.setLicenseData(authenticationLicenseExt.getLicenseData());
            authenticationLicense.setLicenseSignData(authenticationLicenseExt.getLicenseSignData());
            authenticationLicense.setLicensePublicKey(authenticationLicenseExt.getLicensePublicKey());
            authenticationLicense.setLicenseKey(licenseKey);
            AuthenticationLicense check = getById(licenseKey);
            if (check != null) {
                authenticationLicenseMapper.deleteByPrimaryKey(authenticationLicense);
            }
        }
        return true;
    }

    public void checkLicenseTask() {
        List<AuthenticationLicenseExt> authenticationLicenseList = authenticationLicenseMapper.getLicenseData();
        authenticationLicenseList = authenticationLicenseList.stream().map(LicenseInfo::new).peek(x -> {
            try {
                x.setDecryptData();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }).map(LicenseInfo::getEntity).collect(Collectors.toList());

        Integer daysAdvance = 10;
        String days = authenticationLicenseMapper.getSystemConfiguration(DAY_ADVANCE_CHECK_LICENSE);
        if (StringUtils.isNotBlank(days)) {
            daysAdvance = Integer.valueOf(days);
        }

        for (AuthenticationLicenseExt authenticationLicenseExt : authenticationLicenseList) {
            if (BooleanUtils.isFalse(authenticationLicenseExt.getValid())) {
                //TODO 发送信息给APM/ECS

            }

            if (authenticationLicenseExt.getRemainDay() > 0 && authenticationLicenseExt.getRemainDay() < daysAdvance) {
                //TODO 发送信息给APM/ECS
            }
        }
    }
}
