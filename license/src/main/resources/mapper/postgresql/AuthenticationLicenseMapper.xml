<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cec.jiutian.mes.license.mapper.AuthenticationLicenseMapper">
    <resultMap id="AuthenticationLicense" type="cec.jiutian.mes.license.model.AuthenticationLicense">
        <id property="licenseKey" jdbcType="VARCHAR" column="LCNS_KY"/>
        <result property="licenseData" jdbcType="VARCHAR" column="LCNS_DATA"/>
        <result property="licenseSignData" jdbcType="VARCHAR" column="LCNS_SGN_DATA"/>
        <result property="licensePublicKey" jdbcType="VARCHAR" column="LCNS_PBLC_KY"/>
    </resultMap>

    <resultMap id="AuthenticationLicenseExt" type="cec.jiutian.mes.license.model.AuthenticationLicenseExt" extends="AuthenticationLicense">
    </resultMap>

    <select id="getBySystem" resultMap="AuthenticationLicense">
        select AL.LCNS_KY,
               AL.LCNS_DATA,
               AL.LCNS_PBLC_KY,
               AL.LCNS_SGN_DATA
        from fd_lcns AL
                 inner join fd_systm_cmpnt SC on AL.lcns_ky = any (string_to_array(sc.systm_lcns, ','))
        where SC.SYSTM_CD = #{systemCode}
          and SC.SYSTM_VRSN = #{systemVersion}
    </select>


    <select id="getLicenseData" resultMap="AuthenticationLicenseExt">
        select sc.*, l.*
        from (select systm_cd                               as systemCode,
                     systm_nm                               as systemName,
                     systm_vrsn                             as systemVersion,
                     regexp_split_to_table(systm_lcns, ',') as license,
                     systm_lcns                             as systemLicense
              from fd_systm_cmpnt
              where vld_flg = 'Y') sc
                 left join fd_lcns l on l.lcns_ky = sc.license
    </select>
</mapper>
