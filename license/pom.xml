<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>cec.jiutian</groupId>
		<artifactId>fabos-security</artifactId>
		<version>3.2.0</version>
	</parent>
	<artifactId>license</artifactId>

	<properties>
		<java.version>17</java.version>
	</properties>

	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>cec.jiutian</groupId>
			<artifactId>security-core-comn</artifactId>
			<version>3.2.0</version>
		</dependency>
		<dependency>
			<groupId>cec.jiutian</groupId>
			<artifactId>security-core-service</artifactId>
			<version>3.2.0</version>
		</dependency>
		<dependency>
			<groupId>cec.jiutian</groupId>
			<artifactId>xxljob-spring-boot-starter</artifactId>
			<version>1.0-RELEASE</version>
			<scope>provided</scope>
		</dependency>
	</dependencies>
</project>
