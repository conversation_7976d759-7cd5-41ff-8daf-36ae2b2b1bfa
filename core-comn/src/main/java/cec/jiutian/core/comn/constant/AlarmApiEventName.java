package cec.jiutian.core.comn.constant;

public enum AlarmApiEventName {
    // 预警用户组绑定配置
    ALARM_ITEM_BOND("Alarm_Item_Bond"),
    // 预警用户组解绑配置
    ALARM_ITEM_UNBOND("Alarm_Item_UnBond"),
    // 预警通知配置
    ALARM_ITEM_CONFIG_UPDATE("Update_Config"),
    // // 预警消息失效
    ALARM_MESSAGE_INVALID("Alarm_Message_Invalid"),
    // 预警用户组绑定用户
    ALARM_USER_BOND("Alarm_User_Bond"),
    // 预警用户组解绑用户
    ALARM_USER_UNBOND("Alarm_User_UnBond"),

    ALARM_INSERT("Insert"),

    ALARM_DELETE("Delete"),

    ALARM_CREATE("Create"),

    ALARM_UPDATE("Update"),

    ALARM_SAVEHOST("SaveHost"),

    ALARM_DELETEHOST("DeleteHost"),

    ALARM_REFRESH("Refresh"),

    ALARM_SAVE("Save"),

    ALARM_RESEND("Resend"),

    ALARM_SEND("Send"),

    DELETE_ALARM_USER_MONITOR_ITEM("del_alrm_usr_grp_mntr_itm"),

    CHANGE_ALARM_STATE("Change_Alarm_State"),
    ;

    private String name;

    AlarmApiEventName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }
}
