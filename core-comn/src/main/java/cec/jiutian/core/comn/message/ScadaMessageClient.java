package cec.jiutian.core.comn.message;

/**
 * <AUTHOR> Y
 * @version 1.0
 * @date 2020/3/2 12:21 PM
 */
public interface ScadaMessageClient {
    String OUTPUT = "outputScada";
    String INPUT = "inputStreamSets";
    String INPUTSCADA = "inputScadaMessage";
    String INPUT_EQPMNT_OVRL = "inputEquipmentOverall";
    String INPUT_ELCTRC_ION = "inputElectrostaticIon";
    String INPUT_SCADA_AI = "inputScadaAI";
    String INPUT_SCADA_DI = "inputScadaDI";
    String INPUT_SCADA_VT = "inputScadaVT";

 /*   @Input(ScadaMessageClient.INPUT_SCADA_AI)
    SubscribableChannel inputScadaAI();

    @Input(ScadaMessageClient.INPUT_SCADA_DI)
    SubscribableChannel inputScadaDI();

    @Input(ScadaMessageClient.INPUT_SCADA_VT)
    SubscribableChannel inputScadaVT();

    @Input(ScadaMessageClient.INPUT)
    SubscribableChannel input();

    @Output(ScadaMessageClient.OUTPUT)
    MessageChannel output();

    @Input(ScadaMessageClient.INPUTSCADA)
    SubscribableChannel inputScadaMessage();

    @Input(ScadaMessageClient.INPUT_EQPMNT_OVRL)
    SubscribableChannel inputEquipmentOverall();

    @Input(ScadaMessageClient.INPUT_ELCTRC_ION)
    SubscribableChannel inputElectrostaticIon();*/
}
