package cec.jiutian.core.comn.util;

import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import java.util.Date;

/**
 * <AUTHOR> Y
 * @version 1.0
 * @date 2019/6/20 10:37 AM
 */
public class DateUtils {

    public static String parseDateToString(Date date) {
        DateTime dateTime = new DateTime(date);
        String formatStr = "yyyy-MM-dd HH:mm:ss";
        return dateTime.toString(formatStr);
    }

    public static Date parseStringToDate(String strDate) {
        DateTimeFormatter dateTimeFormat = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss");
        DateTime dateTime = DateTime.parse(strDate, dateTimeFormat);
        dateTime = dateTime.plusDays(1);
        return dateTime.toDate();
    }
}
