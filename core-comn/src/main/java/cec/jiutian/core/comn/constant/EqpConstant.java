package cec.jiutian.core.comn.constant;

public interface EqpConstant {
    String DATE_FORMAT_YMDHMS = "yyyy-MM-dd HH:mm:ss";
    String DATE_FORMAT_YMD = "yyyy-MM-dd";
    String DATE_FORMAT_YMDHMSS = "yyyyMMddHHmmssSSS";
    String STATE_IDLE = "Idle";
    String EVENT_TYPE_ADD = "Idle";
    String EVENT_TYPE_DELETE = "Delete";

    String REPAIRT_STATE_CREATED = "Created";//创建
    @Deprecated
    String REPAIRT_STATE_TOPICKUP = "ToPickUp";//待接单
    @Deprecated
    String REPAIRT_STATE_TOREPAIR = "ToRepair";//待维修
    @Deprecated
    String REPAIRT_STATE_REPAIRING = "Repairing";//维修中
    @Deprecated
    String REPAIRT_STATE_REPAIRCOMPLETED = "RepairCompleted";//维修完成
    @Deprecated
    String REPAIRT_STATE_CLOSED = "Closed";//已关闭
    /**
     * 保修记录状态转换
     */
    //审核
    @Deprecated
    String REPAIRT_STATE_CHANGE_CREATED_TOPICKUP = "CreatedToPickUp";
    //接单
    @Deprecated
    String REPAIRT_STATE_CHANGE_TOPICKUP_TOREPAIR = "ToPickUpToRepair";
    //开始维修
    @Deprecated
    String REPAIRT_STATE_CHANGE_TOREPAIR_REPAIRING = "ToRepairRepairing";
    //结束维修
    @Deprecated
    String REPAIRT_STATE_CHANGE_REPAIRING_REPAIRCOMPLETED = "RepairingRepairCompleted";
    //隐患验证
    @Deprecated
    String REPAIRT_STATE_CHANGE_REPAIRCOMPLETED_CLOSED = "RepairCompletedClosed";

    String YESORNO_YES = "Y";
    String YESORNO_NO = "N";

    String PART_REQUEST_NAME_RULE = "PmsPartRequestCode";

    String PART_REQUEST_TYPE_RECEIVE = "Receive";
    String PART_REQUEST_TYPE_RETURN = "Return";

    String PART_REQUEST_STATE_VALID = "ToBeValidated";
    String PART_REQUEST_STATE_SHIP = "ToBeShipped";
    String PART_REQUEST_STATE_COMP = "Completed";
    String PART_REQUEST_STATE_CANCEL = "Cancelled";

    String CBM = "CBM";
    String TBM = "TBM";

    String PMS_SPOT_CHECK_STATE_CREATED = "Created";
    String PMS_SPOT_CHECK_STATE_RELEASED = "Released";
    String PMS_SPOT_CHECK_STATE_TO_BE_VALIDATED = "ToBeValidated";

    /**
     * 2.5.4版本中已经没有该状态
     */
    @Deprecated
    String PMS_SPOT_CHECK_STATE_ASSIGNED = "Assigned";

    String PMS_SPOT_CHECK_STATE_COMPLETED = "Completed";

    String PMS_MNTN_STATE_CREATED = "Created";
    String PMS_MNTN_STATE_TOBEVALIDATED = "ToBeValidated";
    @Deprecated
    String PMS_MNTN_STATE_INPROGRESS = "InProgress";
    String PMS_MNTN_STATE_COMPLETED = "Completed";

    String PMS_MNTN_TSK_MERGE = "PMSMaintainTaskMerge";

    String MACHINE_DETAIL_TYPE_MAIN = "主设备";

    String PLAN_FREQUENCY_CODE_EVERYDAY = "Daily";
    String PLAN_FREQUENCY_CODE_WEEK = "Weekly";
    String PLAN_FREQUENCY_CODE_MONTH = "Monthly";
    String PLAN_FREQUENCY_CODE_QUARTER = "Quarterly";
    String PLAN_FREQUENCY_CODE_YEAR = "Yearly";

    String CHECK_PLAN_NAME_RULE = "PmsSpotCheckPlanCode";
    String REPAIR_TASK_NAME_RULE = "PmsRepairTaskCode";

    String REPAIR_RESULT_SUCCESS = "Success";
    String REPAIR_RESULT_FAILURE = "Failure";
    String REPAIR_RESULT_RE_REPAIR = "Re-repair";

    String MACHINE_STATE_NORMAL = "Normal";
    String MACHINE_STATE_DISABLED = "Disabled";

    String VALID_FLAG_Y = "Y";
    String VALID_FLAG_N = "N";
}
