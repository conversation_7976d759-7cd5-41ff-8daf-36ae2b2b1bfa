package cec.jiutian.core.comn.constant;

/**
 * <AUTHOR>
 * @date 2019/12/16
 * @description
 */
public enum FactoryApiEventName {
    // factory
    FACTORY_CREATE("Factory_Create"),
    FACTORY_UPDATE("Factory_Update"),
    FACTORY_DELETE("Factory_Delete"),

    // area
    AREA_CREATE("Area_Create"),
    AREA_UPDATE("Area_Update"),
    AREA_DELETE("Area_Delete"),

    // operation
    FLOW_OP_DEFN_CREATE("Flow_Op_Defn_Create"),
    FLOW_OP_DEFN_UPDATE("Flow_Op_Defn_Update"),
    FLOW_OP_DEFN_DELETE("Flow_Op_Defn_Delete"),

    // process
    FLOW_PROC_DEFN_CREATE("Flow_Proc_Defn_Create"),
    FLOW_PROC_DEFN_UPDATE("Flow_Proc_Defn_Update"),
    FLOW_PROC_DEFN_DELETE("Flow_Proc_Defn_Delete"),

    // mode
    MODE_CREATE("Mode_Create"),
    MODE_UPDATE("Mode_Update"),
    MODE_DELETE("Mode_Delete"),

    // modeModel
    MODE_MODEL_CREATE("ModeModel_Create"),
    MODE_MODEL_UPDATE("ModeModel_Update"),
    MODE_MODEL_DELETE("ModeModel_Delete"),

    // modeTransformationalRule
    CREATE_MODE_TRANSFORMATIONAL_RULE("Create_Mode_Transformational_Rule"),
    UPDATE_MODE_TRANSFORMATIONAL_RULE("Update_Mode_Transformational_Rule"),
    DELETE_MODE_TRANSFORMATIONAL_RULE("Delete_Mode_Transformational_Rule"),

    // productSpec
    CREATE_PRODUCT_SPEC("Create_Product_Spec"),
    UPDATE_PRODUCT_SPEC("Update_Product_Spec"),
    DELETE_PRODUCT_SPEC("Delete_Product_Spec"),

    // reasonCode
    REASONCODE_CREATE("ReasonCode_Create"),
    REASONCODE_UPDATE("ReasonCode_Update"),
    REASONCODE_DELETE("ReasonCode_Delete"),

    // reasonCodeType
    REASONCODETYPE_CREATE("ReasonCodeType_Create"),
    REASONCODETYPE_UPDATE("ReasonCodeType_Update"),
    REASONCODETYPE_DELETE("ReasonCodeType_Delete"),

    EMPLOYEE_SHIFT_CREATE("Employee_Shift_Create"),
    EMPLOYEE_SHIFT_UPDATE("Employee_Shift_Update"),
    EMPLOYEE_SHIFT_DELETE("Employee_Shift_Delete"),

    ;
    private String name;

    FactoryApiEventName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }
}
