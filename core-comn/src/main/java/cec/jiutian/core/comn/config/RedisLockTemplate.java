package cec.jiutian.core.comn.config;

import cec.jiutian.core.comn.util.RedisLock;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

@Configuration
public class RedisLockTemplate {

    private static JedisPool pool = null;

    @Bean
    public RedisLock getLock() {
        JedisPoolConfig config = new JedisPoolConfig();
        // 设置最大连接数
        config.setMaxTotal(500);
        // 设置最大空闲数
        config.setMaxIdle(8);
        // 设置最大等待时间,单位毫秒
        config.setMaxWaitMillis(180 * 1000);
        config.setTestOnBorrow(true);
        pool = new JedisPool(config, "220.249.91.187", 2301, 3000);
        return new RedisLock(pool);
    }
}
