package cec.jiutian.core.comn.message;

public interface AuthenticationDepartmentStreamClient {

    String INPUT = "inputAuthenticationDepartment";

    String OUTPUT = "outputAuthenticationDepartment";

    String OUTPUTCUST = "outputCustomerMessage";

/*    @Input(AuthenticationDepartmentStreamClient.INPUT)
    SubscribableChannel input();

    @Output(AuthenticationDepartmentStreamClient.OUTPUT)
    MessageChannel output();

    @Output(AuthenticationDepartmentStreamClient.OUTPUTCUST)
    MessageChannel outputCust();*/
}
