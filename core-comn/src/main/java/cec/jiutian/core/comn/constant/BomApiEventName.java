package cec.jiutian.core.comn.constant;

/**
 * <AUTHOR>
 * @date 2019/12/16
 * @description Bom服务最后时间名称
 */
public enum BomApiEventName {
    // materialProcessConsumptionDefinition
    CREATE_MTRL_PRCS_CNSMPN_DFNTN("Create_Mtrl_Prcs_Cnsmpn_Dfntn"),
    UPDATE_MTRL_PRCS_CNSMPN_DFNTN("Update_Mtrl_Prcs_Cnsmpn_Dfntn"),
    DELETE_MTRL_PRCS_CNSMPN_DFNTN("Delete_Mtrl_Prcs_Cnsmpn_Dfntn"),

    // productionProcessProperty
    CREATE_PRODUCTION_PROCESS_PRPRTY("Create_Production_Process_Prprty"),
    UPDATE_PRODUCTION_PROCESS_PRPRTY("Update_Production_Process_Prprty"),
    DELETE_PRODUCTION_PROCESS_PRPRTY("Delete_Production_Process_Prprty"),

    // materialSpecification
    MM_SPEC_CRE("Mm_Spec_Cre"),
    MM_SPEC_UPD("Mm_Spec_Upd"),
    MM_SPEC_DEL("Mm_Spec_Del"),

    // materialSpecificationProperty
    CREATE_MM_SPEC_PROP("Create_Mm_Spec_Prop"),
    UPDATE_MM_SPEC_PROP("Update_Mm_Spec_Prop"),
    DELETE_MM_SPEC_PROP("Delete_Mm_Spec_Prop"),

    // material
    CREATE_MATERIAL("Create_Material"),
    DELETE_MATERIAL("Delete_Material"),
    UPDATE_MATERIAL_STATE("Update_Material_State"),
    UPDATE_MATERIAL_PROPERTY("Update_Material_Property"),
    UPDATE_MATERIALSTATE_IS_ONHOLD("Update_MaterialState_Is_OnHold"),
    UPDATE_MATERIALSTATE_IS_NOTONHOLD("Update_MaterialState_Is_NotOnHold"),
    UPDATE_MATERIALSTATE_IS_INUSE("Update_MaterialState_Is_InUse"),
    UPDATE_MATERIALSTATE_IS_WAIT("Update_MaterialState_Is_wait"),

    // productionProcessPropertyRumTime
    CREATE_PRODUCTION_PROCESS_PROPERTY_RUM_TIME("Create_Property_Rum_Time"),
    UPDATE_PRODUCTION_PROCESS_PROPERTY_RUM_TIME("Update_Property_Rum_Time"),
    DELETE_PRODUCTION_PROCESS_PROPERTY_RUM_TIME("Delete_Property_Rum_Time"),

    ;
    private String name;

    BomApiEventName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }
}
