package cec.jiutian.core.comn.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

@Component
public class BaseRedisService {

    @Autowired
    private StringRedisTemplate redisTemplate;

    public void setString(String key, Object data, Long timeout) {
        if (data instanceof String) {
            String value = (String) data;
            redisTemplate.opsForValue().set(key, value);
        }
        if (null != timeout) {
            redisTemplate.expire(key, timeout, TimeUnit.SECONDS);
        }
    }

    public void setString(String key, Object data) {
        if (data instanceof String) {
            String value = (String) data;
            redisTemplate.opsForValue().set(key, value);
        }
    }

    public Object getString(String key) {
        return redisTemplate.opsForValue().get(key);
    }

    public void delKey(String key) {
        redisTemplate.delete(key);
    }
}
