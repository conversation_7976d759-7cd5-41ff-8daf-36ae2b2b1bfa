package cec.jiutian.core.comn.util;

import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;

/**
 *  * @description: 数据值类型判断  * @copyright: Copyright (c) 2019  * @company: 中电九天
 *    * @version: 1.0  * @date: 2019 2019年5月31日 上午9:25:57
 */
public class ValTypCheckUtils {

    /**
     * 定义数据类型
     */
    public static final String DOUBLE_TYP = "Double";
    public static final String INTEGER_TYP = "Integer";
    public static final String BOOLEAN_TYP = "Boolean";
    public static final String STRING_TYP = "String";
    public static final String ERROR_TYP = "Error";

    private static final Set<String> valTypSet = new HashSet<String>() {
        private static final long serialVersionUID = 1L;

        {
            add("string");
            add("int");
            add("float");
            add("double");
            add("bool");
        }
    };

    private static final Map<String, String> valTypMap = new HashMap<String, String>() {
        private static final long serialVersionUID = -3723211177985101555L;

        {
            put("string", STRING_TYP);
            put("int", INTEGER_TYP);
            put("float", DOUBLE_TYP);
            put("double", DOUBLE_TYP);
            put("bool", BOOLEAN_TYP);
        }
    };

    /**
     *  * @description: 判断是否在已有 类型范围内，如果在返回true    * @version:
     * 1.0  * @date: 2019 2019年5月31日 上午9:34:05  * @param typ  * @return
     */
    public static boolean containValTyp(String typ) {
        return valTypSet.contains(StringUtils.lowerCase(typ));
    }

    /**
     *  * @description: 根据ENUM表中的数据类型返回相应数据类型    * @version:
     * 1.0  * @date: 2019 2019年5月31日 上午9:40:38  * @param typ  * @return
     */
    public static String enum2StdTyp(String typ) {
        return valTypMap.get(StringUtils.lowerCase(typ));
    }

    /**
     *  * @description: 校验数据类型与所提供的数据类型是否匹配
     *  
     *  * @version: 1.0
     *  * @date: 2019 2019年5月31日 上午10:39:36
     *  * @param typ
     *  * @param val
     *  * @return
     */
    public static boolean checkValTyp(String typ, String val) {
        if (!containValTyp(typ)) {
            return false;
        }
        String stdTyp = enum2StdTyp(typ);

        return StringUtils.equals(stdTyp, getValTyp(val));
    }

    /**
     *  * @description: 获取数据的类型
     *  
     *  * @version: 1.0
     *  * @date: 2019 2019年5月31日 上午10:40:01
     *  * @param val
     *  * @return
     */
    public static String getValTyp(String val) {
        if (isInteger(val)) {
            return INTEGER_TYP;
        } else if (isDoubleOrFloat(val)) {
            return DOUBLE_TYP;
        } else if (isBoolean(val)) {
            return BOOLEAN_TYP;
        } else if (StringUtils.isNotEmpty(val)) {
            return STRING_TYP;
        } else {
            return ERROR_TYP;
        }
    }

    /**
     *  * @description: 判断字符串是否为double或者float类型，可以为全整数 ，如果是则返回true
     *    * @version: 1.0  * @date: 2019 2019年5月31日 上午10:24:59
     *  * @param str  * @return
     */
    public static boolean isDoubleOrFloat(String str) {
        Pattern pattern = Pattern.compile("((^[1-9]([0-9]*))|0)(.[0-9]+)?");
        return pattern.matcher(str).matches();
    }

    /**
     *  * @description: 判断字符串是否为Integer类型 ，如果是则返回true  
     *  * @version: 1.0  * @date: 2019 2019年5月31日 上午10:24:59  * @param str
     *  * @return
     */
    public static boolean isInteger(String str) {
        Pattern pattern = Pattern.compile("((^[1-9]([0-9]*))|0)");
        return pattern.matcher(str).matches();
    }

    /**
     *  * @description: 判断是否为Boolean类型 ，如果是则返回true  
     *  * @version: 1.0  * @date: 2019 2019年5月31日 上午10:31:22  * @param str
     *  * @return
     */
    public static boolean isBoolean(String str) {
        return StringUtils.equalsIgnoreCase(str, "true") || StringUtils.equalsIgnoreCase(str, "false");
    }

}
