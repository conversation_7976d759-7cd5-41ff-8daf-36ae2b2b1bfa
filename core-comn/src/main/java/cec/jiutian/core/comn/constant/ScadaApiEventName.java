package cec.jiutian.core.comn.constant;

/**
 * <NAME_EMAIL> on 2020/8/27
 */
public enum ScadaApiEventName {
    CREATE_VARIABLE("Create_Variable"),
    UPDATE_VARIABLE("Update_Variable"),
    DELETE_VARIABLE("Delete_Variable"),
    CREATE_DATA_EXTRCT_TYP("Create_DATA_EXTRCT_TYP"),
    UPDATE_DATA_EXTRCT_TYP("Update_DATA_EXTRCT_TYP"),
    DELETE_DATA_EXTRCT_TYP("Delete_DATA_EXTRCT_TYP"),
    ;
    private final String name;

    ScadaApiEventName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }
}
