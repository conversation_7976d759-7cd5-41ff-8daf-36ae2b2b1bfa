package cec.jiutian.core.comn.message;

/**
 * <AUTHOR> Y
 * @version 1.0
 * @date 2019/8/22 3:35 PM
 */
public interface FactoryInformationStreamClient {
    String INPUT = "inputFactoryInformation";

    String OUTPUT = "outputFactoryInformation";

/*    @Input(FactoryInformationStreamClient.INPUT)
    SubscribableChannel input();

    @Output(FactoryInformationStreamClient.OUTPUT)
    MessageChannel output();*/
}
