package cec.jiutian.core.comn.util;

import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;

import java.text.ParseException;
import java.util.Calendar;
import java.util.Date;
import java.util.Objects;

/**
 * 日期处理
 *
 * <AUTHOR>
 */
public class DateHandler {

    /**
     * <P>获取系统当前日期</P>
     *
     * @return 返回系统当前日期
     */
    public static Date getCurrentDate() {
        return new Date();
    }

    /**
     * <P>日期转字符串</P>
     *
     * @param date 日期
     * @return 返回格式为"yyyy-MM-dd HH:mm:ss"的字符串日期
     */
    public static String fromDate(Date date) {
        return null == date ? null : DateFormatUtils.format(date, "yyyy-MM-dd HH:mm:ss");
    }

    /**
     * <P>日期转字符串</P>
     *
     * @param dateStr 日期字符串
     * @return 返回格式为"yyyy-MM-dd HH:mm:ss"的日期
     */
    public static Date toDate(String dateStr) {
        return toDate(dateStr, "yyyy-MM-dd HH:mm:ss");
    }

    /**
     * <P>日期转字符串</P>
     *
     * @param date    日期
     * @param pattern 日期格式
     * @return 返回指定日期格式的字符串
     */
    public static String fromDate(Date date, String pattern) {
        return null == date ? null : DateFormatUtils.format(date, pattern);
    }

    /**
     * <P>字符串转日期</P>
     *
     * @param dateStr 日期字符串
     * @param pattern 日期格式
     * @return 返回自定日期格式的日期
     */
    public static Date toDate(String dateStr, String pattern) {
        try {
            return StringUtils.isBlank(dateStr) ? null : DateUtils.parseDate(dateStr, pattern);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * <P>获取重置后的起始时间</P>
     *
     * @param date
     * @return yyyy-MM-dd 00:00:00.000
     */
    public static Date getResetStartTime(Date date) {
        // 如果为空，则直接抛出空指针，后面的代码就不执行了
        Objects.requireNonNull(date);
        Calendar startCalendar = Calendar.getInstance();
        startCalendar.setTime(date);
        startCalendar.set(startCalendar.get(Calendar.YEAR), startCalendar.get(Calendar.MONTH), startCalendar.get(Calendar.DATE), 0, 0, 0);
        startCalendar.set(Calendar.MILLISECOND, 0);
        return startCalendar.getTime();
    }

    /**
     * <P>获取重置后的结束时间</P>
     *
     * @param date
     * @return yyyy-MM-dd 23:59:59.999
     */
    public static Date getResetEndTime(Date date) {
        // 如果为空，则直接抛出空指针，后面的代码就不执行了
        Objects.requireNonNull(date);
        Calendar endCalendar = Calendar.getInstance();
        endCalendar.setTime(date);
        endCalendar.set(endCalendar.get(Calendar.YEAR), endCalendar.get(Calendar.MONTH), endCalendar.get(Calendar.DATE), 23, 59, 59);
        endCalendar.set(Calendar.MILLISECOND, 999);
        return endCalendar.getTime();
    }
}
