package cec.jiutian.core.comn.message;

/**
 * <AUTHOR> Y
 * @version 1.0
 * @date 2019/8/22 3:39 PM
 */
public interface OperationDefinitionStreamClient {

    String INPUT = "inputOperationDefinition";

    String OUTPUT = "outputOperationDefinition";

/*    @Input(OperationDefinitionStreamClient.INPUT)
    SubscribableChannel input();

    @Output(OperationDefinitionStreamClient.OUTPUT)
    MessageChannel output();*/
}
