package cec.jiutian.core.comn.message;

/**
 * <AUTHOR> Y
 * @version 1.0
 * @date 2019/6/20 9:43 AM
 */
public interface TransactionStreamClient {

//    String INPUT = "trans_";

//    String INPUT1 = "transactionLog1";

    String INPUT = "inputTransaction";

    String OUTPUT = "outputTransaction";

/*    @Input(TransactionStreamClient.INPUT)
    SubscribableChannel input();

    @Output(TransactionStreamClient.OUTPUT)
    MessageChannel output();*/

}
