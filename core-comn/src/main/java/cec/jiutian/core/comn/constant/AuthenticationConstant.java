package cec.jiutian.core.comn.constant;

public interface AuthenticationConstant {

    String AUDIENCE_ID = "100000";

    String SECRET_KEY = "3f7e6855fd9454956d2ead0b1e122482";

    // modRightsTree
    String MODULE = "MODULE";
    String RIGHTS = "RIGHTS";

    String INIT_PASSWORD = "123456";
    String ENCRYPTED_PWD = "encryptedPwd";
    String SALT = "salt";

    String LOGIN_EVENT_TYPE = "login";
    String LOGOUT_EVENT_TYPE = "logout";


    String YES = "Y";
    String NO = "N";

    String LOGIN_USER_PASSWORD_ERROR = "用户名或密码错误，请重新输入";
    String LOGIN_FLAG_ERROR = "账户异常，请联系管理员解除锁定";

    String REDIS_HASH_KEY_LOGIN_BLACKLIST = "Login_Blacklist";
    String REDIS_HASH_KEY_LOCKED = "Account_Locked";
    String REDIS_HASH_KEY_WAIT = "Account_Wait";

    String ORGANIZATION_TYPE_CODE = "ORGNZN";
    String ENTITY_TYPE_CODE = "ENTY";
    String NONE_TYPE_CODE = "NONE";

    String FACTORY_TYPE_CODE = "FCTRY";
    String WAREHOUSE_TYPE_CODE = "WRHS";
    String AREA_TYPE_CODE = "ARA";

    String SYSTEM_CODE_CORE = "CORE";

    String AUTHENTICATION_HEADER = "Authorization";
    String AUTHENTICATION_PREFIX = "Bearer ";
    String EXCLUDE_KEY = "mes.gateway.excludePatterns";
    String excludeURI = "";
    String FEATURE_SERVER_NAME = "fabos-base-feature";
    String ROOT_NAME = "ROOT";
    String CONTROL_MODE_NON = "NONE";
    String CONTROL_MODE_ORG = "ORGNZN";
    String CONTROL_MODE_ENT = "ENTY";
    // 3.0.4版本后，组织ID的字段名改为OID
    // String ORG_ID_COLUMN_NAME = "ORGNZN_ID";
    String ORG_ID_COLUMN_NAME = "OID";
    // 3.0.4版本后，组织ID的字段名改为OID
    // String ORG_ID_FIELD_NAME = "organizationIdentifier";
    String ORG_ID_FIELD_NAME = "oid";
    String SYS_ORG_ID = "0";
}
