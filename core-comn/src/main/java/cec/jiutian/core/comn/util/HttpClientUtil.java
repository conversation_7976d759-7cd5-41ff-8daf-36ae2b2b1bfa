package cec.jiutian.core.comn.util;

import jakarta.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/6/24
 */
public class HttpClientUtil {
    private static final String APP_ID_KEY = "X-SDC-APPLICATION-ID";
    private static final String X_REQUESTED_BY = "";

    public static String doGet(String httpUrl, String appId) {
        Map<String, Object> responseMap = new HashMap<>();
        return doGet(httpUrl, null, appId, responseMap);
    }

    public static String doGet(String httpUrl) {
        Map<String, Object> responseMap = new HashMap<>();
        return doGet(httpUrl, null, null, responseMap);
    }

    public static String doGet(String httpUrl, String requestBody, String appId, Map<String, Object> responseMap) {
        List<String> errors = new ArrayList<>();
        HttpURLConnection connection = null;
        InputStream is = null;
        BufferedReader br = null;
        String result = null;
        OutputStream os;
        try {
            URL url = new URL(httpUrl);
            connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(15000);
            connection.setReadTimeout(60000);
            connection.setRequestProperty("Content-Type", "application/json");
            if (null != appId) {
                connection.setRequestProperty(APP_ID_KEY, appId);
            }
            connection.connect();
            if (null != requestBody) {
                os = connection.getOutputStream();
                os.write(requestBody.getBytes());
            }
            if (connection.getResponseCode() == 200) {
                is = connection.getInputStream();
                br = new BufferedReader(new InputStreamReader(is, StandardCharsets.UTF_8));
                StringBuffer sbf = new StringBuffer();
                String temp;
                while ((temp = br.readLine()) != null) {
                    sbf.append(temp);
                    sbf.append(System.lineSeparator());
                }
                result = sbf.toString();
            }
        } catch (IOException e) {
            e.printStackTrace();
            errors.add(getExceptionStackTrace(e));
            responseMap.put("httpStatusCode", HttpServletResponse.SC_BAD_GATEWAY);
        } finally {
            // 关闭资源
            if (null != br) {
                try {
                    br.close();
                } catch (IOException e) {
                    e.printStackTrace();
                    errors.add(getExceptionStackTrace(e));
                    responseMap.put("httpStatusCode", HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                }
            }
            if (null != is) {
                try {
                    is.close();
                } catch (IOException e) {
                    e.printStackTrace();
                    errors.add(getExceptionStackTrace(e));
                    responseMap.put("httpStatusCode", HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                }
            }
            if (connection != null) {
                connection.disconnect();
                responseMap.put("httpStatusCode", HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            }
        }
        responseMap.put("error", errors);
        return result;
    }

    public static String doPost(String httpUrl, String requestBody, String appId, Map<String, Object> responseMap) {
        List<String> errors = new ArrayList<>();
        HttpURLConnection connection = null;
        InputStream is = null;
        OutputStream os = null;
        BufferedReader br = null;
        String result = null;
        try {
            URL url = new URL(httpUrl);
            connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setConnectTimeout(15000);
            connection.setReadTimeout(60000);
            connection.setDoOutput(true);
            connection.setDoInput(true);
            connection.setRequestProperty("Content-Type", "application/json");
            if (null != appId) {
                connection.setRequestProperty(APP_ID_KEY, appId);
            }
            if (null != requestBody) {
                os = connection.getOutputStream();
                os.write(requestBody.getBytes());
            }
            if (connection.getResponseCode() == 200) {
                is = connection.getInputStream();
                br = new BufferedReader(new InputStreamReader(is, StandardCharsets.UTF_8));
                StringBuilder sbf = new StringBuilder();
                String temp;
                while ((temp = br.readLine()) != null) {
                    sbf.append(temp);
                    sbf.append(System.lineSeparator());
                }
                result = sbf.toString();
            }
        } catch (IOException e) {
            e.printStackTrace();
            errors.add(getExceptionStackTrace(e));
            responseMap.put("httpStatusCode", HttpServletResponse.SC_BAD_GATEWAY);
        } finally {
            if (null != br) {
                try {
                    br.close();
                } catch (IOException e) {
                    e.printStackTrace();
                    errors.add(getExceptionStackTrace(e));
                    responseMap.put("httpStatusCode", HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                }
            }
            if (null != os) {
                try {
                    os.close();
                } catch (IOException e) {
                    e.printStackTrace();
                    errors.add(getExceptionStackTrace(e));
                    responseMap.put("httpStatusCode", HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                }
            }
            if (null != is) {
                try {
                    is.close();
                } catch (IOException e) {
                    e.printStackTrace();
                    errors.add(getExceptionStackTrace(e));
                    responseMap.put("httpStatusCode", HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                }
            }
            if (connection != null) {
                connection.disconnect();
            }
        }
        responseMap.put("error", errors);
        return result;
    }

    public static int doHttpGet(String httpUrl) {
        List<String> errors = new ArrayList<>();
        int resultCode = 0;
        try {
            URL url = new URL(httpUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(15000);
            connection.setReadTimeout(60000);
            connection.setRequestProperty("Content-Type", "application/json");
            resultCode = connection.getResponseCode();
            if (null != connection) {
                connection.disconnect();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return resultCode;
    }

    public static int doPost(String httpUrl) {
        List<String> errors = new ArrayList<>();
        int resultCode = 0;
        try {
            URL url = new URL(httpUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setConnectTimeout(15000);
            connection.setReadTimeout(60000);
            connection.setDoOutput(true);
            connection.setDoInput(true);
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("X-Requested-By", "sdc");
            resultCode = connection.getResponseCode();
            if (null != connection) {
                connection.disconnect();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return resultCode;
    }

    /**
     * 如果在转发请求的过程中报错则打印报错堆栈中10行信息
     */
    private static String getExceptionStackTrace(Exception exception) {
        StringBuilder sb = new StringBuilder(exception + "\n");
        StackTraceElement[] stackTraceElements = exception.getStackTrace();
        for (int index = 0; index < 10 && index < stackTraceElements.length; index++) {
            StackTraceElement stackTraceElement = stackTraceElements[index];
            sb.append("\tat ").append(stackTraceElement).append("\n");
        }
        return sb.toString();
    }
}
