package cec.jiutian.core.comn.errorCode;

public enum BaseErrorCode {

/*
    // 枚举已存在
    ENUM_EXISTS("MES-0013"),
    // 枚举值已存在
    ENUM_VALUE_EXISTS("MES-0030"),
    NO_VALID_ENUM_DEFN_FOUND("MES-0002"),

    PRE_CONDITION_NOT_MATCHED("MES-0003"),

    DROOLS_FIRE_BROKEN("MES-0004"),

    TIME_FORMAT_ERROR("MES-0006"),
    // 国际化配置的skey已存在
    SKEY_EXISTS("MES-0019"),
    // 国际化配置的skey不存在
    SKEY_NOT_EXIST("MES-0020"),
    ROLEID_CNG_ERROR("MES-0022"),

    PLAN_RELEASED_QTY_FOUND("MES-1032"),

    VALUE_GREATER_THAN_PARENT_VALUE("MES-1034"),

    G<PERSON><PERSON>_NAME_IS_EXIST("MES-1035"),
    SELECT_NULL_POINTER("MES-1078"),

    MACH_NOT_MATCH_PROC_OP_ID("MES-1119"),

    THE_DATA_HAS_NOT_EXISTED("MES-1131"),

    NOT_FIND_CORRECT_STATE("MES-1140"),

    TEST("MES-1004"),
    RIGHT_ALREADY_EXISTS("MES-1028"),
    // 在mm_csme_info中根据主键查询失败
    CSME_QTY_CHANGE_SELECT_FAILED("MES-1038"),
    // 在mm_csme_info中根据主键查询失败
    INCR_CSME_DRB_QTY_SELECT_FAILED("MES-1038"),
    // 在mm_csme_info中根据主键查询失败
    DECR_CSME_DRB_QTY_SELECT_FAILED("MES-1038"),
    // 下料数量大于已有数量
    CSME_QTY_MORE_THAN_CUR_QTY("MES-1039"),
    // 在mm_csme_unit_info中根据主键查询失败 没有该物料件次
    CSME_UNIT_RTN_SELECT_FAILED("MES-1041"),

    // 在mm_csme_info主键已存在
    CSME_INFO_PRIMARY_KEY_REPETITION("MES-1104"),

    // 在mm_csme_unit_info主键已存在
    CSME_UNIT_INFO_PRIMARY_KEY_REPETITION("MES-1105"),

    // 当前数量小于0
    CSME_CUR_QTY_LESS_THAN_0("MES-1106"),

    // 创建物料件次位置信息不存在
    CSME_CRE_UNIT_POSITION("MES-1107"),
    // 剩余数量不能小于零
    LEFT_NUM_NOT_LESS_THAN_ZERO("MES-1123"),
    // 载具名称已存在，插入失败
    DRB_CRE_INSERT_FAILED("MES-1036"),
    // 载具名称不存在，查询失败
    DRB_CANC_CRE_SELECT_FAILED("MES-1037"),
    DRB_UNIT_DEASGN_PROD_CRIR_SELECT_FAILED("MES-1037"),
    DRB_LOT_ASGN_CRIR_SELECT_FAILED("MES-1037"),
    // 载治具状态不等于Created
    DRB_CANC_CRE_DURABLE_ST_NOT_EQUAL_CREATED("MES-1058"),
    // 引用参数不合规范或者引用参数与需求不同,DRB_CLN_ST DRB_HLD_ST状态不满足需求
    DRB_UNIT_DEASGN_PROD_CRIR_ST_WRONG("MES-1047"),
    // 产品数量没有这么多被占用,UNIT_QTY小于减数
    DRB_UNIT_DEASGN_PROD_CRIR_UNIT_QTY_LESS_THAN_REDUCTION("MES-1048"),
    // 批次数量没有这么多被使用,LOT_QTY小于减数
    DRB_UNIT_DEASGN_PROD_CRIR_LOT_QTY_LESS_THAN_REDUCTION("MES-1049"),
    // 当前使用次数没有这么多,TIME_USED小于减数
    DRB_UNIT_DEASGN_PROD_CRIR_TIME_USED_LESS_THAN_REDUCTION("MES-1050"),
    // 添加错误代码 载治具清洁状态 不等于 Clean
    DRB_LOT_ASGN_CRIR_DRB_CLN_ST_NOT_EQUAL_TO_CLEAN("MES-1059"),
    // 添加错误代码 载治具暂留状态 不等于 NotOnHold
    DRB_LOT_ASGN_CRIR_DRB_HLD_ST_NOT_EQUAL_TO_NOT_ON_HOLD("MES-1060"),
    // 添加错误代码 输入批次数量 小于等于0
    DRB_LOT_ASGN_CRIR_LOT_QTY_LESS_THAN_OR_EQUAL_TO_ZERO("MES-1061"),
    // 添加错误代码 输入使用次数 小于等于0
    DRB_LOT_ASGN_CRIR_TIME_USED_LESS_THAN_OR_EQUAL_TO_ZERO("MES-1062"),
    // DRB_CLEAN_ST状态不为Clean或DRB_HLD_ST状态不为NotOnHold
    DRB_LOT_DEASGN_CRIR_DRB_CLN_ST_OR_DRB_HLD_ST_WRONG("MES-1093"),
    DRB_UNIT_ASGN_HLD_ST_NOT_NotOnHold("MES-1094"),
    DRB_UNIT_ASGN_LOT_OR_UNIT_AT_LEAST_HAS_ONE("MES-1101"),
    // 添加错误代码 生产批次不良信息表中根据主键查询失败
    LOT_CLN_RSN_CODE_SELECT_FAILED("MES-1056"),
    // 添加错误代码 生产单元不良信息表中根据主键查询失败
    UNIT_CLN_RSN_CODE_SELECT_FAILED("MES-1057"),
    // 添加错误代码 根据标签名选择标签失败
    PROD_LBL_RLSE_SELECT_FAILED("MES-1044"),
    // 添加错误代码 根据标签状态不合规范
    PROD_LBL_RLSE_LBL_ST_WRONG("MES-1045"),
    // 添加错误代码 根据标签状态转换失败
    PROD_LBL_RLSE_LBL_ST_CNVT_FAILED("MES-1046"),
    MACHINE_PRIMARYKEY_EXISTED("MES-1129"),
    MACHINE_GROUP_EXISTED("MES-1130"),
    NO_VALID_STATE_TYPE_FOUND2("MES-0101"),
    // mm_mat_mach_info通过id查询失败 无该机台名称或该机台位置
    MAT_MACH_MOUNT_SELECT_FAILED("MES-1040"),
    DRB_UNIT_DEASGN_DRB_HLD_ST("MES-1043"),
    MAT_MACH_DECR_RESULT_LESS_THAN_ZERO("MES-1102"),
    DO_RULE_FAILURE("MES-1126"),
    LOT_ST_NOT_RELEASED("MES-1099"),
    // LOT OR UNIT HLD ST
    LOT_OR_UNIT_HLD_ST_NOT_NOT_ON_HLD("MES-1091"),
    LOT_PROC_ST_NOT_WAIT("MES-1110"),
    // LOT_PROC_ST不为Run
    LOT_PROC_ST_NOT_RUN("MES-1112"),
    PROD_REQ_HLD_ST_SHOULD_NOT_ONHOLD("MES-1115"),
    // PROD_REQ_ERP_ST不该为Released
    PROD_REQ_ERP_ST_SHOULD_NOT_RELEASED("MES-1116"),
    // PROD_REQ_CTRL_ST不该为Open
    PROD_REQ_CTRL_ST_SHOULD_NOT_OPEN("MES-1117"),
    // 上料种类或者数量不符合
    MAT_TYPE_OR_MOUNT_NOT_MATCH("MES-1118"),
    // CSME_LOAD_CSM_TYP不为BOM或不为NOT
    CSME_LOAD_CSM_TYP_NOT_BOM_OR_NOT("MES-1124"),
    // 状态不是InProduction
    UNIT_ST_NOT_INPRODUCTION("MES-1074"),
    // UNIT_PROC_ST不为Idle
    UNIT_PROC_ST_NOT_IDLE("MES-1113"),
    // UNIT_PROC_ST不为Processing
    UNIT_PROC_ST_NOT_PROCESSING("MES-1111"),
    // DRB_CLN_ST 不为Clean
    DRB_CLN_ST_NOT_CLEAN("MES-1114"),
    // 执行的规则数量为0或者小于应执行的规则数量
    DO_RULE_COUNT_NOT_CORRECT("MES-1127"),
    ERROR_EXISTS("MES-11112"),
    NO_VALID_STATE_TYPE_FOUND("MES-0101"),

    NO_VALID_LOT_FOUND("MES-1001"),
    NO_VALID_PROD_SPEC_FOUND("MES-1002"),
    NO_VALID_PROD_FOUND("MES-1003"),
    NO_VALID_CSME_SPEC_FOUND("MES-1004"),
    NO_VALID_DRB_SPEC_FOUND("MES-1005"),
    NO_VALID_TARGER_STATE_FOUND("MES-0003"),
    NO_VALID_DRB_FOUNT("MES-0102"),

    LOT_CARRIER_NAME_EXISTED("MES-1006"),
    LOT_GROUP_NAME_EXISTED("MES-1007"),
    LOT_PROD_NAME_EXISTED("MES-1008"),
    UNIT_GROUP_NAME_EXISTED("MES-1009"),
    UNIT_PROD_NAME_EXISTED("MES-1010"),

    CURRENT_QTY_GREATER_THAN_CREATE_QTY("MES-1011"),
    SPLIT_QTY_GREATER_THAN_CREATE_QTY("MES-1012"),
    DRB_QTY_GREATER_THAN_PROD_QTY("MES-1013"),
    CONFLICTED_COMMON_FAB("MES-1014"),
    TARGET_QTY_GREATER_THAN_CREATE_QTY("MES-1015"),
    EXCEED_CAPACITY_QTY("MES-1016"),
    PROD_SPEC_NAME_MISMATCH("MES-1017"),
    PROD_SPEC_VER_MISMATCH("MES-1018"),
    OWNER_MISMATCH("MES-1019"),
    LOT_UNIT_STATE_MISMATCH("MES-1020"),
    ASSIGNED_QTY_GREATER_THAN_PLAN_QTY("MES-1021"),
    DECREMENT_QTY_GREATER_THAN_FINISHED_QTY("MES-1022"),
    CSME_SPEC_NAME_MISMATCH("MES-1023"),
    CSME_SPEC_VER_MISMATCH("MES-1024"),
    NO_VALID_SOURCE_CSME_FOUND("MES-1025"),
    NO_VALID_TARGET_CSME_FOUND("MES-1026"),
    MERGE_QTY_GREATER_THAN_CREATE_QTY("MES-1027"),
    NO_VALID_HISTORY_FOUND("MES-1030"),
    EXCEED_PROD_REQ_QTY("MES-1033"),
    // 添加错误代码wip_unit_info表中无该主键所代表的行
    UNIT_ASGN_GRP_SELECT_FAILED("MES-1042"),
    UNIT_DEASGN_GRP_SELECT_FAILED("MES-1042"),
    UNIT_DEASGN_PROD_CRIR_SELECT_FAILED("MES-1042"),
    UNIT_DO_NOT_HOLD_SELECT_FAILED("MES-1042"),
    CHANGE_PROD_MDL_UNIT_NAME_SELECT_FAILED("MES-1042"),
    PROD_TRACK_IN_V1_SELECT_FAILED("MES-1042"),
    PROD_ASGN_BOX_PACK_UNIT_NAME_SELECT_FAILED("MES-1042"),
    PROD_TRACK_IN_V3_UNIT_NAME_SELECT_FAILED("MES-1042"),
    // 添加错误代码wip_unit_info表中UNIT_HLD_ST状态不是NOT_ON_HOLD
    UNIT_DEASGN_GRP_UNIT_HLD_ST_WRONG("MES-1043"),
    UNIT_DEASGN_PROD_CRIR_UNIT_HLD_ST_WRONG("MES-1043"),
    // 添加错误代码wip_lot_info表中LOT_NAME物料批次不存在
    LOT_DO_NOT_HOLD_SELECT_FAILED("MES-1051"),
    PROD_REQ_RLSE_SELECT_FAILED("MES-1051"),
    CHANGE_PROD_MDL_LOT_NAME_SELECT_FAILED("MES-1051"),
    PROD_ASGN_BOX_PACK_LOT_NAME_SELECT_FAILED("MES-1051"),
    PROD_TRACK_IN_V3_LOT_NAME_SELECT_FAILED("MES-1051"),
    // 添加错误代码wip_lot_info表中LOT_ST物料批次状态不是Released|Completed
    LOT_DO_NOT_HOLD_LOT_ST_NOT_EQUAL_RELEASED_OR_COMPLETED("MES-1052"),
    // 添加错误状态wip_lot_info表中LOT_PROC_ST物料批次运行状态不是Wait
    LOT_DO_NOT_HOLD_LOT_PROC_ST_NOT_EQUAL_WAIT("MES-1053"),
    // 添加错误代码wip_lot_info表中LOT_ST物料批次状态不是Released|Completed
    UNIT_DO_NOT_HOLD_LOT_ST_NOT_EQUAL_RELEASED_OR_COMPLETED("MES-1054"),
    // 添加错误状态wip_lot_info表中LOT_PROC_ST物料批次运行状态不是Wait
    UNIT_DO_NOT_HOLD_LOT_PROC_ST_NOT_EQUAL_WAIT("MES-1055"),
    UNIT_SCRAP_DEASGN_LOT_ST("MES-1073"),
    UNIT_SCRAP_DEASGN_UNIT_ST("MES-1074"),
    UNIT_SCRAP_DEASGN_CRIR_NAME_AND_GRP_NAME("MES-1075"),
    // 添加错误状态wip_unit_info表中unit_proc_st件次运行状态不是Idle
    UNIT_DO_ASGN_WITH_OUT_CRIR_UNIT_PROC_ST_NOT_IDLE("MES-1055"),
    // 添加错误状态wip_unit_info表中unit_st件次状态不是INPRODUCTION或COMPLETED
    UNIT_DO_ASGN_WITH_OUT_CRIR_UNIT_ST_NOT_INPRODUCTION_OR_COMPLETED("MES-1054"),
    // 添加错误状态wip_unit_info表中unit_hold_st状态不是NotOnHold
    UNIT_DO_ASGN_WITH_OUT_CRIR_UNIT_HOLD_ST_NOT_NOT_ON_HOLD("MES-1043"),
    CHANGE_PROD_MDL_UNIT_HOLD_ST_NOT_EQUAL_TO_NOT_ON_HOLD("MES-1043"),

    // lotFlag为Y时lotName不能为空unitFlag为Y时unitName不能为空吴绮婕
    LOT_NAME_CANNOT_BE_NULL("MES-1051"),
    UNIT_NAME_CANNOT_BE_NULL("MES-1052"),

    LOT_DO_SPLIT_WITHOUT_CRIR_LOT_ST_NOT_RELEASED_OR_COMPLETED("MES-1052"),
    LOT_DO_SPLIT_WITHOUT_CRIR_LOT_PROC_ST_NOT_WAIT("MES-1053"),
    LOT_DO_SPLIT_WITHOUT_CRIR_LOT_HLD_ST_NOT_NOT_ON_HOLD("MES-1043"),
    LOT_DO_SPLIT_WITHOUT_CRIR_UNIT_QTY_MORE_THAN_NOW("MES-1063"),
    LOT_DO_RECEIVE_UNIT_QTY_NOT_GREATER_THAN_ZERO("MES-1061"),
    LOT_DO_RETURN_UNIT_QTY_NOT_GREATER_THAN_ZERO("MES-1061"),
    LOT_DO_SHIPPED_UNIT_QTY_NOT_GREATER_THAN_ZERO("MES-1061"),
    LOT_DO_UN_SHIPPED_UNIT_QTY_NOT_GREATER_THAN_ZERO("MES-1061"),
    SAVE_DUPLICATE_PRIMARY_KEY("MES-1077"),
    LOT_DO_COMPOSE_WITHOUT_CRIR_UNIT_QTY_MORE_THAN_NOW("MES-1079"),
    CHECK_RLSE_QTY_OR_FIN_QTY_OR_SCRP_QTY("MES-1076"),

    // 添加错误代码wip_qc_chk表检验最终结果中已存在该结果
    CRE_QC_LOT_INSERT_FAILED("MES-1064"),
    // 添加错误代码wip_qc_items_chk表检验最终结果中已存在该结果
    CRE_QC_UNIT_INSERT_FAILED("MES-1065"),
    // 添加错误代码批次名称输入为空
    CHANGE_PROD_MDL_LOT_NAME_INPUT_NULL("MES-1067"),
    // 添加错误代码单元名称输入为空
    CHANGE_PROD_MDL_UNIT_NAME_INPUT_NULL("MES-1067"),
    // 添加错误代码单元名称输入长度错误
    CHANGE_PROD_MDL_UNIT_NAME_LENGH_INPUT_WRONG("MES-1068"),
    // 添加错误代码批次状态不等于NotOnHold
    CHANGE_PROD_MDL_LOT_HOLD_ST_NOT_EQUAL_TO_NOT_ON_HOLD("MES-1069"),
    LOT_DO_RELEASE_LOT_ST_NOT_CREATED("MES-1052"),
    PROD_SPEC_ACTIVE_ST_NOT_ACTIVE("MES-1088"),
    CHANGE_PROD_UNIT_TYP_NO_LOT_NAME_OR_UNIT_NAME("MES-1091"),
    CHANGE_PROD_UNIT_TYP_LOT_HLD_ST_OR_UNIT_HLD_ST_NOT_NOT_ON_HOLD("MES-1092"),
    // 添加错误代码LOT_NAME和UNIT_NAME必有一项输入
    PROD_ASGN_BOX_PACK_LOT_NAME_OR_UNIT_NAME_IS_EMPTY("MES-1089"),

    // 添加错误代码PROC_OP_NAME为空PROD_TRACK_IN_V1执行失败
    PROD_TRACK_IN_V1_PROC_OP_NAME_IS_NULL("MES-1090"),

    // 添加错误代码所创建批次的件次数与实际操作的件次数不等吴绮婕
    VALUE_OF_UNITQTY_AND_UNITS_ARE_NOT_EQUAL("MES-1092"),
    // 添加错误代码CRE_UNIT_QTY必须大于0
    CREUNITQYT_MUST_GREATER_THAN_0("MES-1095"),
    // 添加错误代码UNIT_QTY必须大于0
    UNITQYT_MUST_GREATER_THAN_0("MES-1096"),
    DO_DECR_FIN_QTY_NOT_BIGGER_THAN_ZERO("MES-1097"),
    DO_QC_UNIT_CNG_LOT_OR_UNIT_AT_LEAST_HAS_ONE("MES-1098"),
    DO_UNIT_ASGN_LOT_LOT_ST_NOT_RELEASED("MES-1099"),
    DO_UNIT_ASGN_LOT_UNIT_ST_NOT_INPRODUCTION("MES-1100"),
    DO_INCR_GNRT_LOT_QTY_NULL("MES-1103"),
    NO_FIND_PROCESS("MES-0103"),
    // 错误代码：该批次未进站，无法进行此操作。批次信息
    CAN_NOT_TRACK_IN("MES-1108"),
    // 错误代码：该批次未进站，无法进行此操作。批次信息
    ALREADY_TRACKED_IN("MES-1108"),
    PROD_REQ_HLD_ST_ONHOLD("MES-1115"),
    PROD_REQ_ERP_ST_RELEASED("MES-1116"),
    PROD_REQ_CTRL_ST_OPEN("MES-1117"),
    SHOULD_BE_NOT_ON_HOLD("MES-1121"),
    UNIT_QTY_COULD_NOT_BE_NAGETIVE("MES-1122"),
    LOT_NAME_NOT_MATCH_FTY_NAME("MES-1125"),
    // 生成规则参数不对
    NEW_SERIAL_PARAM_NOT_CORRECT("MES-1128"),

    // 机台输入错误
    MACH_NAME_ERROR("MES-8001"),
    // 机台属性已存在
    MACH_PROP_EXISTED("MES-8002"),
    // 机台属性值与类型不匹配
    MACH_PROP_VAL_ERROR("MES-8003"),
    // 机台属性不存在
    MACH_PROP_NOT_EXISTED("MES-8004"),
    // SQL操作未知异常
    EQP_SQL_OPERATE_ERROR("MES-8005"),
    NO_VALID_BASE_DATA("MES-8006"),

    // 载制具输入错误
    DRB_NAME_ERROR("MES-5001"),
    // 载制具属性已存在
    DRB_PROP_EXISTED("MES-5002"),
    // 载制具属性值与类型不匹配
    DRB_PROP_VAL_ERROR("MES-5003"),
    // 载制具属性不存在
    DRB_PROP_NOT_EXISTED("MES-5004"),
    // SQL操作未知异常
    DRB_SQL_OPERATE_ERROR("MES-5005"),
    // 载治具Spec已存在
    DRB_SPEC_EXISTED("MES-5006"),
    // 载治具Spec不存在
    DRB_SPEC_NOT_EXISTED("MES-5007"),
    // 载治具已存在
    DRB_NAME_EXISTED("MES-5008"),
    // 载治具不存在
    DRB_NAME_NOT_EXISTED("MES-5009"),
    // 载治具编号生成错误
    DRB_NAME_GN_ERROR("MES-5010"),
    // 命名生成规则不存在
    NAMING_SERIAL_NOT_EXISTED("MES-7001"),
    // 日期格式错误
    DATE_FORMAT_ERROR("MES-7002"),
    // 图片上传错误
    IMG_UPLOAD_ERROR("MES-12001"),
    // 图标信息不存在
    ICON_NOT_EXITSTED("MES-6001"),
    // CFM 机台配置信息不存在
    CFM_EQP_CONFIG_NOT_EXISTED("MES-6002"),
    // CFM 图标上传 描述信息为空
    CFM_ICON_UPLOAD_DTO_EMPTY("MES-6003"),
    // URL 为空
    URL_EMPTY("MES-6004"),
    SQL_ID_NOT_EXSIST("MES-10001"),
    NO_TABLE_FOUND("MES-10002"),
    NO_COLUMN_FOUND("MES-10003"),
    SQL_NOT_MATCH("MES-10004"),
    // MQ message为空
    CFM_MQ_MSG_EMPTY("MES-6005"),
    SQL_NOT_SELECT("MES-10005"),
    // 数据格式错误，
    CFM_DATA_FORM_ERROR("MES-6006"),
    // 上级部门不能是当前部门
    SUPERDEPT_NOT_SELF("MES-2001"),
    // CFM 流程绑定信息不存在
    CFM_FLOW_CONFIG_NOT_EXISTED("MES-6007"),
    EQP_FTY_MACH_NOT_NULL("MES-8007"),
    EQP_PROC_OP_ID_NOT_NULL("MES-8006"),
    MACH_GRP_NOT_EXISTED("MES-8008"),
    REASONCODE_NOT_EXISTED("MES-8009"),
    REASONCODE_EXISTED("MES-8010"),
    REASONCODETYPE_NOT_EXISTED("MES-8011"),
    REASONCODETYPE_EXISTED("MES-8012"),
    PORT_NOT_EXISTED("MES-8013"),
    PORT_EXISTED("MES-8014"),
    PORT_SPEC_NOT_EXISTED("MES-8015"),
    PORT_SPEC_EXISTED("MES-8016"),
    CARRIER_NOT_EXISTED("MES-9003"),
    CARRIER_EXISTED("MES-9004"),
    MATERIAL_NOT_EXISTED("MES-9005"),
    MATERIAL_EXISTED("MES-9006"),
    CARRIER_SPEC_PROP_EXISTED("MES-7005"),
    CARRIER_SPEC_PROP_VAL_ERROR("MES-7006"),
    CARRIER_SPEC_PROP_NOT_EXISTED("MES-7007"),
    CARRIER_RLSE_ERROR("MES-8202"),
    LOT_BIND_ORDER_ERROR("MES-8203"),
    // 工序已存在
    FLOW_OP_EXISTS("MES-9009"),
    // 工序不存在
    FLOW_OP_NOT_EXIST("MES-9010"),
    // 流程JSON_XML解析失败
    FLOW_JSON_XML_PARSE_FAIL("MES-9011"),
    // 流程已存在
    FLOW_PROC_EXISTS("MES-9012"),
    // 流程不存在
    FLOW_PROC_NOT_EXIST("MES-9013"),
    // 流程被占用
    FLOW_PROC_IS_PROCESSING("MES-9014"),
    // 流程占用情况检测失败
    FLOW_PROC_STATE_OBSERVE_FAIL("MES-9015"),
    // 工序绑定已存在
    MACH_OP_BINDING_EXIST("MES-8017"),
    // 工序绑定不存在
    MACH_OP_BINDING_NOT_EXIST("MES-8018"),
    // 工厂已存在
    FACTORY_EXISTS("MES-0007"),
    // 工厂不存在
    FACTORY_NOT_EXISTS("MES-0008"),
    // 区域已存在
    AREA_EXISTS("MES-0009"),
    // 区域不存在
    AREA_NOT_EXIST("MES-0010"),
    // 合作商已存在
    PARTNER_EXISTS("MES-0011"),
    // 合作商不存在
    PARTNER_NOT_EXIST("MES-0012"),
    // 错误代码已存在
    ERROR_CODE_EXISTS("MES-0014"),
    PRDT_EXISTS("MES-0016"),
    PRDT_NOT_EXISTS("MES-0017"),
    // 菜单权限树为空
    MODULE_RIGHTS_TREE_NULL("MES-0021"),
    // 产品规格已存在
    PRODUCTION_SPEC_EXISTS("MES-Product-0001"),

    // 工序物料流程已经存在
    MTRL_PRCS_CNSMPN_DFNTN_EXIST("MES-BOM-0001"),

    // 工序物料流程不存在
    MTRL_PRCS_CNSMPN_DFNTN_NOT_EXIST("MES-BOM-0002"),

    // 工程参数已经存在
    MTRL_PRCS_CNSMPN_PRPRTY_EXIST("MES-BOM-0003"),

    // 工程参数不存在
    MTRL_PRCS_CNSMPN_PRPRTY_NOT_EXIST("MES-BOM-0004"),
    //批次的产品数量不能为0
    LOT_QTY_NOT_BE_ZERO("MES-82021"),
    //该流程不存在站点
    PROC_NO_OPS("MES-11001"),
    //工单关闭失败
    PROD_ORDER_CLOSE_FAILURE("MES-82022"),
    //根據站點查詢機台識別
    SEARCH_MACH_NAMES_BY_OPS_FAIL("MES-4001"),
    FEIGN_GET_PROD_SPEC_FAIL("MES-82023"),
    FEIGN_GET_PROC_FAIL("MES-82024"),
    FEIGN_GET_ORDER_FAIL("MES-82056"),
    // 当前数量小于0
    ORDER_CUR_QTY_LESS_THAN_OR_ZERO("MES-82025"),
    ORDER_RLSE_QTY_MORE_THAN_PLAN_QTY("MES-82026"),
    //推送规则已存在
    PUSH_RULE_EXIST("MES-8088"),
    //流程被禁用
    FLOW_DISABLED("MES-8089"),
    //推送规则不存在
    PUSH_RULE_NOT_EXIST("MES-8090"),
    //机台保养计划已存在
    MACHINE_MAINTAIN_PLAN_EXIST("MES-8887"),
    //机台保养计划不存在
    MACHINE_MAINTAIN_PLAN_NOT_EXIST("MES-8888"),

    OUTBOX_HAS_BEEN_LOADED("MES-8889"),

    SYSTEM_CONFIGURATION_EXISTS("MES-8890"),

    SYSTEM_CONFIGURATION_NOT_EXISTS("MES-8891"),

    //导入时枚举名不允许重复或者为空
    ENUM_NAME_NOT_ALLOW_EMPTY_OR_REPEATE("MES-8892"),

    //枚举名由英文字符（-） 、数字的顺序组成，首字母必须为英文字符
    ENUM_NAME_CONSIST_OF_ENGLISH_NUMBER("MES-8893"),

    //导入时枚举值不允许重复或者为空
    ENUM_VALUE_NOT_ALLOW_EMPTY_OR_REPEATE("MES-8894"),

    //枚举值由英文字符（-） 、数字的顺序组成，首字母必须为英文字符
    ENUM_VALUE_CONSIST_OF_ENGLISH_NUMBER("MES-8895"),

    // 采购单PO ID已存在
    PO_EXISTS("WMS-0024"),
    // 采购单不存在
    PO_NOT_EXIST("WMS-0026"),
    // 采购单状态错误
    PO_STATE_ERROR("WMS-0027"),
    // 采购清单不存在
    PO_DETAIL_NOT_EXIST("WMS-0028"),
    // 仓储规格不存在
    PROD_SPEC_NOT_EXIST("WMS-0025"),
    //冻结类型不存在
    HOLD_TYPE_NOT_EXIST("WMS-0001"),
    //冻结单存在上级冻结
    SUPERIOR_HOLD_ALREADY_EXISTS("WMS-0002"),
    //仓储批次不存在
    WHS_LOT_NOT_EXISTS("WMS-0003"),
    //仓储载具不存在
    WMS_CRR_NOT_EXIST("WMS-0004"),
    //查询不到可以绑定载具的批次
    WMS_BIND_LOT_NOT_EXIST("WMS-0005"),
    //批次不能绑定载具，待绑定的批次的规格，等级，状态，位置信息必须一致
    WMS_LOT_CAN_NOT_BIND_CRR("WMS-0006"),
    //载具的状态不对，不能绑定,载具的状态必须为Created，InUse或者Empty
    WMS_BIND_CRR_STATE_NOT_RIGHT("WMS-0007"),
    //绑定批次与待绑定批次状态规格，等级，位置信息不一致
    BATCH_TO_BE_BOUND_WITH_BOUND_BATCH_ERROR("WMS-0008"),
    //成品批次的最小包装数量与实际产品数量不符
    PRODUCT_COUNT_ERROR("WMS-0009"),
    //退料清单状态错误
    RETURN_DETAIL_STATE_ERROR("WMS-0010"),
    //退料单状态错误
    RETURN_STATE_ERROR("WMS-0011"),
    //退料批次的最小包装数量错误
    RETURN_LOT_CN_ERROR("WMS-0012"),
    //领料清单状态错误
    SO_DETAIL_STATE_ERROR("WMS-0013"),
    //领料清单不存在
    SO_DETAIL_NOT_EXIST("WMS-0014"),
    //领料单状态错误
    SO_STATE_ERROR("WMS-0015"),
    //领料单不存在
    SO_NOT_EXIST("WMS-0016"),
    //载具冻结
    CARRIER_ON_HOLD("WMS-0017"),
    //收货单状态错误
    ASN_STATE_ERROR("WMS-0018"),
    // 数量超出范围
    OUT_OF_RANGE("WMS-0029"),
    // 退货清单不存在
    RETURN_DETAIL_NOT_EXIST("WMS-0030"),
    // 退货单不存在
    RETURN_NOT_EXIST("WMS-0031"),
    // 收货单已存在
    ASN_EXISTS("WMS-0047"),
    // 收货单不存在
    ASN_NOT_EXIST("WMS-0051"),
    // 收货清单不存在
    ASN_DETAIL_NOT_EXIST("WMS-0052"),
    // 仓库与物料类型的绑定关系已存在
    WHS_PRODUCT_TPYE_LOCATION_DEFINITION_EXISTS("WMS-0032"),
    // 仓库已存在
    WHS_WAREHOUSE_EXISTS("WMS-0033"),
    // 仓库编码已存在
    WHS_WAREHOUSECODE_EXISTS("WMS-0035"),
    // 仓库名称已存在
    WHS_WAREHOUSENAME_EXISTS("WMS-0036"),
    // 仓库不存在
    WHS_WAREHOUSE_NOT_EXISTS("WMS-0038"),
    // 库区不存在
    WHS_WAREHOUSEBLOCK_NOT_EXISTS("WMS-0039"),
    // 库区编码已存在
    WHS_WAREHOUSEBLOCKCODE_EXISTS("WMS-0040"),
    // 库区名称已存在
    WHS_WAREHOUSEBLOCKNAME_EXISTS("WMS-0041"),
    // 库位编码已存在
    WHS_WAREHOUSESHELFCODE_EXISTS("WMS-0042"),
    // 库位名称已存在
    WHS_WAREHOUSESHELFNAME_EXISTS("WMS-0043"),
    // 库位不存在
    WHS_WAREHOUSESHELF_NOT_EXISTS("MES-0044"),
    // 仓库与物料规格的绑定关系已存在
    WHS_PRODUCT_SPEC_LOCATION_DEFINITION_EXISTS("WMS-0045"),
    // 库位与物料规格的绑定关系已存在
    WHS_SHELF_PRODUCT_LOCATION_DEFINITION_EXISTS("WMS-0046"),
    // 载具规格编码已存在
    CARRIER_SPECIFICATIONCODE_EXISTS("WMS-0048"),
    // 载具规格名称已存在
    CARRIER_SPECIFICATIONNAME_EXISTS("WMS-0049"),
    // 载具规格不存在
    CARRIER_SPECIFICATION_NOT_EXISTS("WMS-0050"),
    // 载具不存在
    CARRIER_NOT_EXISTS("WMS-0056"),
    // 载具名称已存在
    CARRIERNAME_EXISTS("WMS-0057"),
    // 载具编号已存在
    CARRIERCODE_EXISTS("WMS-0058"),
    // 父级载具不存在
    SUPER_CARRIER_NOT_EXISTS("WMS-0059"),
    // 载具使用中
    CARRIER_INUSE("WMS-0060"),

    // 物料入库-批次不属于同一个采购单和收货单
    PO_ASN_NOT_SAME("WMS-0053"),
    //采购单不一致
    PO_NOT_SAME("WMS-0091"),
    //入库数量过多
    LOTS_UNIT_TOO_MACH("WMS-0054"),
    //存在批次不存在
    SOME_LOTS_NOT_EXIST("WMS-0055"),
    //收货清单状态不正在去
    ASN_DETAIL_STATE_WRONG("WMS-0061"),
    //批次状态错误
    LOT_STATE_WRONG("WMS-0062"),
    //产品状态错误
    PROD_STATE_WRONG("WMS-0089"),
    //拒收数量过多
    REJECTION_NUM_TOO_MACH("WMS-0063"),
    //替代料组信息已存在
    ALTERNATIVE_MATERIAL_GROUP_ALREADY_EXIST("WMS-0064"),
    //替代料组详细信息有重复值
    ALTERNATIVE_MATERIAL_GROUP__DETAILS_HAVE_DUPLICATE_VALUES("WMS-0065"),
    //替代料组信息不存在
    ALTERNATIVE_MATERIAL_GROUP_NOT_EXIST("WMS-0066"),
    //替代料组详细信息不存在
    ALTERNATIVE_MATERIAL_GROUP__DETAILS_NOT_EXIST("WMS-0067"),
    //库存物料规格信息不存在
    STOCK_MATERIAL_SPECIFICATION_NOT_EXIST("WMS-0068"),
    //批次状态错误
    LOT_STATE_ERROR("WMS-0069"),
    //安全库存信息不存在
    SAFE_INVENTORY_NOT_EXIST("WMS-0070"),
    //安全库存信息已存在
    SAFE_INVENTORY_EXIST("WMS-0071"),
    //库位冻结
    SHELF_ON_HOLD("WMS-0072"),
    //仓库冻结
    WAREHOUSE_ON_HOLD("WMS-0073"),
    //载具满载
    CARRIER_FULL("WMS-0074"),
    //批次拆批后的总仓储数量要与原批次的仓储数量相等
    LOT_SPILT_COUNT_ERROR("WMS-0075"),
    //收货清单的抽检到的不良数量错误，抽检不良数量不能大于抽检数量
    ASN_DETAL_ASMPLE_BAD_COUNT_ERROR("WMS-0076"),
    //收货清单的抽检数量错误，抽检数量不能大于收货数量
    ASN_DETAL_ASMPLE_COUNT_ERROR("WMS-0077"),
    //创建时间OnlineDate有误，库位不能大于库区，库区不能大于仓库
    ONLINE_DATE_ERROR("WMS-0078"),
    //长宽高，容积等参数错误，库位不能大于库区，库区不能大于仓库
    WHS_PARAMETER_ERROR("WMS-0079"),
    // 工厂日历已存在
    CALENDAR_EXISTS("MES-0031"),
    // 班组时间冲突
    SHIFT_TIME_CONFLICT("MES-0032"),
    // 批次ID已存在
    LOT_ID_EXISTS("MES-0033"),
    // 批次ID不存在
    LOT_ID_NOT_EXIST("MES-0034"),
    // 物料消耗ID不存在
    MATERIAL_PROCESS_NOT_EXIST("MES-0035"),
    // 工序参数ID不存在
    MATERIAL_PROCESS_PROPERTY_NOT_EXIST("MES-0036"),
    // 预警通知配置ID不存在
    ALARM_ITEM_CONFIG_NOT_EXIST("MES-0037"),
    // 预警用户不存在
    ALARM_USER_NOT_EXIST("MES-0038"),
    // 预警用户组不存在
    ALARM_USER_GROUP_NOT_EXIST("MES-0039"),
    // 预警用户组绑定关系不存在
    ALARM_USER_GROUP_BOND_NOT_EXIST("MES-0040"),
    // 预警配置绑定关系不存在
    ALARM_GROUP_ITEM_BOND_NOT_EXIST("MES-0041"),
    // 预警信息不存在
    ALARM_MESSAGE_NOT_EXIST("MES-0042"),
    // 预警规则类型不为A不能设置检查频率
    ALARM_ITEM_TYPE_ERROR("MES-0045"),
    // 预警检查频率不能为空
    ALARM_CHECK_FREQUENCY_ERROR("MES-0046"),
    // 载具状态不对
    CARRIER_STATE_WRONG("WMS-0080"),
    // 盘点计划状态不对
    PLAN_STATE_WRONG("WMS-0081"),
    //收货方不唯一
    PROD_RECEIVER_NOT_UNIQUE("WMS-0082"),
    // 冻结单状态不对
    HOLD_STATE_WRONG("WMS-0083"),
    // 替代料未绑定
    SPEC_NOT_BIND("WMS-0084"),
    // 替代料已绑定
    SPEC_ALREADY_BIND("WMS-0085"),
    // 权限输入错误
    RIGHT_NOT_EXIST("MES-0086"),
    // 必输项
    URL_EXIST("MES-0087"),
    // 必输项
    MUST_HAVE_VALUE("MES-0088"),
    // 盘点计划状态不正确
    STOCK_PLAN_STATE_ERROR("WMS-0086"),
    //命名规则参数名称已存在
    SECTION_NAME_EXIST("MES-7008"),
    // 批量插入时ID重复
    ID_REPEAT("WMS-0087"),
    // 物料消耗属性创建失败
    MTRL_CNSMPN_PRPRTY_CREATE_FAIL("MES-4002"),
    //不存在下一站点
    NO_NEXT_OPERATION("MES-WIP-0001"),
    //不存在前一站点
    NO_PREVIOUS_OPERATION("MES-WIP-0002"),
    // 工单报废数量更新失败
    ORDER_SCRAP_UPDATE_ERROR("MES-0089"),
    //载具规格简称已经存在
    CARRIER_SPECIFICATION_SHORT_NAME_EXISTS("WMS-0088"),
    // 产品冻结信息不存在
    PRODUCT_HOLD_ERROR("MES-0091"),
    PRODUCT_FUTUREHOLD_ERROR("MES-12008"),
    //维修记录已经存在
    REPAIR_EXISTS("MES-0092"),
    //产品已经不在之前Sample站点了
    PROD_NOT_IN_SAMPLE_PROC_OPERATION("MES-8204"),
    //暂留信息不存在
    HLD_INFO_NOT_EXIST("MES-8205"),
    //所给产品不存在
    PRODUCT_NOT_EXIST("MES-8206"),
    //自动抽样配置不存在
    AUTOMATIC_SAMPLE_NOT_EXIST("MES-8207"),
    //批次不存在
    LOT_NOT_EXIST("MES-8208"),
    //包装号不存在
    PRCS_GRP_NM_NOT_EXIST("MES-8209"),
    //包装号不为空
    PRCS_GRP_NM_NOT_EMPTY("MES-8210"),
    //包装号为空
    PRCS_GRP_NM_IS_EMPTY("MES-8211"),
    //包装号已存在
    PRCS_GRP_NM_EXISTED("MES-8212"),
    //先解外箱再执行内箱解包
    UNPACK_OUTER_BOX_FIRST_THEN_INNER_BOX("MES-8213"),
    //内箱已有外包箱
    INNERBOX_HAVE_OUTBOX("MES-8214"),
    //内箱不在同一流程工序
    INNERBOX_PROCESS_NOT_SAME("MES-8215"),
    //外箱在栈板上，请先解栈板
    OUTBOX_HAVE_PALLET("MES-8216"),
    //不是栈板
    NOT_PALLET("MES-8217"),
    //状态错误
    STATE_ERROR("MES-8218"),
    //QTime配置已存在
    QTIME_CONFIG_EXISTED("MES-8219"),
    MAX_MIN_QTIME_MUST_HAVE_AT_LEAST_ONE_VALUE("MES-8220"),
    //QTime配置不存在
    QTIME_CONFIG_NOT_EXIST("MES-8221"),
    //站点顺序检测失败
    OPERATION_ORDER_CHECK_FAIL("MES-8222"),
    // 没有可以进行拣配的批次
    NO_LOT_TO_PICK("WMS-0090"),
    //接口
    INTERFACE_NOT_EXSIST("MES-8223"),
    INTERFACE_EXSIST("MES-8224"),
    FUTURE_SAMPLE_CONFIG_EXISTED("MES-8225"),
    EMPLOYEE_RIGHT_INFO_NOT_EXIST("MES-80021"),
    EMPLOYEE_RIGHT_INFO_EXISTED("MES-80022"),
    MACHINE_NAMES_NOT_EXISTED("MES-80023"),

    ALARM_RULE_NOT_EXISTED("MES-8226"),
    LOT_NAME_NOT_EMPTY("MES-8227"),
    PRODUCT_FUTUREHOLD_NOT_EXISTED("MES-8228"),
    PROD_HOLD_INFO_EXIST("MES-8229"),
    PROD_NM_EXIST("MES-8230"),
    PRODS_PROD_SPEC_NOT_SAME("MES-82049"),
    PRODS_PRCS_NOT_SAME("MES-82027"),
    PRODS_PRCS_OP_NOT_SAME("MES-82049"),
    CARR_CAPACITY_INSUFFICICENT("MES-82029"),
    LOT_NM_GENERATE_FAIL("MES-82030"),
    RPC_EXCEPTION("MES-82031"),
    LOGIN_ID_NOT_EXIST("MES-82032"),
    EMP_ID_EXISTED("MES-82033"),
    PRCS_OR_OP_EMPTY("MES-82034"),
    NO_OPERATION_MATCH_CONDITION("MES-82035"),
    // 考试计划已存在
    TEST_ARRANGEMENT_EXIST("MES-8091"),
    // 考试计划不存在
    TEST_ARRANGEMENT_NOT_EXIST("MES-8092"),
    // 题库中题目数量不够
    QUESTION_NOT_ENOUGH("MES-8093"),
    // 该部门下没有员工
    DEPT_NO_EMPLOYEE("MES-8094"),
    MACH_RRP_ID_NOT_EXIST("MES-82036"),
    MACH_RRP_ST_CHANGE_ERROR("MES-82037"),
    MACH_RRP_ST_ERROR("MES-82038"),
    // 该考试已取消
    TEST_CANCEL("MES-0093"),
    MACH_SPOT_CHECK_PLAN_EXISTED("MES-82039"),
    SHIP_ORDER_DETAIL_NOT_MEET_CONDITION("MES-82042"),
    SHIP_ODRDER_DETAIL_CAPACITY_NOT_ENOUGH("MES-82043"),
    LOT_NOT_PROD("MES-82044"),
    OP_NO_TYPE("MES-82045"),
    OP_NOT_LOT("MES-82046"),
    OP_NOT_PRODUCT("MES-82047"),
    MATERIAL_EXIST("MES-82048"),
    // 备件规格不存在
    PART_SPEC_NOT_EXIST("PMS-0001"),
    // 备件规格绑定关系不存在
    PART_SPEC_GROUP_NOT_EXIST("PMS-0002"),
    // 备件安全库存配置已存在
    PART_SAFE_EXISTS("PMS-0003"),
    // 备件安全库存配置不存在
    PART_SAFE_NOT_EXIST("PMS-0004"),
    // 备件领用申请单不存在
    PART_REQUEST_NOT_EXIST("PMS-0005"),
    // 备件领用申请详单不存在
    PART_REQUEST_DETAIL_NOT_EXIST("PMS-0006"),
    // 无对应服务器的授权码
    NO_LICENSE("MES-0094"),
    // 本机序列与授权码序列不符
    UUID_NOT_MATCH("MES-0095"),
    // 授权码签名验证失败
    LICENSE_VERIFY_FAILED("MES-0097"),
    // 授权码解析失败
    LICENSE_ANALYSIS_FAILED("MES-0098"),
    // 系统注册人数已达最大值
    REGISTER_NUMBER_FULL("MES-0099"),
    EMPLOYEE_SHIFT_EXISTS("MES-0100"),
    SPOT_CHECK_STATE_NO_TOBEVALISHED("PMS-0007"),
    // CFM配置信息与机台信息不匹配
    CFM_DATA_NOT_MATCH("MES-0104"),
    RESTORABLE_NUM_LESS_THAN_EXPECTATION_NUM("MES-82050"),
    IS_ONETOONE_MACHINE_PORT_ID_AND_STOCKER_NAME("DSP-0001"),
    VALID_SETCOUNT("DSP-0002"),
    LOT_PRCS_IS_FORCE_PRCS("MES-82051"),
    MAN_HOUR_RECORD_FAIL("MES-82052"),
    CARRY_DIRECTIVE_HAVE_BEEN_SENT("DSP-0003"),
    DSP_AUTO_DISPATCH_CONFIGURATION_EXISTS("DSP-0004"),
    DSP_AUTO_DISPATCH_CONFIGURATION_NOT_EXIST("DSP-0005"),
    DSP_PORT_WIP_BIND_EXISTS("DSP-0006"),
    DSP_PORT_WIP_BIND_NOT_EXISTS("DSP-0007"),
    DATA_VALUE_TYPE_ERROR("MES-82053"),
    DATA_VERIFICATION_ERROR("MES-20200714"),
    CANNOT_FIND_CURRENT_OPERATION("MES-82054"),
    THERE_IS_ONLY_ONE_VALUE_ABOUT_PRCSID_OR_PRCSOPID("MES-82055"),
    // 审核流流程定义不存在
    WORK_FLOW_NOT_EXIST("WF-0001"),
    // 审核流pencil配置不存在
    PENCIL_CONFIG_NOT_EXIST("WF-0002"),
    // 审核流activiti流程定义与自建表workflow流程定义的关联关系已存在
    PROCESS_MAPPING_EXIST("WF-0003"),
    // 审核流流程定义存在未完成流程，不能取消部署
    WORK_FLOW_ACTIVE("WF-0004"),
    // 审核流流程定义无流程图信息，无法部署
    WORK_FLOW_INCOMPLETE("WF-0005"),
    // 审核流流程已部署，不能删除
    WORK_FLOW_DEPLOYED("WF-0006"),
    // 审核流流程未部署，不能发起一个流程
    WORK_FLOW_NOT_DEPLOYED("WF-0007"),
    // 无权领取该任务
    NO_RIGHT_CLAIM_TASK("WF-0008"),
    // 无权委托（分配）该任务
    NO_RIGHT_DELEGATE_TASK("WF-0009"),
    // 无权处理该任务
    NO_RIGHT_PROCESS_TASK("WF-0010"),
    // 任务被挂起，无法操作
    TASK_SUSPEND("WF-0011"),
    // 任务不存在
    TASK_NOT_EXIST("WF-0012"),
    // 附件不存在
    ATTACHMENT_NOT_EXIST("WF-0013"),
    // 无权取消已认领的任务
    NO_RIGHT_CANCEL_CLAIM("WF-0014"),
    // IP格式不正确
    IP_FORMAT_ERROR("MES-0106"),
    // 审核要素名称重复
    LPA_ELEMENT_EXIST("WF-0015"),
    // 审核要素不存在
    LPA_ELEMENT_NOT_EXIST("WF-0016"),
    // 流程图解析出错
    PROCESS_PARSE_ERROR("WF-0017"),
    // 用户不存在
    USER_NOT_EXIST("WF-0018"),
    // 审核项名称已存在
    LPA_ITEM_EXIST("WF-0019"),
    // 审核项不存在
    LPA_ITEM_NOT_EXIST("WF-0020"),
    // 审核清单不存在
    LPA_LIST_NOT_EXIST("WF-0021"),
    // 审核清单名称存在
    LPA_LIST_EXIST("WF-0022"),
    // 审核层级已存在
    LPA_LEVEL_EXIST("WF-0023"),
    // 审核层级不存在
    LPA_LEVEL_NOT_EXIST("WF-0024"),
    // 审核组名称重复
    LPA_GROUP_EXIST("WF-0025"),
    // 审核组不存在
    LPA_GROUP_NOT_EXIST("WF-0026"),
    //采集参数已存在
    VRBL_EXIST("Scada-0001"),
    //采集参数不存在
    VRBL_NOT_EXIST("Scada-0002"),
    //采集参数类型已存在
    DATA_EXTRCT_TYP_EXIST("Scada-0003"),
    //采集参数类型不存在
    DATA_EXTRCT_TYP_NOT_EXIST("Scada-0004"),
    // 审核计划已存在
    LPA_PLAN_EXIST("WF-0027"),
    // 审核计划不存在
    LPA_PLAN_NOT_EXIST("WF-0028"),
    // 审核计划状态异常
    LPA_PLAN_STATE_ERROR("WF-0029"),
    // 审核清单无法删除，存在关联的已发布的审核计划
    LPA_LIST_DELETE_ERROR("WF-0030"),
    // 审核计划未对人员进行编制，无法发布！
    LPA_PLAN_PUBLISH_ERROR("WF-0031"),
    // 监控项目编号不能超过10个英文字母或数字
    ALARM_ITEM_CODE_CANNOT_EXCEED("MES-8897"),
    // 流程图的定时器格式错误
    DUEDATE_FORMAT_ERROR("WF-0032"),
    // 审核任务不存在
    LPA_TASK_NOT_EXIST("WF-0033"),
    //监控项目已存在
    ALARM_ITEM_CONFIG_EXIST("MES-8898"),
    // 审核表未完全填报
    LPA_FORM_INCOMPLETE("WF-0034"),
    TEMPLATE_EXIST("MES-8899"),
    TEMPLATE_NOT_EXIST("MES-8900"),
    //批次的产品数量与产品表该批次的产品数量不一致
    LOT_UNIT_QUANTITY_NOT_MATCH("MES-8901"),
    LICENSE_VERSION_NOT_MATCH("MES-0107"),
    NOT_SYSTEM("MES-0108"),
    LICENSE_INVALID("MES-0110"),
    DEPARTMENT_EMPLOYEE_DELETE_ERROR("MES-0112"),
    TOP_DEPARTMENT_DELETE_ERROR("MES-0113"),
    DEPARTMENT_EMPLOYEE_UPDATE_ERROR("MES-0114"),
    DEPARTMENT_EMPLOYEE_UNBIND_ERROR("MES-0115"),
    EMPLOYEE_ARRANGEMENT_NOT_EXIST("MES-0116"),
    CANNOT_BE_GREATER_THAN("MES-9016"),
    PROCESS_PROPERTY_ERROR("MES-0120"),
    RECEIPT_ALREADY_HAVE_IQC("WMS-0091"),
    // 延期数量不能大于送检数量
    ACCEPT_QUANTITY_ERROR("WMS-0092"),
    // 点检项目名称重复
    SPOT_CHECK_ITEM_NAME_EXIST("PMS-0001"),
    // 点检模板名称重复
    SPOT_CHECK_LIST_NAME_EXIST("PMS-0008"),
    // 保养项名称重复
    MAINTAIN_ITEM_NAME_EXIST("PMS-0009"),
    // 保养清单名称重复
    MAINTAIN_LIST_NAME_EXIST("PMS-0010"),
    // 物料数量不足
    MATERIAL_QUANTITY_NOT_ENOUGH("MES-0121"),
    // 所有件次芯子数量总和大于批次中芯子数量
    PROD_COREQUANTITY_TO_MUCH("MES-0122"),
    // 机台物料不存在
    WIP_MTRL_ON_MCHN_NOT_EXISTED("MES-0123"),
    // 批次{{}}出账的载具{{}}有重复，请不要重复！
    LOT_TRACK_OUT_CARRIER_REPEAT("MES-0124"),
    // 合并的批次需要来自同一个入库批次
    LOT_MERGE_DIFFER_STOCK_IN("WMS-0093"),
    // 入库数量不正确
    STOCK_IN_QUANTITY_ERROR("WMS-0094"),
    // 盘点的规格没有库存，无法创建盘点单
    STOCK_COUNT_NO_LOT("WMS-0095"),
    // 无法创建过期的年度评价
    SUPPLIER_REVIEW_RESULT_EXPIRE("CORE-0003"),
    // 采购产品任务单关闭失败
    PURCHASE_TASK_CLOSE_ERROR("WMS-0096"),
    // 采购产品任务单撤销与撤销释放失败
    PURCHASE_TASK_CANCEL_ERROR("WMS-0097"),
    // 采购计划单撤销与撤销释放失败
    PURCHASE_PLAN_CANCEL_ERROR("WMS-0098"),
    // 采购计划单关闭失败
    PURCHASE_PLAN_CLOSE_ERROR("WMS-0099"),
    // 采购合同撤销与撤销释放失败
    PURCHASE_CONTRACT_CANCEL_ERROR("WMS-0100"),
    // 采购订单撤销与撤销释放失败
    PURCHASE_ORDER_CANCEL_ERROR("WMS-0101"),
    // {}当前状态{}错误，需要{}
    CURRENT_STATE_ERROR("MES-0125"),
    // 铭牌打印模板不存在{}
    TP_TPM_NOT_EXIST("MES-0126"),
    // 铭牌打印模板配置错误，匹配多个{}
    TP_TPM_NOT_REPETED("MES-0127"),
    // 生产指令单的采购单状态错误{}
    ORDER_PURCHASE_STATE_ERROR("MES-0128"),
    // 生产指令单的采购单ID为空
    ORDER_PURCHASE_NULL("MES-0129"),
    // 收货单下的已有收货详情项目进行了送检或入库，无法撤销
    RECEIPT_CANCEL_ERROR("WMS-0102"),
    // 采购送检单已有检验单，无法删除
    PURCHASE_CHECKLIST_DELETE_ERROR("WMS-0103"),
    // 库存数量不足，原有{{}}，需要{{}}
    STOCK_QUANTITY_NOT_ENOUGH("WMS-0104"),
    // 备料计划{{}}存在备料单，不能进行此操作
    PLAN_PREPARE_MATERIAL_EXIST("WMS-0105"),
    // 生产任务{{}}存在备料计划，不能进行此操作
    TASK_PLAN_MATERIAL_EXIST("WMS-0106"),
    // 发料单{{}}的工单{{}}未关闭,不允许关闭发料单
    STOCK_OUT_ORDER_NOT_CLOSE("WMS-0107"),
    // 校验不通过，存货编码{{}}不允许超发
    SPEC_NAME_NOT_OVER_ISSUE("WMS-0108"),
    // 生产任务{{}}在配料发料表中存在状态不为废弃的数据
    TASK_EXIST_NOT_CANCEL("WMS-0109"),
    // 存货编码{{}}发料超过限额，不允许操作
    SPEC_NAME_OVER_LIMIT("WMS-0110"),
    // 发料申请祥单{{}}发料超过限额，不允许操作！可发（需求数量-已出库数量）{{}}，计划发{{}}
    REQUEST_DETAIL_OVER_LIMIT("WMS-0111"),
    //{{}}入库失败
    STORAGE_FAIL("WMS-0112"),
    // 该生产指令单{{}}不存在站点信息
    ORDER_NOT_EXIST_SITE("MES-133"),
    // 出账自动入库失败
    MOVE_OUT_AUTO_STOCK_IN_ERROR("WMS-0113"),
    WIP_VALID_STOCK_QUANTITY_CHECK("MES-0133"),
    // 微信模板获取失败，微信公众号配置不正确
    WECHAT_TEMPLATE_FETCH_ERROR("MES-0134"),
    // 检验结果与项目结果不相符
    RESULT_AND_ITEM_NOT_ACCORD("MES-0135"),
    // 当前芯组列表中没有芯子
    CORE_NOT_EXIST_IN_GROUP("MES-0136"),
    // 库存批次发生变化，需要重新进行齐套计算，存货编码：{{}}库存数量不足
    STOCK_QUANTITY_CHANGE("MES-0137"),
    // 所加物料不满足当前生产指令单{{}}的存货编码
    MATERIAL_NOT_ACCORD_ORDER_SPEC_NAME("MES-0138"),
    // 上料数量大于可用数量！
    FEED_QUANTITY_GREATER_AVAILABLE_QUANTITY("MES-0139"),
    // 所选物料在工位物料表中已存在。
    MATERIAL_ALREADY_EXIST("MES-0140"),
    // 该物料入库仓库不正确，无法出账！
    MATERIAL_WAREHOUSE_ERROR("MES-0141"),
    // 指令单{{}}对应工序不存在
    ORDER_PROCESS_NOT_EXIST("MES-0142"),
    // 生产指令单{{}}的存货编码在存货编码表不存在
    ORDER_SPEC_NOT_IN_SPECTABLE("MES-143"),
    // 所选部件没有可用的bom配置
    PART_NO_BOM("MES-0144"),
    // 齐套任务相关的单据请删除对应的任务单来关联删除
    PART_ORDER_DELETE_ERROR("MES-0145"),
    // 工艺{{}}中有重复的站点{{}}
    PROCESS_OPERATION_REPEATE("MES-0146"),
    // 工序、厂别、工序类型不能为空或空格
    PROCESS_OPERATION_DEFINE_NOT_NULL("MES-0147"),
    // 申请单详情{{}} 的 本次出库批次数量 {{}} 大于 需求数量 {{}} - 已出库数量 {{}} 不能出库。
    STOCKOUT_LOT_QUANTITY_ERROR("WMS-0116"),
    // 选取的数据中：仓库识别码、存货编码、编码版本、供应商代码、供应商名称可能存在空值，请重新选择。
    DATA_EXISTS_NULL_VALUE("WMS-0114"),
    // 上一工序{{}} 中有零件未提交，请先到上一工序提交。
    LAST_OPERATION_NOT_SUBMIT("MES-0148"),
    // 仓库编码、存货编码、存货版本、存货类别、存货类型不能为空或空格
    WAREHOUSE_DRAFT_IMPORT_ERROR("WMS-0151"),
    // 仓库编码{{}}不存在！
    WAREHOUSE_CODE_NOT_EXIST("WMS-0152"),
    // 库区编码{{}}不存在！
    BLOCK_CODE_NOT_EXIST("WMS-0154"),
    // 库位编码{{}}不存在！
    SHELF_CODE_NOT_EXIST("WMS-0156"),
    // {{}}历史不存在，请按正确流程操作！
    HISTORY_NOT_EXIST("WMS-0157"),
    // 存货编码未配置生产工艺！
    SPEC_NO_PROCESS("MES-0149"),
    // 用户{{}}在工序{{}}中没权限！
    USER_OPERATION_NO_PERMISSION("MES-0150"),
    // bom配置错误，单位使用量为空
    BOM_NO_USE_AMOUNT("MES-0151"),
    // 作业流程单数据生成失败
    NO_PROCESS_PROPERTY("MES-0152"),
    // 库存批次{{}} 的 库存数量 {{}} 小于 本次出库数量 {{}} ， 不能出库。
    STOCKOUT_LOT_QUANTITY_ZERO("WMS-0158"),
    // 领料申请创建时，领料数量必须至少有一个大于0
    PICK_QUANTITY_ERROR("MES-0153"),
    // 数据{{}}重复！
    DATA_REPEATED("MES-82057"),
    DATA_MATCH_ERROR("MES-82058"),
    MTRL_DEDUCT_FAIL("MES-82059"),
    //  出库数量{{}}不能大于申请详情需求数量{{}}
    STK_QT_OVR("MES-82060"),
    //拆批错误
    SPLIT_ERR("MES-82061"),
    //申请单详情{{}}本次发料数量超出剩余需求量
    ISSUE_QT_OVER("MES-82062"),
    //创建出库单失败
    STK_OUT_CREATE_FAIL("WMS-0159"),
    //远程调用物料接收上工位失败
    RECEIVE_FAIL("WMS-0160"),
    RECEIPT_NOT_EXIST("WMS-0158"),
    INSUFFICIENT_OF_MACHINE("MES-82065"),
    // 存货编码：{{}}尚未配置模板
    SPEC_NOT_TEM("WMS-0161"),
    PRDT_SPEC_INCONSISTENT("MES-82063"),
    // 批次号：{{}}在过程检验单中已存在
    LOT_IN_PROCESS_EXISTED("WMS-0162"),
    //流转单{{}}已经填报，撤销失败
    UNDO_FAILED("MES-82064"),
    // 模板编码：{{}}配置的打印模板不足以下传。
    TEM_CODE_NOT_ENOUGH("WMS-0163"),
    //钣金指令单{{}}零件编码{{}}详情的单据状态为{{}}，不可继续操作
    FLOW_FAILED("MES-82066"),
    // 领料单不满足ERP同步条件
    SYNC_ERP_STATE_ERROR("WMS-0162"),
    //存货编码为{{}}的所选批次信息已不存在
    MATERIAL_LOT_EXCEPTION("MES-82067"),
    //存货编码为{{}}的所选批次剩余数量已被改变
    MATERIAL_LOT_SUM_EXCEPTION("MES-82068"),
    //零件{{}}绑定工艺为{{}}，与流程不符
    PRCS_NOT_MATCH("MES-82069"),*/




    /*
     * @Date 9:29 2022-4-28
     * 通用错误码
     **/
    RIGHT_ALREADY_HAVE_PARENT("CORE-000029"),
    RIGHT_ALREADY_HAVE_CHILD("CORE-000030"),

    DATA_EXISTED("CORE-000031"),
    DATA_NOT_EXIST("CORE-000032"),
    //异常
    EXCEPTION("CORE-000033"),
    // 查询参数为空
    SEARCH_PARAM_NULL("CORE-000034"),
    STATE_MODEL_EXISTED("CORE-000035"),
    STATE_MODEL_NOT_EXISTED("CORE-000036"),
    STATE_CONVERT_ERROR("CORE-000037"),
    JSON_TO_ENTITY_FAIL("CORE-000038"),
    // 父级菜单不能设置为自身
    PARENT_MENU_CANNOT_SET_ITSELF("CORE-000039"),
    THE_DATE_HAS_ALREADY_EXISTED("CORE-000040"),
    SYSTEM_USER_DELETE_ERROR("CORE-000041"),
    USER_PASSWORD_ERROR("CORE-000042"),
    // 没有上传任何文件
    UPLOADING_NO_FILES("CORE-000043"),
    // 空数据
    NO_DATA("CORE-000044"),
    // SQL 操作异常
    SQL_OP_ERROR("CORE-000045"),
    //角色已经存在
    ROLE_ALREADY_EXISTS("CORE-000046"),
    // 授权码已过期
    LICENSE_EXPIRED("CORE-000047"),
    IMPORTED_EXCEL_DUPLICATE_DATA("CORE-000048"),


    // 文件上传错误
    FILE_UPLOAD_ERROR("CORE-000049"),
    // 文件上传错误
    FILE_TYPE_ERROR("CORE-000050"),
    // 文件IO异常
    FILE_IO_EXCEPTION("CORE-000051"),

    // 文件删除错误
    FILE_DELETE_ERROR("CORE-000052"),
    // feign获取{{}}失败
    FEIGN_GET_FAIL("CORE-000053"),
    // {{}}厂别不在配置表中
    FCTRY_NOT_CONFIG("CORE-000054"),
    // {{}}区域不在配置表中
    AREA_NOT_CONFIG("CORE-000055"),
    CHECK_OUT_ERROR("CORE-000056"),
    NO_CHECK_OUT("CORE-000057"),
    // 文件关联项删除异常！
    FILE_RELATION_DELETION_EXCEPTION("CORE-000058"),
    NAME_RULE_NOT_EXISTED("CORE-000059"),
    CONSTRAINT_VIOLATION("CORE-000060"),
    // 打印信息不存在
    PRNT_INFO_NOT_EXISTED("CORE-000061"),
    // 打印信息已存在
    PRNT_INFO_EXISTED("CORE-000062"),
    // feign获取命名规则生成的名称失败
    FEIGN_GET_NAME_FAIL("CORE-000063"),
    STATE_EXISTED("CORE-000064"),
    STATE_NOT_EXISTED("CORE-000065"),
    // feign调用失败
    FEIGN_ERROR("CORE-000066"),
    // 数据已失效
    DATA_HAS_DISABLE("CORE-000075"),
    // 数据已生效
    DATA_HAS_EFFECTIVE("CORE-000076"),
    // 该设备已绑定工序
    MACHINE_BIND_PROCESS("MODELER-F-000017"),
    // 	该设备已绑定生产区域
    MACHINE_BIND_AREA("MODELER-F-000016"),
    // 该设备已绑定设备口
    MACHINE_BIND_PORT("MODELER-F-000015"),
    // 该设备组已被设备绑定
    MACHINE_BIND_GROUP("MODELER-F-000021"),
    // 数据已冻结
    DATA_IS_ON_HOLD("CORE-000078"),
    // 数据非冻结
    DATA_IS_NOT_ON_HOLD("CORE-000079"),

    // 数据已废弃
    DATA_HAS_OBSOLETE("FabMDM-000005"),
    // 数据已生效
    DATA_HAS_NOT_OBSOLETE("FabMDM-000006"),
    // {{}}当前状态{{}}错误，应为{{}}
    CURRENT_STATE_ERROR("FabMES-000055"),
    ;

    private final String code;

    BaseErrorCode(final String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    @Override
    public String toString() {
        return "[" + getCode() + "]";
    }
}
