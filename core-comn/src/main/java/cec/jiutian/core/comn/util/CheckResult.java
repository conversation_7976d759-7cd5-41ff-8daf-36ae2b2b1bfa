package cec.jiutian.core.comn.util;

import java.util.List;

public class CheckResult {

    /**
     * 通用的检查数据库插入返回值并转化为布尔型判断的工具方法
     *
     * <AUTHOR>
     */
    public static boolean checkUpdateForInteger(int... nums) {
        for (int num : nums) {
            if (num != 1) {
                return false;
            }
        }
        return true;
    }

    /**
     * 通用的检查数据库插入返回值并转化为布尔型判断的工具方法
     *
     * <AUTHOR>
     */
    public static boolean checkUpdateForInteger(List<Integer> nums) {
        for (int num : nums) {
            if (num != 1) {
                return false;
            }
        }
        return true;
    }

    /**
     * 将布尔型的列表的判断转化为一个值
     *
     * <AUTHOR>
     */
    public static boolean checkBoolean(Boolean... boolList) {
        for (Boolean bool : boolList) {
            if (!bool) {
                return false;
            }
        }
        return true;
    }

    /**
     * 将布尔型的列表的判断转化为一个值
     *
     * <AUTHOR>
     */
    public static boolean checkBooleanForList(List<Boolean> boolList) {
        for (Boolean bool : boolList) {
            if (!bool) {
                return false;
            }
        }
        return true;
    }
}
