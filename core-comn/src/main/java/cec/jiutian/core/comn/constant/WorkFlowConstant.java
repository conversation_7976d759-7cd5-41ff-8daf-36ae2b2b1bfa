package cec.jiutian.core.comn.constant;

/**
 * 工作流模块常量接口
 *
 * <AUTHOR>
 * @date 2020/7/23
 */
public interface WorkFlowConstant {
    String PENCIL_TYPE_GLOBAL = "component";
    String PENCIL_TYPE_PARTIAL = "page";

    String PERMISSION_TYPE_OWNER = "owner";
    String PERMISSION_TYPE_ASSIGNED = "assigned";
    String PERMISSION_TYPE_CANDIDATE = "candidate";

    String PROCESS_STATE_FINISHED = "finished";
    String PROCESS_STATE_UNFINISHED = "unfinished";

    String START_OPTIONS_NORMAL = "normal";
    String START_OPTIONS_ASSIGNEE = "assignee";

    String CHANGE_COLOR = " bioc:stroke=\"#000\" bioc:fill=\"#FD9105\"";

    String TASK_COMPLETE_COLOR = " bioc:stroke=\"#000\" bioc:fill=\"#CCC\"";

    String PLAN_STATE_UNPUBLISHED = "unpublished";
    String PLAN_STATE_PUBLISHED = "published";
    String PLAN_STATE_ACTIVE = "active";

    String LPA_TASK_CREATE = "created";
    String LPA_TASK_PROCESSING = "processing";
    String LPA_TASK_DELETE = "deleted";
    String LPA_TASK_COMPLETED = "completed";
    String LPA_TASK_DELAY_COMPLETED = "delay_completed";

    String MAIL_KEY = "email";
    String WECHAT_KEY = "wechat";
    String TASK_KEY = "taskId";
    String EXECUTION_DATE_KEY = "executionDate";

    String LPA_PLAN_TYPE_MONTHLY = "monthly";
    String LPA_PLAN_TYPE_WEEKLY = "weekly";

    String LPA_TASK_DELETE_REASON_UNPUBLISH = "plan_unPublished";
    String LPA_TASK_DELETE_REASON_CANCEL = "task_cancel";

    String LPA_RESULT_PASS = "pass";
    String LPA_RESULT_FAIR = "fail";

    String LPA_ISSUE_STATE_PROCESSING = "processing";
    String LPA_ISSUE_STATE_SHELVE = "shelve";
    String LPA_ISSUE_STATE_COMPLETE = "complete";

    String LPA_ISSUE_RESULT_SOLVED = "solved";
    String LPA_ISSUE_RESULT_UNSOLVED = "unsolved";

    String PROCESS_VARIABLE_PRIORITY = "priority";
}
