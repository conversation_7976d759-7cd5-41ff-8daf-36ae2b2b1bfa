package cec.jiutian.core.comn.constant;

/**
 * <AUTHOR>
 * @date 2019/12/16
 * @description
 */
public enum ConfigApiEventName {
    // businessPartner
    CREATE_PARTNER("Create_Partner"),
    DELETE_PARTNER("Delete_Partner"),
    UPDATE_PARTNER("Update_Partner"),

    // enum
    CREATE_ENUM("Create_Enum"),
    UPDATE_ENUM("Update_Enum"),
    DELETE_ENUM("Delete_Enum"),

    // enumValue
    CREATE_ENUM_VALUE("Create_Enum_Value"),
    UPDATE_ENUM_VALUE("Update_Enum_Value"),
    DELETE_ENUM_VALUE("Delete_Enum_Value"),

    // errorCode
    ERROR_CODE_CREATE("Error_Code_Create"),
    ERROR_CODE_UPDATE("Error_Code_Update"),
    ERROR_CODE_DELETE("Error_Code_Delete"),

    // factoryCalendar
    FTYCAL_CRE("Calendar_Create"),
    FTYCAL_DEL("Calendar_Delete"),

    // factoryShift
    FACTORY_SHIFT_CREATE("Factory_Shift_Create"),
    FACTORY_SHIFT_UPDATE("Factory_Shift_Update"),
    FACTORY_SHIFT_DELETE("Factory_Shift_Delete"),

    // i18n
    I18N_CREATE("I18N_Create"),
    I18N_UPDATE("I18N_Update"),
    I18N_DELETE("I18N_Delete"),

    // nameRule
    CREATE_NAME_RULE("Create_Name_Rule"),
    UPDATE_NAME_RULE("Update_Name_Rule"),
    DELETE_NAME_RULE("Delete_Name_Rule"),

    // print
    PRINT_HTML_CREATE("Print_Html_Create"),
    PRINT_HTML_UPDATE("Print_Html_Update"),
    // interface
    INTERFACE_CREATE("Interface_Create"),
    INTERFACE_UPDATE("Interface_Update"),
    INTERFACE_DELETE("Interface_Delete"),

    // TestArrangement
    TEST_ARRANGEMENT_CREATE("Test_Arrangement_Create"),
    TEST_ARRANGEMENT_UPDATE("Test_Arrangement_Update"),
    TEST_ARRANGEMENT_DELETE("Test_Arrangement_Delete"),

    // employeeTestRecord
    TEST_RECORD_UPDATE("Test_Record_Update"),
    // employeeTestResult
    TEST_RESULT_UPDATE("Test_Result_Update");
    private String name;

    ConfigApiEventName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }
}
