package cec.jiutian.core.comn.constant;

/**
 * <AUTHOR>
 * @date 2019/12/16
 * @description carrier服务最后时间名称
 */
public enum CarrierApiEventName {
    // carrier
    CARRIER_CREATE("Carrier_Create"),
    CARRIER_UPDATE("Carrier_Update"),
    CARRIER_DELETE("Carrier_Delete"),
    CARRIER_RELEASE("Carrier_Release"),
    CARRIER_CANCEL_RELEASE("Carrier_Cancel_Release"),
    CARRIER_TRANSFER("Carrier_Transfer"),
    DO_UPDATE_CARRIER_PROPERTY("Do_Update_Carrier_Property"),
    DO_ONHOLD_CARRIER("Do_OnHold_Carrier"),
    DO_NOT_ONHOLD_CARRIER("Do_Not_OnHold_Carrier"),
    DO_CLEAN_CARRIER("Do_Clean_Carrier"),
    LOT_RELEASE("Lot_Release"),

    // carrierSpecification
    CARRIER_SPEC_CREATE("Carrier_Spec_Create"),
    CAR<PERSON><PERSON>_SPEC_UPDATE("Carrier_Spec_Update"),
    CARRIER_SPEC_DELETE("Carrier_Spec_Delete"),

    // carrierSpecificationProperty
    CARRIER_SPEC_PROP_CREATE("Carrier_Spec_Prop_Create"),
    CARRIER_SPEC_PROP_UPDATE("Carrier_Spec_Prop_Update"),
    CARRIER_SPEC_PROP_DELETE("Carrier_Spec_Prop_Delete"),

    // durable
    DURABLE_CREATE("Durable_Create"),
    DURABLE_DELETE("Durable_Delete"),
    DURABLE_UPDATE("Durable_Update"),
    DURABLE_MAINTAIN("Durable_Maintain"),
    DURABLE_SCRAP("Durable_Scrap"),

    ;
    private String name;

    CarrierApiEventName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }
}
