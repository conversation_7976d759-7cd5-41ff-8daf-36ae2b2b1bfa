package cec.jiutian.core.comn.constant;

public interface WipConstant {
    String DATE_FORMAT_YMDHMSS = "yyyyMMddHHmmSSS";
    String STATE_INPRODUCTION = "InProduction";
    String STATE_WAIT = "Wait";
    String STATE_SHIPPED = "Shipped";
    String STATE_CREATE = "Create";
    String STATE_CREATED = "Created";
    String STATE_RELEASED = "Released";
    String STATE_INUSE = "InUse";
    String STATE_NOTONHOLD = "NotOnHold";
    String STATE_ONHOLD = "OnHold";
    String STATE_SCRAPED = "Scraped";
    String STATE_COMPLETED = "Completed";
    String STATE_RUN = "Run";
    String STATE_EMPTY = "Empty";
    String STATE_AVAILABLE = "Available";

    String PROCESS_OPERATION_END = "End";
    String ACTIVE_ENABLE_STATE_DISABLED = "Disabled";
    String STATE_UNITUNTRACKIN = "UnitUnTrackIn";
    String STATE_UNITSHIP = "UnitShip";
    String STATE_UNITTRACKOUT = "UnitTrackOut";
    String STATE_UNIT_CANCEL_SHIP = "Unit_Cancel_Ship";
    String LOT_NAME_RULE = "LotName";
    String PROCESS_GROUP_NAME = "ProcessGroupName";

    String UPDATE_TYPE_SCRAPED = "Scraped";
    String UPDATE_TYPE_CANCEL = "CancelScraped";

    String IN_REWORK = "InRework";
    String NOT_IN_REWORK = "NotInRework";
    String PENDING = "Pending";

    String PRCS_GRP_TYP_BOX = "Box";
    String PRCS_GRP_TYP_PALLET = "Pallet";

    String PRCS_GRP_MTRL_TYP_BOX = "Box";
    String PRCS_GRP_MTRL_TYP_LOT = "Lot";
    String PRCS_GRP_MTRL_TYP_PRODUCT = "Product";

    String QTIME_HOLD_REASON_CODE = "ProductQtimeHold";
    String QTIME_HOLD_REASON_CODE_TYPE = "MesHold";

    String GENERAL_FUTURE_HOLD = "GeneralFutureHold";
    String MES_FUTURE_HOLD = "MesFutureHold";
    String GENERAL_FUTURE_HOLD_DS = "正常预暂留";

    String SPECIAL_FUTURE_HOLD = "SpecialFutureHold";
    String SPECIAL_FUTURE_HOLD_DS = "特殊预暂留";

    String NAME_RULE_TYP_TIME = "Time";
    String NAME_RULE_TYP_VARIABLE = "Variable";

//	String MSG_HOLD_SUCCESS = "HOLD SUCCESS";
//	String MSG_HOLD_FAIL = "HOLD FAIL";
//	String MSG_NO_HOLD = "NO HOLD";

    String PRODUCT_STATE_RELEASE = "Release";
    String PRODUCT_STATE_SHIP = "Ship";
    String PRODUCT_STATE_UNSHIP = "UnShip";

}
