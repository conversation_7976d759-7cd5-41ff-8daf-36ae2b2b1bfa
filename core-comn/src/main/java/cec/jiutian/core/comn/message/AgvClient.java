package cec.jiutian.core.comn.message;

/**
 * @title: AgvClient.java
 * @package cec.jiutian.mes.core.comn.message
 * @description: agv
 * @author: <EMAIL>
 */
public interface AgvClient {

    String AGVINPUT = "inputFromAgv";

    String AGVOUTPUTBYTEST = "outputFromAgvByTest";

/*    @Input(AgvClient.AGVINPUT)
    SubscribableChannel inputFromAgv();

    @Output(AgvClient.AGVOUTPUTBYTEST)
    MessageChannel outputFromAgvByTest();*/

}
