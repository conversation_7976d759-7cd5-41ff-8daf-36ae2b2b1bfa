package cec.jiutian.core.comn.constant;

public enum WipApiEventName {
    PRODUCTION_CREATE("Production_create"),
    PRODUCTION_UPDATE("Update_Production"),
    PRODUCTION_SCRAP("Production_Scrap"),
    PRODUCTION_CANCEL_SCRAP("Production_Cancel_Scrap"),
    PRODUCTION_HOLD("Production_Hold"),
    PRODUCTION_CANCEL_HOLD("Production_Cancel_Hold"),
    LOT_CREATE("Lot_Create"),
    LOT_CANCEL_INPRODUCTION("Lot_Cancel_InProduction"),
    LOT_TRACK_OUT("Lot_Track_Out"),
    LOT_JUMP_STATION("Lot_Jump_Station"),
    LOT_RELEASE("Lot_Release"),
    LOT_INPRODUCTION("Lot_InProduction"),
    LOT_SHIP("Lot_Ship"),
    LOT_CANCEL_SHIP("Lot_Cancel_Ship"),
    LOT_TRACK_IN("Lot_Track_In"),
    LOT_MERGE("Lot_Merge"),
    LOT_SPLIT("Lot_Split"),
    SPLIT("Split"),
    RECEIVED_LOT("Received_Lot"),
    CANCEL_RECEIVED_LOT("Cancel_Received_Lot"),
    LOTUNTRACKIN("Lot_Un_Track_In"),
    CANCEL_FUTURE_HOLD("Cancel_Future_Hold_Released"),
    FUTURE_HOLD("Future_Hold"),
    AUTOMATIC_SAMPLE_CONFIGURATION_CREATE("Automatic_Sample_Configuration_Create"),
    AUTOMATIC_SAMPLE_CONFIGURATION_DELETE("Automatic_Sample_Configuration_Delete"),
    AUTOMATIC_SAMPLE_CONFIGURATION_UPDATE("Automatic_Sample_Configuration_Update"),
    FUTURE_SAMPLE_CONFIGURATIONS_CREATE("Future_Sample_Configurations_Create"),
    FUTURE_SAMPLE_CONFIGURATIONS_DELETE("Future_Sample_Configurations_Delete"),
    FORCESAMPLERECORDING_CREATE("Force_Sample_Recording_Create"),
    FORCESAMPLERECORDING_DELETE("Force_Sample_Recording_Delete"),
    DO_NOT_ON_HOLD("Do_Not_On_Hold"),
    DO_REWORK("Do_Rework"), USERPROFILE_CREATE("User_Profile_Create"),
    USERPROFILE_UPDATE("User_Profile_Update"),
    USERPROFILE_UPDATE_LANG("User_Profile_Update_Lang"),
    USERPROFILE_UPDATE_MENU_ON_TOP("User_Profile_Update_Menu_On_Top"),
    USERPROFILE_UPDATE_UI_STYLE("User_Profile_Update_Ui_Style"),
    USERPROFILE_UPDATE_CLEAR_CH_IN("User_Profile_Update_Clear_Ch_In"),
    USERPROFILE_UPDATE_FAVORITE_MODULE_LIST("User_Profile_Update_Favorite_module_List"),
    USERPROFILE_UPDATE_DEFAULT_MODULE("User_Profile_Update_Default_module"),
    BOX_TRACK_IN("Box_Track_In"),
    BOX_TRACK_OUT("Box_Track_Out"),
    WIP_PROCESS_GROUP_CREATE("Wip_Process_Group_Create"),
    INNER_BOX_PACKING("Inner_Box_Packing"),
    INNER_BOX_UNPACKING("Inner_Box_Unpacking"),
    OUT_BOX_PACKING("Out_Box_Packing"),
    OUT_BOX_UNPACKING("Out_Box_Unpacking"),
    PALLET_SHIP("Pallet_Ship"),
    PALLET_UNSHIP("Pallet_Unship"),
    QTIME_CREATE("QTime_Create"),
    QTIME_UPDATE("QTime_Update"),
    QTIME_DELETE("QTime_Delete"),
    PRODUCT_TRACK_IN("Product_Track_In"),
    PRODUCT_TRACK_OUT("Product_Track_Out"),
    PRODUCT_REWORK_ATTACH("Product_Rework_Attach"),
    PRODUCT_MATERIAL_REMOVE("Product_Material_Remove"),
    PALLET_PACKING("Pallet_Packing"),
    PALLET_UNPACKING("Pallet_UnPacking"),
    LOT_REJUDGE("Lot_Rejudge"),
    LOT_JUDGE("Lot_Judge"),
    PRODUCT_JUDGE("Product_Judge"),
    PROD_WITH_NO_LOT_HOLD("Produ_With_No_Lot_Hold"),
    PROD_WITH_NO_LOT_CANCEL_HOLD("Produ_With_No_Lot_Cancel_Hold"),
    PROD_WITH_NO_LOT_FUTURE_HOLD("Produ_With_No_Lot_Future_Hold"),
    PROD_WITH_NO_LOT_CANCEL_FUTURE_HOLD("Produ_With_No_Lot_Cancel_Hold"),
    ASSIGN_CARRIER("Assign_Carrier"),
    DEASSIGN_CARRIER("Deassign_Carrier"),
    CREATE("Create"),
    OUT_BOUND("Outbound"),
    TRACK_IN("TrackIn"),
    CANCEL_TRACK_IN("CancelTrackIn"),
    ;
    private String name;

    WipApiEventName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }
}
