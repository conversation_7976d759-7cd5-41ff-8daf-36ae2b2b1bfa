package cec.jiutian.core.comn.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2020/6/2
 */
@Aspect
@Component
@Slf4j
public class ServiceLog {
    private static final transient String LINE_BREAK = System.lineSeparator();
    private static final transient int MAX_LOG_LENGTH = 32766;
    @Autowired
    private ObjectMapper jsonMapper;

    @Pointcut("execution(* cec.jiutian.*..service..*.*(..))")
    public void pointCut() {
    }

    @Around("pointCut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        Object[] args = joinPoint.getArgs();
        StringBuilder sb = new StringBuilder();
        sb.append(LINE_BREAK).append("----------------------------").append(LINE_BREAK);
        sb.append("Method: ").append(joinPoint.getSignature().getDeclaringType()).append(".").append(joinPoint.getSignature().getName()).append(LINE_BREAK);
        try {
            sb.append("Parameter: ").append(jsonMapper.writeValueAsString(args)).append(LINE_BREAK);
        } catch (Exception e) {
            sb.append("Parameter: ").append("WARNING! The parameter is not serializable!").append(LINE_BREAK);
        }
        long startTime = System.currentTimeMillis();
        Object obj = joinPoint.proceed();
        long proceedTime = System.currentTimeMillis() - startTime;
        sb.append("----------------------------").append(LINE_BREAK);
        sb.append("Response Time: ").append(proceedTime).append(LINE_BREAK);
        try {
            // 此段代码用于排查return数据大于32766的接口，之后会重写。
            String returnBody = jsonMapper.writeValueAsString(obj);
            if (returnBody.length() > MAX_LOG_LENGTH) {
                /* 暂时不记录长日志
                try {
                    FileOutputStream fileOutputStream = new FileOutputStream("/long_log.txt",true);
                    fileOutputStream.write((returnBody+LINE_BREAK+LINE_BREAK).getBytes());
                    fileOutputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
                 */
                sb.append("Return: The length of the returned data exceeds 32766!").append(LINE_BREAK);
            } else {
                sb.append("Return: ").append(returnBody).append(LINE_BREAK);
            }
        } catch (Exception e) {
            sb.append("Return: ").append("WARNING! The result is not serializable!").append(LINE_BREAK);
        }
        sb.append("----------------------------");
        log.info(sb.toString());
        return obj;
    }
}
