package cec.jiutian.core.comn.constant;

/**
 * @description: 功能说明（jira task id）
 * @author: chenjx
 * @date: 20220819
 */
public class WipInventoryConstant {
    public interface HoldState{
        String STATE_NOT_ONHOLD = "NotOnHold";
        String STATE_ONHOLD = "OnHold";
    }
    public interface ProcessState{
        String RUN = "RUN";
        String WAIT = "WAIT";
    }

    public interface CurrentState{
        /**
         * 开立
         */
        String Edit = "EDIT";
        /**
         * 待执行
         */
        String New = "NEW";
        /**
         * 执行中
         */
        String Active = "ACTIVE";
        /**
         * 关闭
         */
        String Close = "CLOSE";
        /**
         * 完工
         */
        String Complete = "COMPLETE";

        /**
         * 报废
         */
        String Scrap = "SCRAP";
    }
}
