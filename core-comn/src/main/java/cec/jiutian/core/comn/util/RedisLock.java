package cec.jiutian.core.comn.util;

import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import java.util.UUID;

public class RedisLock {

    private JedisPool jedisPool;

    private String distributedLockKey = "REIDS_LOCK";

    public RedisLock(JedisPool jedisPool) {
        this.jedisPool = jedisPool;
    }

    /**
     * <p>获取锁</p>
     *
     * @param acquireTimeout
     * @param timeOut
     * @return
     * <AUTHOR>
     */
    public String getLock(Long acquireTimeout, Long timeOut) {
        Jedis conn = null;
        try {
            conn = jedisPool.getResource();
            String identifierValue = UUID.randomUUID().toString();
            int expireLock = (int) (timeOut / 1000);
            Long endTime = System.currentTimeMillis() + acquireTimeout;
            while (System.currentTimeMillis() < endTime) {
                if (conn.setnx(distributedLockKey, identifierValue) == 1) {
                    conn.expire(distributedLockKey, expireLock);
                    return identifierValue;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (conn != null) {
                conn.close();
            }
        }
        return null;
    }

    /**
     * <p>释放锁</p>
     *
     * @param identifierValue
     * @return
     * <AUTHOR>
     */
    public boolean unLock(String identifierValue) {
        Jedis conn = null;
        boolean flag = false;
        try {
            conn = jedisPool.getResource();
            if (conn.get(distributedLockKey).equals(identifierValue)) {
                conn.del(distributedLockKey);
                flag = true;
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (conn != null) {
                conn.close();
            }
        }
        return flag;
    }
}
