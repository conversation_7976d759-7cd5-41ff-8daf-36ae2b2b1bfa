/**
 * projectName: mes-core-comn
 * fileName: FileTypeCheckUtils.java
 * packageName: cec.jiutian.mes.core.comn.util
 * date: 2020年7月9日上午10:08:48
 * copyright(c) 2020,cecjt
 */

package cec.jiutian.core.comn.util;

import org.springframework.web.multipart.MultipartFile;

/**
 * @title: FileTypeCheckUtils.java
 * @package cec.jiutian.mes.core.comn.util
 * @description: TODO
 * @author: Administrator
 * @date: 2020年7月9日 上午10:08:48
 * @version: V1.0
 */

public class FileTypeCheckUtils {

    public static boolean checkExcel(MultipartFile file) {
        //首先判断是不是空的文件
        if (!file.isEmpty()) {
            //对文文件的全名进行截取然后在后缀名进行删选。
            int begin = file.getOriginalFilename().indexOf(".");
            int last = file.getOriginalFilename().length();
            //获得文件后缀名
            String a = file.getOriginalFilename().substring(begin, last);
            return a.endsWith(".xlsx") || a.endsWith(".xls");
        } else {
            return false;
        }
    }
}
