package cec.jiutian.core.comn.constant;

/**
 * <AUTHOR>
 * @date 2019/12/16
 * @description auth服务最后时间名称
 */
public enum AuthApiEventName {
    // employee
    SELECT_DEPT("Select_Dept"),
    CREATE_EMP("Create_Emp"),
    DELETE_EMP("Delete_Emp"),
    UPDATE_EMP("Update_Emp"),
    RESET_PWD("Reset_Pwd"),
    UPDATE_PWD("Update_Pwd"),

    // department
    CREATE_DEPT("Create_Dept"),
    DELETE_DEPT("Delete_Dept"),
    UPDATE_DEPT("Update_Dept"),
    SELECT_EMP("Select_Emp"),
    UPDETE_DEPT_LEVEL("Updete_Dept_Level"),

    // departmentRight
    DEPT_RIGHT_CANCEL("Dept_Right_Cancel"),
    DEPT_RIGHT_ADDED("Dept_Right_Added"),

    // employeeRight
    EMP_RIGHT_CANCEL("Emp_Right_Cancel"),
    EMP_RIGHT_ADDED("Emp_Right_Added"),

    // loginHist
    LOGIN_HIST_INSERT("Login_Hist_Insert"),
    LOGOUT_HIST_INSERT("Logout_Hist_Insert"),

    // modRight
    CREATE_RIGHT_MOD("Create_Right_Mod"),
    UPDATE_RIGHT_MOD("Update_Right_Mod"),
    DELETE_RIGHT_MOD("Delete_Right_Mod"),

    // menu
    CREATE_MOD("Create_Mod"),
    DELETE_MOD("Delete_Mod"),
    UPDATE_MOD("Update_Mod"),

    // rightType
    ADD_RIGHT_TYPE("Add_Right_Type"),
    UPDATE_RIGHT_TYPE("Update_Right_Type"),
    DELETE_RIGHT_TYPE("Delete_Right_Type"),

    // role
    CREATE_ROLE("Create_Role"),
    UPDATE_ROLE("Update_Role"),
    DELETE_ROLE("Delete_Role"),

    // roleRight
    CREATE_ROLE_RIGHT_INFO("Create_Role_Right_Info"),

    CREATE_DOMAIN("Create_Domain"),
    UPDATE_DOMAIN("Update_Domain"),
    DELETE_DOMAIN("Delete_Domain"),

    UPDATE_ARRANGEMENT("Update_Arrangement"),
    DELETE_ARRANGEMENT("Delete_Arrangement"),

    ;
    private String name;

    AuthApiEventName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }
}
