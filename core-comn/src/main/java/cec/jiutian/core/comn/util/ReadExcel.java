package cec.jiutian.core.comn.util;

import org.apache.poi.POIXMLDocument;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.openxml4j.opc.OPCPackage;
import org.apache.poi.poifs.filesystem.POIFSFileSystem;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.io.PushbackInputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
public class ReadExcel {
    // 总行数
    private static int totalRows = 0;
    // 总条数
    private static int totalCells = 0;
    // 错误信息接收器
    private static String errorMsg;

    // 构造方法
    public ReadExcel() {
    }

    /**
     * 读EXCEL文件，获取信息集合
     *
     * @param fielName
     * @return
     */
    public static List<Map<String, Object>> getExcelInfo(MultipartFile mFile) {
        String fileName = mFile.getOriginalFilename();// 获取文件名
//        List<Map<String, Object>> userList = new LinkedList<Map<String, Object>>();
        try {
            if (!validateExcel(fileName)) {// 验证文件名是否合格
                return null;
            }
            boolean isExcel2003 = true;// 根据文件名判断文件是2003版本还是2007版本
            if (isExcel2007(fileName)) {
                isExcel2003 = false;
            }
            return createExcel(mFile.getInputStream(), isExcel2003);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 根据excel里面的内容读取客户信息
     *
     * @param is          输入流
     * @param isExcel2003 excel是2003还是2007版本
     * @return
     * @throws IOException
     */
    public static List<Map<String, Object>> createExcel(InputStream is, boolean isExcel2003) {
        try {
            Workbook wb = null;
            if (isExcel2003) {// 当excel是2003时,创建excel2003
                wb = new HSSFWorkbook(is);
            } else {// 当excel是2007时,创建excel2007
                wb = new XSSFWorkbook(is);
            }
            return readExcelValue(wb);// 读取Excel里面的信息
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 读取Excel里面客户的信息
     *
     * @param wb
     * @return
     */
    public static List<Map<String, Object>> readExcelValue(Workbook wb) {
        // 得到第一个shell
        Sheet sheet = wb.getSheetAt(0);
        // 得到Excel的行数
        ReadExcel.totalRows = sheet.getPhysicalNumberOfRows();
        // 得到Excel的列数(前提是有行数)
        if (totalRows > 1 && sheet.getRow(0) != null) {
            ReadExcel.totalCells = sheet.getRow(0).getPhysicalNumberOfCells();
        }
        List<Map<String, Object>> userList = new ArrayList<Map<String, Object>>();
        // 循环Excel行数

        List<String> list = new ArrayList<String>();

//		for (int i = 0; i < totalCells; i++) {
//			Row row = sheet.getRow(i);
//			for (int j = 0; j < totalRows; j++) {
//				Cell cell = row.getCell(j);
//				cell.setCellType(Cell.CELL_TYPE_STRING);
//			}
//
//		}

        Row row0 = sheet.getRow(0);
        for (int r = 0; r < totalCells; r++) {
            Cell cell0 = row0.getCell(r);
            list.add(cell0.getStringCellValue());
        }


        for (int r = 1; r < totalRows; r++) {
            Row row = sheet.getRow(r);
            if (row == null) {
                continue;
            }
            // 循环Excel的列
            Map<String, Object> map = new HashMap<String, Object>();
            for (int c = 0; c < ReadExcel.totalCells; c++) {
                Cell cell = row.getCell(c);
                if (null != cell) {
                    cell.setCellType(Cell.CELL_TYPE_STRING);
                    map.put(list.get(c), cell.getStringCellValue());// 名称
//                    if (c == 0) {
//                        // 如果是纯数字,比如你写的是25,cell.getNumericCellValue()获得是25.0,通过截取字符串去掉.0获得25
//                        if (cell.getCellType() == HSSFCell.CELL_TYPE_NUMERIC) {
//                            String name = String.valueOf(cell.getNumericCellValue());
//                            map.put("name", name.substring(0, name.length() - 2 > 0 ? name.length() - 2 : 1));// 名称
//                        } else {
//                            map.put("name", cell.getStringCellValue());// 名称
//                        }
//                    } else if (c == 1) {
//                        if (cell.getCellType() == HSSFCell.CELL_TYPE_NUMERIC) {
//                            String sex = String.valueOf(cell.getNumericCellValue());
//                            map.put("sex",sex.substring(0, sex.length() - 2 > 0 ? sex.length() - 2 : 1));// 性别
//                        } else {
//                            map.put("sex",cell.getStringCellValue());// 性别
//                        }
//                    } else if (c == 2) {
//                        if (cell.getCellType() == HSSFCell.CELL_TYPE_NUMERIC) {
//                            String age = String.valueOf(cell.getNumericCellValue());
//                            map.put("age", age.substring(0, age.length() - 2 > 0 ? age.length() - 2 : 1));// 年龄
//                        } else {
//                            map.put("age", cell.getStringCellValue());// 年龄
//                        }
//                    }
                } else {
                    map.put(list.get(c), "null");// 名称
                }
            }
            // 添加到list
            userList.add(map);
        }
        return userList;
    }

    /**
     * 验证EXCEL文件
     *
     * @param filePath
     * @return
     */
    public static boolean validateExcel(String filePath) {
        if (filePath == null || !(isExcel2003(filePath) || isExcel2007(filePath))) {
            errorMsg = "文件名不是excel格式";
            return false;
        }
        return true;
    }

    // @描述：是否是2003的excel，返回true是2003
    public static boolean isExcel2003(String filePath) {
        return filePath.matches("^.+\\.(?i)(xls)$");
    }

    // @描述：是否是2007的excel，返回true是2007
    public static boolean isExcel2007(String filePath) {
        return filePath.matches("^.+\\.(?i)(xlsx)$");
    }

    // 获取总行数
    public int getTotalRows() {
        return totalRows;
    }

    // 获取总列数
    public int getTotalCells() {
        return totalCells;
    }

    // 获取错误信息
    public String getErrorInfo() {
        return errorMsg;
    }

    public Workbook createCommonWorkbook(InputStream inp) throws IOException, InvalidFormatException {

        // 首先判断流是否支持mark和reset方法，最后两个if分支中的方法才能支持
        if (!inp.markSupported()) {
            // 还原流信息
            inp = new PushbackInputStream(inp, 8);
        }
        // EXCEL2003使用的是微软的文件系统
        if (POIFSFileSystem.hasPOIFSHeader(inp)) {
            return new HSSFWorkbook(inp);
        }
        // EXCEL2007使用的是OOM文件格式
        if (POIXMLDocument.hasOOXMLHeader(inp)) {
            // 可以直接传流参数，但是推荐使用OPCPackage容器打开
            return new XSSFWorkbook(OPCPackage.open(inp));
        }

        return null;

    }

}
