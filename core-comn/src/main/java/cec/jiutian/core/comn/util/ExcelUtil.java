package cec.jiutian.core.comn.util;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.POIXMLDocument;
import org.apache.poi.hssf.usermodel.HSSFDateUtil;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.openxml4j.opc.OPCPackage;
import org.apache.poi.poifs.filesystem.POIFSFileSystem;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Comment;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.io.PushbackInputStream;
import java.lang.reflect.Field;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
public class ExcelUtil {

    public static Workbook createCommonWorkbook(InputStream inp) throws IOException, InvalidFormatException {

        // 首先判断流是否支持mark和reset方法，最后两个if分支中的方法才能支持
        if (!inp.markSupported()) {
            // 还原流信息
            inp = new PushbackInputStream(inp, 8);
        }
        // EXCEL2003使用的是微软的文件系统
        if (POIFSFileSystem.hasPOIFSHeader(inp)) {
            return new HSSFWorkbook(inp);
        }
        // EXCEL2007使用的是OOM文件格式
        if (POIXMLDocument.hasOOXMLHeader(inp)) {
            // 可以直接传流参数，但是推荐使用OPCPackage容器打开
            return new XSSFWorkbook(OPCPackage.open(inp));
        }

        return null;
    }

    public static String getCellValue(Row row, int cellNum) {
        Cell cell = row.getCell(cellNum - 1);
        if (null == cell) {
            return null;
        }
        cell.setCellType(Cell.CELL_TYPE_STRING);
        String cellValue = cell.getStringCellValue();
        return StringUtils.isNotBlank(cellValue) ? cellValue.trim() : null;
    }

    public static Float getFloatValue(Row row, int cellNum) {
        Cell c = row.getCell(cellNum - 1);
        if (c != null) {
            c.setCellType(Cell.CELL_TYPE_STRING);
            return Float.parseFloat(c.getStringCellValue());
        }
        return null;
    }

    public static Integer getIntegerValue(Row row, int cellNum) {
        Cell c = row.getCell(cellNum - 1);
        if (c != null) {
            c.setCellType(Cell.CELL_TYPE_STRING);
            return Integer.parseInt(c.getStringCellValue());
        }
        return null;
    }

    /**
     * <p>从excel中读取数据并转化为一个对象数组，主要用于各个功能的excel导入接口，该方法只适用于单sheet的情况，一次只对一种对象进行操作</p>
     * <p>该方法要求excel的表头以备注的形式将对象的属性信息填入对应单元格，即表格第一行的每个单元格需要添加批注，内容为对象的Java字段名（小写开头驼峰式）</p>
     *
     * @param file   excel文件
     * @param tClass 对象类型
     * @return 对象的数组，如果表格无数据则返回null
     */
    public static <T> List<T> readExcelToList(MultipartFile file, Class<T> tClass) throws InvalidFormatException, InstantiationException, IllegalAccessException, IOException {
        try {
            Workbook workbook = ExcelUtil.createCommonWorkbook(file.getInputStream());
            Sheet sheet = Objects.requireNonNull(workbook).getSheetAt(0);
            if (sheet != null) {
                Map<Integer, String> fieldIndex = getFieldIndex(sheet);
                List<Field> fields = getAllFields(tClass);
                List<T> list = new ArrayList<>();
                for (int i = 1; i < sheet.getLastRowNum() + 1; i++) {
                    T t = Objects.requireNonNull(tClass).newInstance();
                    int count = setFieldValueByRow(fieldIndex, fields, sheet.getRow(i), t);
                    if (count != 0) {
                        list.add(t);
                    }
                }
                return list;
            } else {
                return null;
            }
        } catch (IOException | InvalidFormatException | IllegalAccessException | InstantiationException e) {
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * <p>从excel中读取数据并转化为一个对象数组，主要用于各个功能的excel导入接口，该方法只适用于单sheet的情况，一次只对一种对象进行操作</p>
     * <p>该方法要求excel的表头以备注的形式将对象的属性信息填入对应单元格，即表格第一行的每个单元格需要添加批注，内容为对象的Java字段名（小写开头驼峰式）</p>
     * <p>根据对象字段的类型解析</p>
     *
     * @param file   excel文件
     * @param tClass 对象类型
     * @return 对象的数组，如果表格无数据则返回null
     */
    public static <T> List<T> readExcelToListByFieldType(MultipartFile file, Class<T> tClass) throws InvalidFormatException, InstantiationException, IllegalAccessException, IOException {
        return readExcelToListByFieldType(file, tClass, 0);
    }

    public static <T> List<T> readExcelToListByFieldType(MultipartFile file, Class<T> tClass, int tier) throws InvalidFormatException, InstantiationException, IllegalAccessException, IOException {
        try {
            Workbook workbook = ExcelUtil.createCommonWorkbook(file.getInputStream());
            Sheet sheet = Objects.requireNonNull(workbook).getSheetAt(tier);
            if (sheet != null) {
                Map<Integer, String> fieldIndex = getFieldIndex(sheet);
                List<Field> fields = getAllFields(tClass);
                List<T> list = new ArrayList<>();
                for (int i = 1; i < sheet.getLastRowNum() + 1; i++) {
                    T t = Objects.requireNonNull(tClass).newInstance();
                    int count = setFieldValueByFieldType(fieldIndex, fields, sheet.getRow(i), t);
                    if (count != 0) {
                        list.add(t);
                    }
                }
                return list;
            } else {
                return null;
            }
        } catch (IOException | InvalidFormatException | IllegalAccessException | InstantiationException e) {
            e.printStackTrace();
            throw e;
        }
    }

    public static Map<Integer, String> getFieldIndex(Sheet sheet) {
        Row row = sheet.getRow(0);
        Map<Integer, String> fieldIndex = new HashMap<>();
        for (Cell cell : row) {
            Comment comment = cell.getCellComment();
            if (comment != null) {
                fieldIndex.put(comment.getColumn(), comment.getString().getString());
            }
        }
        return fieldIndex;
    }

    @SuppressWarnings("rawtypes")
    public static <T> List<Field> getAllFields(Class<T> tClass) {
        List<Field> fields = new ArrayList<>();
        Class aClass = tClass;
        while (aClass != null) {
            fields.addAll(Arrays.asList(aClass.getDeclaredFields()));
            aClass = aClass.getSuperclass();
        }
        return fields;
    }

    public static <T> void setFieldValueByCell(Field field, Cell cell, T t, AtomicInteger count) {
        field.setAccessible(true);
        try {
            int cellType = cell.getCellType();
            switch (cellType) {
                case Cell.CELL_TYPE_NUMERIC:
                    if (HSSFDateUtil.isCellDateFormatted(cell)) {
                        field.set(t, cell.getDateCellValue());
                    } else {
                        field.set(t, parseNumeric(field.getType(), cell.getNumericCellValue()));
                    }
                    break;
                case Cell.CELL_TYPE_STRING:
                    field.set(t, cell.getStringCellValue());
                    break;
                case Cell.CELL_TYPE_BOOLEAN:
                    field.set(t, cell.getBooleanCellValue());
                    break;
                default:
            }
            if (field.get(t) != null) {
                count.addAndGet(1);
            }
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
    }

    /**
     * 增加字段赋值计数器，初始为0，进行有效赋值操作会累加1，若最终为0，则表示对象为空对象，将被丢弃
     */
    public static <T> int setFieldValueByRow(Map<Integer, String> fieldIndex, List<Field> fields, Row row, T t) {
        AtomicInteger count = new AtomicInteger(0);
        for (Cell cell : row) {
            fields.stream()
                    .filter(it -> it.getName().equals(fieldIndex.get(cell.getColumnIndex())))
                    .findFirst().ifPresent(field -> setFieldValueByCell(field, cell, t, count));
        }
        return count.get();
    }

    public static <T> int setFieldValueByFieldType(Map<Integer, String> fieldIndex, List<Field> fields, Row row, T t) {
        AtomicInteger count = new AtomicInteger(0);
        for (Cell cell : row) {
            fields.stream()
                    .filter(it -> it.getName().equals(fieldIndex.get(cell.getColumnIndex())))
                    .findFirst().ifPresent(field -> setFieldValueByType(field, cell, t, count));
        }
        return count.get();
    }

    public static <T> T parseNumeric(Class<T> t, Object obj) {
        Object result = new Object();
        try {
            String var = String.valueOf(obj);
            Double dblValue = Double.parseDouble(var);
            switch (t.getTypeName()) {
                case "java.lang.Byte":
                    result = dblValue.byteValue();
                    break;
                case "java.lang.Short":
                    result = dblValue.shortValue();
                    break;
                case "java.lang.Integer":
                    result = dblValue.intValue();
                    break;
                case "java.lang.Long":
                    result = dblValue.longValue();
                    break;
                case "java.lang.Float":
                    result = dblValue.floatValue();
                    break;
                case "java.lang.Double":
                    result = dblValue;
                    break;
                default:
            }
        } catch (NumberFormatException e) {
            e.printStackTrace();
        }
        return (T) result;
    }

    public static <T> void setFieldValueByType(Field field, Cell cell, T t, AtomicInteger count) {
        field.setAccessible(true);
        try {
            cell.setCellType(Cell.CELL_TYPE_STRING);
            String cellValue = cell.getStringCellValue();
            field.set(t, parseValue(field.getType(), cellValue, field));
            if (field.get(t) != null) {
                count.addAndGet(1);
            }
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
    }

    public static <T> T parseValue(Class<T> t, Object obj, Field field) {
        Object result = new Object();
        try {
            String var = String.valueOf(obj);
            switch (t.getTypeName()) {
                case "java.lang.Byte":
                    result = ((Double) Double.parseDouble(var)).byteValue();
                    break;
                case "java.lang.Short":
                    result = ((Double) Double.parseDouble(var)).shortValue();
                    break;
                case "java.lang.Integer":
                    result = ((Double) Double.parseDouble(var)).intValue();
                    break;
                case "java.lang.Long":
                    result = ((Double) Double.parseDouble(var)).longValue();
                    break;
                case "java.lang.Float":
                    result = ((Double) Double.parseDouble(var)).floatValue();
                    break;
                case "java.util.Date":
                    SimpleDateFormat sdf;
                    if (field.isAnnotationPresent(JsonFormat.class)) {
                        JsonFormat ano = field.getAnnotation(JsonFormat.class);
                        sdf = new SimpleDateFormat(ano.pattern());
                    } else {
                        sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    }
                    result = sdf.parse(var);
                    break;
                case "java.lang.String":
                    result = var;
                    break;
                default:
                    result = var;
                    break;
            }
        } catch (NumberFormatException | ParseException e) {
            e.printStackTrace();
        }
        return (T) result;
    }
}
