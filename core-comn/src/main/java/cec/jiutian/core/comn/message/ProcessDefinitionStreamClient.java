package cec.jiutian.core.comn.message;

/**
 * <AUTHOR> Y
 * @version 1.0
 * @date 2019/8/22 3:40 PM
 */
public interface ProcessDefinitionStreamClient {
    String INPUT = "inputProcessDefinition";

    String OUTPUT = "outputProcessDefinition";

/*    @Input(ProcessDefinitionStreamClient.INPUT)
    SubscribableChannel input();

    @Output(ProcessDefinitionStreamClient.OUTPUT)
    MessageChannel output();*/
}
