package cec.jiutian.core.comn.constant;

/**
 * <AUTHOR>
 * @date 2019/12/17
 * @description carrier常量接口
 */
public interface CarrierConstant {
    // carrierState
    String CARRIER_STATE_DISABLE = "Disable";
    String CARRIER_STATE_NOT_AVAILABLE = "NotAvailable";
    String CARRIER_STATE_AVAILABLE = "Available";
    String CARRIER_STATE_INUSE = "InUse";
    String DURABLE_STATE_SCRAPPED = "Scrapped";

    // carrierHoldState
    String ON_HOLD = "OnHold";
    String NOT_ON_HOLD = "NotOnHold";

    // carrierCleanState
    String CARRIER_CLEAN_STATE_DIRTY = "Dirty";
    String CARRIER_CLEAN_STATE_CLEAN = "Clean";

    // carrierTransferState
    String IN_STORAGE = "InStorage";
    String IN_TRANSIT = "InTransit";
    String ON_MACHINE = "OnMachine";

    // carrierNameRule
    String CARRIER_NAME = "CarrierName";
    String DURABLE_NAME = "DurableName";

    // categoryCode
    String CARRIER = "Carrier";
    String DURABLE = "Durable";

    // carrierMessage
    String CARRIER_DELETE = "该载具已经被删除";
    String CARRIER_ERROR = "该载具不可用";
    String CARRIER_RELEASE = "该载具已经被冻结释放";
    String CARRIER_NOT_EXIST = "该载具不存在";
    String CARRIER_HOLD = "该载具已经被冻结";
}
