package cec.jiutian.core.comn.message;

/**
 * MWS 发送库存预警到Alarm
 *
 * <AUTHOR>
 */
public interface AlarmMessageStreamClient {

    String INPUT = "inputAlarmMessage";

    String OUTPUT = "outputAlarmMessage";

    String INPUTFROMMESSAGE = "inPutFromMessage";

    String OUTPUTTOMESSAGE = "outPutToMessage";

    String InputAlarmCheckToWMS = "inputAlarmCheckToWMS";

/*    @Input(AlarmMessageStreamClient.INPUT)
    SubscribableChannel input();

    @Output(AlarmMessageStreamClient.OUTPUT)
    MessageChannel output();

    @Input(AlarmMessageStreamClient.INPUTFROMMESSAGE)
    SubscribableChannel inPutFromMessage();

    @Output(AlarmMessageStreamClient.OUTPUTTOMESSAGE)
    MessageChannel outPutToMessage();

    @Input(AlarmMessageStreamClient.InputAlarmCheckToWMS)
    SubscribableChannel inputAlarmCheckToWMS();*/
}
