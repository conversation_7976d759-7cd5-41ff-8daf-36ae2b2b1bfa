package cec.jiutian.core.comn.util;

import org.apache.poi.hssf.usermodel.HSSFDateUtil;
import org.apache.poi.ss.usermodel.Cell;

import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.util.Date;


public class ExcelDateUtils {


    //获取单元格内容

    public static String getCellValue(Cell cell) {

        if (cell == null) {

            return null;

        }

        String cellValue = "";

        DecimalFormat df = new DecimalFormat("#");

        switch (cell.getCellType()) {

            case Cell.CELL_TYPE_STRING:

                cellValue = cell.getRichStringCellValue().getString().trim();

                break;

            case Cell.CELL_TYPE_NUMERIC:

                //like12 add,20180622,支持日期格式

                if (HSSFDateUtil.isCellDateFormatted(cell)) {

                    Date d = cell.getDateCellValue();

                    DateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//HH:mm:ss

                    cellValue = df2.format(d);

                }

                //数字

                else {

                    cellValue = df.format(cell.getNumericCellValue());

                }

                break;

            case Cell.CELL_TYPE_BOOLEAN:

                cellValue = String.valueOf(cell.getBooleanCellValue()).trim();

                break;

            case Cell.CELL_TYPE_FORMULA:

                cellValue = cell.getCellFormula();

                break;

            default:

                cellValue = "";

        }

        return cellValue;
    }

    /**
     * @return返回字符串格式 yyyy-MM-dd HH:mm:ss
     */
    public static Date strToDateLong(String strDate) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        ParsePosition pos = new ParsePosition(0);
        Date strtodate = formatter.parse(strDate, pos);
        return strtodate;
    }

    public static Date strToDate(String strDate) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        ParsePosition pos = new ParsePosition(0);
        Date strtodate = formatter.parse(strDate, pos);
        return strtodate;
    }
}
