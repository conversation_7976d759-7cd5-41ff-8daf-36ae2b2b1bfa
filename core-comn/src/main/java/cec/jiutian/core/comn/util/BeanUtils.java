package cec.jiutian.core.comn.util;

import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.beans.BeansException;
import org.springframework.beans.FatalBeanException;
import org.springframework.util.Assert;

import java.beans.PropertyDescriptor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public abstract class BeanUtils extends org.springframework.beans.BeanUtils {

    /**
     * copy 非null 属性的值
     *
     * @param source
     * @param target
     * @throws BeansException
     * <AUTHOR> Pansy
     * @Date : 20170726
     */
    public static void copyNotNullProperties(Object source, Object target) throws BeansException {
        Assert.notNull(source, "Source must not be null");
        Assert.notNull(target, "Target must not be null");
        Class<?> actualEditable = target.getClass();
        PropertyDescriptor[] targetPds = getPropertyDescriptors(actualEditable);
        for (PropertyDescriptor targetPd : targetPds) {
            if (targetPd.getWriteMethod() != null) {
                PropertyDescriptor sourcePd = getPropertyDescriptor(source.getClass(), targetPd.getName());
                if (sourcePd != null && sourcePd.getReadMethod() != null) {
                    try {
                        Method readMethod = sourcePd.getReadMethod();
                        if (!Modifier.isPublic(readMethod.getDeclaringClass().getModifiers())) {
                            readMethod.setAccessible(true);
                        }
                        Object value = readMethod.invoke(source);
                        // 这里判断以下value是否为空 当然这里也能进行一些特殊要求的处理 例如绑定时格式转换等等
                        if (value != null) {
                            Method writeMethod = targetPd.getWriteMethod();
                            if (!Modifier.isPublic(writeMethod.getDeclaringClass().getModifiers())) {
                                writeMethod.setAccessible(true);
                            }
                            writeMethod.invoke(target, value);
                        }
                    } catch (Throwable ex) {
                        throw new FatalBeanException("Could not copy properties from source to target", ex);
                    }
                }
            }
        }

        //copyProperties(source, target, getNullPropertyNames(source));
    }

    /**
     * copy 非null 非""(String)属性的值
     *
     * @param source
     * @param target
     * @throws BeansException
     * <AUTHOR>
     */
    public static void copyNotEmptyProperties(Object source, Object target) throws BeansException {
        Assert.notNull(source, "Source must not be null");
        Assert.notNull(target, "Target must not be null");
        Class<?> actualEditable = target.getClass();
        PropertyDescriptor[] targetPds = getPropertyDescriptors(actualEditable);
        for (PropertyDescriptor targetPd : targetPds) {
            if (targetPd.getWriteMethod() != null) {
                PropertyDescriptor sourcePd = getPropertyDescriptor(source.getClass(), targetPd.getName());
                if (sourcePd != null && sourcePd.getReadMethod() != null) {
                    try {
                        Method readMethod = sourcePd.getReadMethod();
                        if (!Modifier.isPublic(readMethod.getDeclaringClass().getModifiers())) {
                            readMethod.setAccessible(true);
                        }
                        Object value = readMethod.invoke(source);
                        // 这里判断以下value是否为空 当然这里也能进行一些特殊要求的处理 例如绑定时格式转换等等
                        if (value != null) {
                            if (value instanceof String) {
                                String svalue = (String) value;
                                if (StringUtils.isEmpty(svalue)) {
                                    continue;
                                }
                            }
                            Method writeMethod = targetPd.getWriteMethod();
                            if (!Modifier.isPublic(writeMethod.getDeclaringClass().getModifiers())) {
                                writeMethod.setAccessible(true);
                            }
                            writeMethod.invoke(target, value);
                        }
                    } catch (Throwable ex) {
                        throw new FatalBeanException("Could not copy properties from source to target", ex);
                    }
                }
            }
        }

        //copyProperties(source, target, getNullPropertyNames(source));
    }

    /**
     * copy 非null 非""(String)属性的值,同时将 String值copy给同名-s的byte
     *
     * @param source
     * @param target
     * @throws BeansException
     * <AUTHOR>
     */
    public static void copyNotEmptyPropertiesWithByte(Object source, Object target) throws BeansException {
        Assert.notNull(source, "Source must not be null");
        Assert.notNull(target, "Target must not be null");
        Class<?> actualEditable = target.getClass();
        PropertyDescriptor[] targetPds = BeanUtils.getPropertyDescriptors(actualEditable);
        for (PropertyDescriptor targetPd : targetPds) {
            if (targetPd.getWriteMethod() != null) {
                PropertyDescriptor sourcePd;
                String targetTypeName = targetPd.getPropertyType().getTypeName();
                if ("byte[]".equals(targetPd.getPropertyType().getTypeName())) {
                    sourcePd = BeanUtils.getPropertyDescriptor(source.getClass(), targetPd.getName() + 's');
                } else {
                    sourcePd = BeanUtils.getPropertyDescriptor(source.getClass(), targetPd.getName());
                }
                if (sourcePd != null && sourcePd.getReadMethod() != null) {
                    try {
                        Method readMethod = sourcePd.getReadMethod();
                        if (!Modifier.isPublic(readMethod.getDeclaringClass().getModifiers())) {
                            readMethod.setAccessible(true);
                        }
                        Object value = readMethod.invoke(source);
                        // 这里判断以下value是否为空 当然这里也能进行一些特殊要求的处理 例如绑定时格式转换等等
                        if (value != null) {
                            if (value instanceof String) {
                                String svalue = (String) value;
                                if (StringUtils.isEmpty(svalue)) {
                                    continue;
                                }
                            }
                            Method writeMethod = targetPd.getWriteMethod();
                            if (!Modifier.isPublic(writeMethod.getDeclaringClass().getModifiers())) {
                                writeMethod.setAccessible(true);
                            }
                            String sourceTypeName = sourcePd.getPropertyType().getTypeName();
                            if ("byte[]".equals(targetTypeName) && "java.lang.String".equals(sourceTypeName)) {
                                value = new String(Base64.getDecoder().decode((String) value), StandardCharsets.UTF_8).getBytes(StandardCharsets.UTF_8);
                            }
                            writeMethod.invoke(target, value);
                        }
                    } catch (Throwable ex) {
                        throw new FatalBeanException("Could not copy properties from source to target", ex);
                    }
                }
            }
        }
    }

    private static String[] getNullPropertyNames(Object source) {
        final BeanWrapper src = new BeanWrapperImpl(source);
        java.beans.PropertyDescriptor[] pds = src.getPropertyDescriptors();

        Set<String> emptyNames = new HashSet<>();
        for (java.beans.PropertyDescriptor pd : pds) {
            // check if value of this property is null then add it to the collection
            Object srcValue = src.getPropertyValue(pd.getName());
            if (srcValue == null)
                emptyNames.add(pd.getName());
        }
        String[] result = new String[emptyNames.size()];
        return emptyNames.toArray(result);
    }

    public static List<String> getFieldNameList(Class tClass) {
        List<String> fieldNameLists = new ArrayList<>();
        List<Field> fields = new ArrayList<>();
        Class aClass = tClass;
        while (aClass != null) {
            fields.addAll(Arrays.asList(aClass.getDeclaredFields()));
            aClass = aClass.getSuperclass();
            fieldNameLists = fields.stream().map(Field::getName).collect(Collectors.toList());
        }
        return fieldNameLists;
    }

    public static <T> List<Field> getAllFields(Class<T> tClass) {
        List<Field> fields = new ArrayList<>();
        Class aClass = tClass;
        while (aClass != null) {
            fields.addAll(Arrays.asList(aClass.getDeclaredFields()));
            aClass = aClass.getSuperclass();
        }
        return fields;
    }

    /*
     * <AUTHOR>
     * @Description //判断cClass类的字段是否都存在于pClass类中
     **/
    public static Boolean ClassFieldContain(Class pClass, Class cClass) {
        if (null == cClass || null == pClass) {
            return false;
        }
        List<String> cfieldNameTypeLists;
        List<String> pfieldNameTypeLists;
        List<Field> cfields = new ArrayList<>();
        List<Field> pfields = new ArrayList<>();
        cfields.addAll(getAllFieldsAndSupper(cClass));
        pfields.addAll(getAllFieldsAndSupper(pClass));
        cfieldNameTypeLists = cfields.stream().map(X->X.getName() + X.getType().getName()).collect(Collectors.toList());
        pfieldNameTypeLists = pfields.stream().map(X->X.getName() + X.getType().getName()).collect(Collectors.toList());
        return pfieldNameTypeLists.containsAll(cfieldNameTypeLists);
    }


    /**
     * 获取本类及其父类的字段属性
     * @param clazz 当前类对象
     * @return 字段数组
     */
    public static List<Field> getAllFieldsAndSupper(Class<?> clazz) {
        List<Field> fieldList = new ArrayList<>();
        while (clazz != null) {
            fieldList.addAll(new ArrayList<>(Arrays.asList(clazz.getDeclaredFields())));
            clazz = clazz.getSuperclass();
        }
        return fieldList;
    }
}
