package cec.jiutian.core.comn.constant;

public enum EqpApiEventName {
    PORT_CREATE("Port_Create"),
    PORT_UPDATE("Port_Update"),
    PORT_DELETE("Port_Delete"),
    PORT_SPEC_CREATE("Port_Spec_Create"),
    PORT_SPEC_UPDATE("Port_Spec_Update"),
    PORT_SPEC_DELETE("Port_Spec_Delete"),
    MACH_OP_CREATE("Mach_Op_Create"),
    MACH_OP_UPDATE("Mach_Op_Update"),
    MACH_OP_DELETE("Mach_Op_Delete"),
    CREATE_MACHINE_MAINTAIN("Create_Machine_Maintain"),
    UPDATE_MACHINE_SPEC_PROPERTY("Update_Machine_Spec_Property"),
    CREATE_MACHINE_SPEC_PROPERTY("Create_Machine_Spec_Property"),
    DELETE_MACHINE_SPEC_PROPERTY("Delete_Machine_Spec_Property"),
    UPDATE_MACHINE_REPAIR("Update_Machine_Repair"),
    CREATE_MACHINE_REPAIR("Create_Machine_Repair"),
    UPDATE_MACHINE_MAINTAIN("Update_Machine_Maintain"),
    BIND_MACHINE_MAINTAIN_PLAN("Bind_Machine_Maintain_plan"),
    DELETE_MAINTAIN_PLAN("Delete_Maintain_plan"),
    DELETE_MACHINE_SPOT_CHECK_INFO("Delete_Machine_Spot_Check_Info"),
    UPDATE_MACHINE_SPOT_CHECK_INFO("Update_Machine_Spot_Check_Info"),
    ADD_MACHINE_SPOT_CHECK_INFO("Add_Machine_Spot_Check_Info"),
    BIND_MACHINE_SPOT_CHECK("Bind_Machine_Spot_Check"),
    UN_BIND_MACHINE_SPOT_CHECK("Un_Bind_Machine_Spot_Check"),
    UPDATE_MACHINE_COMMUNICATION_STATE("Update_Machine_Communication_State"),
    UPDATE_MACHINE_STATE("Update_Machine_State"),
    DELETE_MACHINE_SPEC("Delete_Machine_Spec"),
    EQP_CREATE_MACHINE_SPEC("Create_Machine_Spec"),
    UPDATE_MACHINE_SPEC("Update_Machine_Spec"),
    UPDATE_MACHINE_RECIPE("Update_Machine_Recipe"),
    UPDATE_MACHINE_RECIPE_PARAM("Update_Machine_Recipe_Param"),
    CHANGE_VALID_FLAG("Change_Valid_Flag"),
    CREATE_EQP_MACH_RPR("Create_Equipment_Machine_Repair"),
    //审核
    AUTHROIZE_MACH_RPR("Authorize_Machine_Repair"),
    //接单
    PICKUP_MACH_RPR("PickUp_Machine_Repair"),
    //开始维修
    REPAIR_MACH_RPR("Repair_Machine_Repair"),
    //结束维修
    COMPLETE_MACH_RPR("Complete_Machine_Repair"),
    //隐患验证
    CLOSE_MACH_RPR("Close_Machine_Repair"),
    //调配维修任务
    DEPLOY_MACH_RPR("Deploy_Machine_Repair"),
    CHANGE_SPOT_CHECK_STATE("Change_Spot_Check_State"),
    IMPORT_SPOT_CHECK("Import_Spot_Check"),
    CREATE_EQPMNT_MNTN_ITM("Create_Eqpmnt_Mntn_Itm"),
    UPDATE_EQPMNT_MNTN_ITM("Update_Eqpmnt_Mntn_Itm"),
    DELETE_EQPMNT_MNTN_ITM("Delete_Eqpmnt_Mntn_Itm"),
    CHANGE_SEQPMNT_MNTN_ITM_STATE("Change_Eqpmnt_Mntn_Itm_State"),
    IMPORT_SEQPMNT_MNTN_ITM("Import_Eqpmnt_Mntn_Itm"),
    CREATE_EQPMNT_SPOT_CHECK_ITM("Create_Eqpmnt_Spot_Check_Itm"),
    UPDATE_EQPMNT_SPOT_CHECK_ITM("Update_Eqpmnt_Spot_Check_Itm"),
    DELETE_EQPMNT_SPOT_CHECK_ITM("Delete_Eqpmnt_Spot_Check_Itm"),
    CHANGE_EQPMNT_SPOT_CHECK_ITM("Change_Eqpmnt_Spot_Check_Itm"),
    IMPORT_EQPMNT_SPOT_CHECK_ITM("Import_Eqpmnt_Spot_Check_Itm"),
    CREATE_EQPMNT_MNTN_LIST("Create_Eqpmnt_Mntn_List"),
    UPDATE_EQPMNT_MNTN_LIST("Update_Eqpmnt_Mntn_List"),
    DELETE_EQPMNT_MNTN_LIST("Delete_Eqpmnt_Mntn_List"),
    CHANGE_SEQPMNT_MNTN_LIST_STATE("Change_Eqpmnt_Mntn_List_State"),
    IMPORT_SEQPMNT_MNTN_LIST("Import_Eqpmnt_Mntn_List"),
    CREATE_EQPMNT_SPOT_CHECK_LIST("Create_Eqpmnt_SpotCheck_List"),
    UPDATE_EQPMNT_SPOT_CHECK_LIST("Update_Eqpmnt_SpotCheck_List"),
    DELETE_EQPMNT_SPOT_CHECK_LIST("Delete_Eqpmnt_SpotCheck_List"),
    CHANGE_SEQPMNT_SPOT_CHECK_LIST_STATE("Change_Eqpmnt_SpotCheck_List_State"),
    IMPORT_SEQPMNT_SPOT_CHECK_LIST("Change_Eqpmnt_SpotCheck_List"),
    CREATE_EQPMNT_MNTN_TBM_PLAN("Create_Eqpmnt_Mntn_Tbm_Plan"),
    UPDATE_EQPMNT_MNTN_TBM_PLAN("Update_Eqpmnt_Mntn_Tbm_Plan"),
    DELETE_EQPMNT_MNTN_TBM_PLAN("Delete_Eqpmnt_Mntn_Tbm_Plan"),
    CHANGE_SEQPMNT_MNTN_TBM_PLAN_STATE("Change_Eqpmnt_Mntn_Tbm_Plan_State"),
    IMPORT_SEQPMNT_MNTN_TBM_PLAN("Import_Eqpmnt_Mntn_Cbm_Plan"),
    CREATE_EQPMNT_MNTN_CBM_PLAN("Create_Eqpmnt_Mntn_Cbm_Plan"),
    UPDATE_EQPMNT_MNTN_CBM_PLAN("Update_Eqpmnt_Mntn_Cbm_Plan"),
    DELETE_EQPMNT_MNTN_CBM_PLAN("Delete_Eqpmnt_Mntn_Cbm_Plan"),
    CHANGE_EQPMNT_MNTN_CBM_PLAN_STATE("Change_Eqpmnt_Mntn_Cbm_Plan_State"),
    IMPORT_EQPMNT_MNTN_CBM_PLAN("Import_Eqpmnt_Mntn_Cbm_Plan"),
    CREATE_EQPMNT_MNTN_ITM_LIST("Create_Eqpmnt_Mntn_Itm_List"),
    DELETE_EQPMNT_MNTN_ITM_LIST("Delete_Eqpmnt_Mntn_Itm_List"),
    PART_SPEC_BOND("Part_Spec_Bond"),
    PART_SPEC_UnBOND("Part_Spec_UnBond"),
    CREATE_PART_SAFE("Create_Part_Safe"),
    UPDATE_PART_SAFE("Update_Part_Safe"),
    DELETE_PART_SAFE("Delete_Part_Safe"),
    CREATE_EQPMNT_SPOT_CHECK_ITM_LIST("Create_Eqpmnt_Spot_Check_Itm_List"),
    DELETE_EQPMNT_SPOT_CHECK_ITM_LIST("Delete_Eqpmnt_Spot_Check_Itm_List"),
    CREATE_PART_REQUEST("Create_Part_Request"),
    AUDIT_PART_REQUEST("Audit_Part_Request"),
    CREATE_EQPMNT_SPOT_CHECK_CBM_PLAN("Create_Eqpmnt_Spot_Check_Cbm_Plan"),
    UPDATE_EQPMNT_SPOT_CHECK_CBM_PLAN("Update_Eqpmnt_Spot_Check_Cbm_Plan"),
    DELETE_EQPMNT_SPOT_CHECK_CBM_PLAN("Delete_Eqpmnt_Spot_Check_Cbm_Plan"),
    CHANGE_EQPMNT_SPOT_CHECK_CBM_PLAN_STATE("Change_Eqpmnt_Spot_Check_Cbm_Plan_State"),
    IMPORT_EQPMNT_SPOT_CHECK_CBM_PLAN("Import_Eqpmnt_Spot_Check_Cbm_Plan"),
    CANCEL_PART_REQUEST("Cancel_Part_Request"),
    CREATE_EQPMNT_SPOT_CHECK_TBM_PLAN("Create_Eqpmnt_Spot_Check_Tbm_Plan"),
    UPDATE_EQPMNT_SPOT_CHECK_TBM_PLAN("Update_Eqpmnt_Spot_Check_Tbm_Plan"),
    DELETE_EQPMNT_SPOT_CHECK_TBM_PLAN("Delete_Eqpmnt_Spot_Check_Tbm_Plan"),
    CHANGE_SEQPMNT_SPOT_CHECK_TBM_PLAN_STATE("Change_Eqpmnt_Spot_Check_Tbm_Plan_State"),
    IMPORT_SEQPMNT_SPOT_CHECK_TBM_PLAN("Import_Eqpmnt_Spot_Check_Cbm_Plan"),
    CREATE_EQPMNT_SPOT_CHECK_TASK("Create_Eqpmnt_Spot_Check_Task"),
    UPDATE_EQPMNT_SPOT_CHECK_TASK("Update_Eqpmnt_Spot_Check_Task"),
    CREATE_EQPMNT_SPOT_CHECK_TASK_DETAIL("Create_Eqpmnt_Spot_Check_Task_Detail"),
    UPDATE_EQPMNT_SPOT_CHECK_TASK_DETAIL("Update_Eqpmnt_Spot_Check_Task_Detail"),
    ALLOCATE_EQPMNT_SPOT_CHECK_TASK("Allocate_Eqpmnt_Spot_Check_Task"),
    EXECUTE_EQPMNT_SPOT_CHECK_TASK("Execute_Eqpmnt_Spot_Check_Task"),
    REDEPLOY_EQPMNT_SPOT_CHECK_TASK("Redeploy_Eqpmnt_Spot_Check_Task"),
    CREATE_FACTORY_EMPLOYEE_ARRANGEMENT("Create_Fctry_Empl_Arngmnt"),
    CREATE_EQPMNT_MNTN_TASK("Create_Eqpmnt_Mntn_Task"),
    UPDATE_EQPMNT_MNTN_TASK("Update_Eqpmnt_Mntn_Task"),
    CREATE_EQPMNT_MNTN_TASK_DETAIL("Create_Eqpmnt_Mntn_Task_Detail"),
    UPDATE_EQPMNT_MNTN_TASK_DETAIL("Update_Eqpmnt_Mntn_Task_Detail"),
    ALLOCATE_EQPMNT_MNTN_TASK("Allocate_Eqpmnt_Mntn_Task"),
    REDEPLOY_EQPMNT_MNTN_TASK("Redeploy_Eqpmnt_Mntn_Task"),
    SHIP_SPARE_PART("Ship_Spare_Part"),
    CHANGE_TST_QSTN_WRHS_STATE("Change_Tst_Qstn_Wrhs_State"),
    RUN_TIME_CREATE("Run_Time_Create"),
    RUN_TIME_UPDATE("Run_Time_Update"),
    RUN_TIME_DELETE("Run_Time_Delete"),

    SPOT_CHECK_PLAN_CREATE("Spot_Check_Plan_Create"),
    CHANGE_MACHINE_GROUP_STATE("Change_Machine_Group_State"),
    CREATE_EQP_MACH_GRP("Create_Equipment_Machine_Group"),
    UPDATE_EQP_MACH_GRP("Update_Equipment_Machine_Group"),
    ;


    private String name;

    EqpApiEventName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }
}
