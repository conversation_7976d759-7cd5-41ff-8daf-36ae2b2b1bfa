package cec.jiutian.core.comn.service;

import cec.jiutian.core.comn.constant.AuthenticationConstant;
import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTCreator.Builder;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTDecodeException;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.TimeZone;

@Service("authorizedSecretService")
@Slf4j
public class AuthorizedSecretService {

    private final Logger logger = LoggerFactory.getLogger(AuthorizedSecretService.class);

    private final long EXPIRATION = 86400;
    private final long EXPIRATION_REMEMBER = 604800L;
    private final String ISSUER = "cecjiutian";
    private final String SUBJECT = "FABOS3.0";
    private final String CECJTMQ = "CECJTMQ";

    @Autowired
    private BaseRedisService baseRedisService;

    /**
     * <p>
     * 获取一个授权密钥
     * </p>
     *
     * @return
     * <AUTHOR>
     */
    public String getAuthorizedSecret(String userId) {
        return getAuthorizedSecret(userId, false);
    }

    /**
     * 获取一个授权密钥
     *
     * @param isRememberMe
     * @return
     */
    public String getAuthorizedSecret(String userId, boolean isRememberMe) {
        Builder builder = JWT.create();
        builder.withAudience(userId);
        Algorithm algorithm = Algorithm.HMAC256(AuthenticationConstant.SECRET_KEY);
        builder.withIssuer(ISSUER);
        long expiration = isRememberMe ? EXPIRATION_REMEMBER : EXPIRATION;
        TimeZone.setDefault(TimeZone.getTimeZone("GMT+08:00"));
        builder.withExpiresAt(new Date(System.currentTimeMillis() + expiration * 1000));
        builder.withSubject(SUBJECT);
        String authorizedSecret = builder.sign(algorithm);
        baseRedisService.setString(userId, authorizedSecret, expiration);
        return authorizedSecret;
    }

    /**
     * 获取一个授权密钥
     *
     * <AUTHOR>
     */
    public void getMQToken() {
        Builder builder = JWT.create();
        builder.withAudience(CECJTMQ);
        Algorithm algorithm = Algorithm.HMAC256(AuthenticationConstant.SECRET_KEY);
        builder.withIssuer(ISSUER);
        TimeZone.setDefault(TimeZone.getTimeZone("GMT+08:00"));
        builder.withSubject(SUBJECT);
        String authorizedSecret = builder.sign(algorithm);
        logger.info("Bearer " + authorizedSecret);
        baseRedisService.setString(CECJTMQ, authorizedSecret);
    }

    /*
     * <AUTHOR>
     * @Description //解析token,获取TokenAnalysisEntity的json字符串
     * @Param [token] 不含前缀的token字符串
     * @return TokenAnalysisEntity的json字符串
     **/
    public String decodeAuthorizedSecret(String token) {
        try {
            String authorizationKey = token.replace(AuthenticationConstant.AUTHENTICATION_PREFIX, "");
            return JWT.decode(authorizationKey).getAudience().get(0);
        } catch (JWTDecodeException e) {
            log.error(e.getMessage());
            throw new RuntimeException("Authorization verify failure, cannot be accessed.");
        }
    }
}
