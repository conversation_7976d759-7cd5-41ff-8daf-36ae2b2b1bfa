package cec.jiutian.core.comn.message;

/**
 * <AUTHOR> Y
 * @version 1.0
 * @date 2019/8/22 3:33 PM
 */
public interface EnumValueDefinitionStreamClient {

    String INPUT = "inputEnumValue";

    String OUTPUT = "outputEnumValue";

/*    @Input(EnumValueDefinitionStreamClient.INPUT)
    SubscribableChannel input();

    @Output(EnumValueDefinitionStreamClient.OUTPUT)
    MessageChannel output();*/
}
