package cec.jiutian.core.comn.constant;

public interface AlarmConstant {
    String DATE_FORMAT_YMDHMS = "yyyy-MM-dd HH:mm:ss";
    String STATE_SUCCESS = "SUCCESS";
    String VALID_FLAG_Y = "Y";
    String VALID_FLAG_N = "N";
    String ALARM_ITEM_TYPE_CODE_A = "A";
    String ALARM_ITEM_TYPE_CODE_L = "L";
    String QUEUE_HOST_NAME = "save alarm log";
    String QUEUE_HOST = "host";
    String QUEUE_VALUE = "value";
    String QUEUE_MESSAGE = "message";
    String QUEUE_RULEID = "ruleid";
    String QUEUE_TIME = "time";
    String PUSH_TYPE = "Wechat";

    String WECHAT_TEMPLATE_1 = "NPhOMTj-zKpgPSG6mESHXsRHak8mUnx8uMNh47eeM0E";
    String WECHAT_OPENID_2 = "EKKjKBAUmma4ILCR-1IxEdRT6J-5caKj7PBHYcbX7eI";
    String WECHAT_USER_ERROR = "USER_ERROR";
    String WECHAT_NET_ERROR = "NET_ERROR";
    String WECHAT_TEMPLATE_COLOR = "#FF00FF";

    String IP_PATTERN = "([1-9]|[1-9]\\d|1\\d{2}|2[0-4]\\d|25[0-5])(\\.(\\d|[1-9]\\d|1\\d{2}|2[0-4]\\d|25[0-5])){3}";

    String WECHAT_PUSH_TYPE = "wechat";
    String EMAIL_PUSH_TYPE = "mail";
    String MESSAGE_PUSH_TYPE = "message";

    String SEND_STATE_SUCCESS = "Success";
    String SEND_STATE_FAILURE = "Failure";
}
