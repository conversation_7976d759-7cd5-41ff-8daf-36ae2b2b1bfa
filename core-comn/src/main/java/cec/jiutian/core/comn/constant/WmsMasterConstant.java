package cec.jiutian.core.comn.constant;

/**
 * <AUTHOR>
 * @date 2019/12/18
 * @description wms-master服务常量接口
 */
public interface WmsMasterConstant {
    // dateFormat
    String DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";

    // poType
    String PO_TYPE_MATERIAL = "Material";

    // asnType
    String ASN_TYPE_MATERIAL = "Material";

    // poState
    String PO_STATE_AUTHORIZED = "Authorized";
    String PO_STATE_CLOSED = "Closed";
    String PO_STATE_CREATED = "Created";
    String PO_STATE_RECEIVING = "Receiving";
    String PO_STATE_REJECTED = "Rejected";

    // asnState
    String ASN_STATE_CREATED = "Created";
    String ASN_STATE_IQC = "IQC";
    String ASN_STATE_CLOSED = "Closed";
    String ASN_STATE_STORING = "Storing";

    // returnState
    String RETURN_STATE_AUTHORIZED = "Authorized";
    String RETURN_STATE_CLOSED = "Closed";
    String RETURN_STATE_CREATED = "Created";
    String RETURN_STATE_REJECTED = "Rejected";
    String RETURN_STATE_RECEIVING = "Receiveing";

    // returnType
    String RETURN_TYPE_INTERNAL_RETURN = "InternalReturn";

    // carrierState
    String CARRIER_STATE_CREATED = "Created";
    String CARRIER_STATE_EMPTY = "Empty";
    String CARRIER_STATE_INUSE = "InUse";
    String CARRIER_STATE_SCRAPPED = "Scrapped";
    String CARRIER_STATE_SHIPPED = "Shipped";

    // carrierMoveState
    String CARRIER_INPLACE = "InPlace";

    // lotState
    String LOT_STATE_CREATED = "Created";
    String LOT_STATE_MERGE = "Merge";
    String LOT_STATE_ONSHELF = "OnShelf";
    String LOT_STATE_PICKED = "Picked";
    String LOT_STATE_RECEIVED = "Received";
    String LOT_STATE_SCRAPPED = "Scrapped";
    String LOT_STATE_SHIPPED = "Shipped";
    String LOT_STATE_TBR = "TBR";
    String LOT_STATE_RETURN = "Return";

    // productState
    String PRODUCT_STATE_ONSHELF = "OnShelf";
    String PRODUCT_STATE_RECEIVED = "Received";
    String PRODUCT_STATE_PICKED = "Picked";
    String PRODUCT_STATE_SCRAPPED = "Scrapped";
    String PRODUCT_STATE_SHIPPED = "Shipped";

    // stockPlanState
    String STOCK_PLAN_CREATE = "Create";
    String STOCK_PLAN_FINISH = "Finish";
    String STOCK_PLAN_MODIFY = "Modify";
    String STOCK_PLAN_DELETE = "Delete";
    String STOCK_PLAN_ONGOING = "Ongoing";
    String STOCK_PLAN_TOBEADJUSTED = "ToBeAdjusted";

    // soState
    String SO_STATE_AUTHORIZED = "Authorized";
    String SO_STATE_CREATED = "Created";
    String SO_STATE_PARTIAL_SHIPPED = "PartialShipped";
    String SO_STATE_PICKING = "Picking";
    String SO_STATE_REJECTED = "Rejected";
    String SO_STATE_RETURN = "Return";
    String SO_STATE_SHIPPED = "Shipped";

    // entryState
    String ENTRY_STATE_RECEIVED = "Received";

    // entryType
    String ENTRY_TYPE_MATERIAL = "Material";

    // soType
    String SO_TYPE_SALESO = "SaleSO";

    // ruleName
    String RULE_NAME_ASN = "InventoryASNID";
    String RULE_NAME_CARRIER = "CarrierId";
    String RULE_NAME_RETURN = "InventoryReturnID";
    String RULE_NAME_LOT = "WmsLotName";
    String RULE_NAME_PO = "InventoryPOID";
    String RULE_NAME_LOT_ID = "InventoryLotID";
    String RULE_NAME_DURABLE = "DurableName";
    String RULE_NAME_SO = "InventorySOID";
    String RULE_NAME_TASK = "InventorySOTaskID";
    String RULE_NAME_ASN_LOT = "InventoryASNLotID";
    String RULE_NAME_ENTRY = "InventoryWarehouseEntryID";

    // hold
    String ON_HOLD = "OnHold";
    String NOT_ON_HOLD = "NotOnHold";

    // grade
    String WAREHOUSE = "Warehouse";
    String BLOCK = "Block";
    String SHELF = "Shelf";
    String CARRIER = "Carrier";
    String LOT = "Lot";
    String PRODUCT = "Product";

    // reasonCode
    String PLAN_HOLD = "PLAN_HOLD";
    String PLAN_NOT_HOLD = "PLAN_NOT_HOLD";

    String YES = "Y";
    String NO = "N";

    String STOCK_CATEGORY_PRODUCT = "Product";
    String STOCK_CATEGORY_MATERIAL = "Material";
    String STOCK_CATEGORY_OTHER = "Other";

    // MesStockLotUseState 库存批次领用状态
    String HAVE_RECIPIENT = "YL";
    String FOR_RECIPIENT = "DL";

    // 	MesPartsOperationStateType 流转单项目状态
    String TO_SUBMIT = "01";  // 待提交
    String TO_DETECT = "02";  // 待检测
    String SUBMITED = "03";   // 已提交
    String RECEIVED = "04";   // 已接收

}
