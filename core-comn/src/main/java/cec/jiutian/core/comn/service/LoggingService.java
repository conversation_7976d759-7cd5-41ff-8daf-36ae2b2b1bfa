package cec.jiutian.core.comn.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;

@Aspect
@Component
@Slf4j
public class LoggingService {

    private static final transient String LINE_BREAK = System.lineSeparator();
    private static final transient int MAX_LOG_LENGTH = 32766;
    @Autowired
    private ObjectMapper logginMapper;

    @Pointcut("execution(public * cec.jiutian.*..controller..*.*(..)) || execution(public * cec.jiutian.*..remote.*.*(..))")
    public void loggingService() {
    }

    @Before("loggingService()")
    public void loggingService(JoinPoint joinPoint) throws Throwable {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        log.info(request.getRequestURL().toString());
    }

    @Around("loggingService()")
    public Object loggingService(ProceedingJoinPoint joinPoint) throws Throwable {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (null == attributes) {
            return null;
        }
        HttpServletRequest request = attributes.getRequest();
        StringBuilder sb = new StringBuilder();
        sb.append("Inbound Message").append(LINE_BREAK);
        sb.append("----------------------------").append(LINE_BREAK);
        sb.append("Method: ").append(joinPoint.getSignature().getDeclaringType()).append(".").append(joinPoint.getSignature().getName()).append(LINE_BREAK);
        sb.append("Request Url: ").append(request.getRequestURL()).append(LINE_BREAK);
        sb.append("Http-Method: ").append(request.getMethod()).append(LINE_BREAK);
        Signature signature = joinPoint.getSignature();
        sb.append("Action-Method: ").append(signature.getDeclaringTypeName()).append(".").append(signature.getName()).append(LINE_BREAK);
        try {
            sb.append("Parameter: ").append(logginMapper.writeValueAsString(joinPoint.getArgs())).append(LINE_BREAK);
        } catch (Exception e) {
            sb.append("Parameter: ").append("WARNING! The parameter is not serializable!").append(LINE_BREAK);
        }
        sb.append("----------------------------").append(LINE_BREAK);
        long startTime = System.currentTimeMillis();
        Object obj = joinPoint.proceed();
        long proceedTime = System.currentTimeMillis() - startTime;
        sb.append("Outbound Message").append(LINE_BREAK);
        sb.append("----------------------------").append(LINE_BREAK);
        sb.append("Response Time: ").append(proceedTime).append(LINE_BREAK);
        String responseBody = logginMapper.writeValueAsString(obj);
        if (responseBody.length() > MAX_LOG_LENGTH) {
            /* 暂时不记录长日志
            try {
                FileOutputStream fileOutputStream = new FileOutputStream("/long_log.txt",true);
                fileOutputStream.write((responseBody+LINE_BREAK+LINE_BREAK).getBytes());
                fileOutputStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
             */
            sb.append("Response Payload: The length of the returned data exceeds 32766!").append(LINE_BREAK);
        } else {
            sb.append("Return: ").append(responseBody).append(LINE_BREAK);
        }
        sb.append("----------------------------");
        log.info(sb.toString());
        return obj;
    }
}
