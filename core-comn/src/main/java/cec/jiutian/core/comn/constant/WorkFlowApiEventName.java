package cec.jiutian.core.comn.constant;

/**
 * <AUTHOR>
 * @date 2020/7/20
 */
public enum WorkFlowApiEventName {
    CREATE_WORK_FLOW("Create_Work_Flow"),
    UPDATE_WORK_FLOW("Update_Work_Flow"),
    DELETE_WORK_FLOW("Delete_Work_Flow"),

    PROCESS_START("Process_Start"),
    TASK_CREATE("Task_Create"),
    TASK_SAVE("Task_Save"),
    TASK_COMPLETE("Task_Complete"),

    INTERFACE_CREATE("Interface_Create"),
    INTERFACE_UPDATE("Interface_Update"),
    INTERFACE_DELETE("Interface_Delete"),

    LPA_ELEMENT_CREATE("Lpa_Element_Create"),
    LPA_ELEMENT_UPDATE("Lpa_Element_Update"),
    LPA_ELEMENT_DELETE("Lpa_Element_Delete"),

    LPA_ITEM_BOND("Lpa_Item_Bond"),
    LPA_ITEM_UNBOND("Lpa_Item_UnBond"),
    LPA_ITEM_CREATE("Lpa_Item_Create"),
    LPA_ITEM_UPDATE("Lpa_Item_Update"),
    LPA_ITEM_DELETE("Lpa_Item_Delete"),

    LPA_LIST_CREATE("Lpa_List_Create"),
    LPA_LIST_UPDATE("Lpa_List_Update"),
    LPA_LIST_DELETE("Lpa_List_Delete"),

    LPA_ELEMENT_BOND("Lpa_Element_Bond"),
    LPA_ELEMENT_UNBOND("Lpa_Element_UnBond"),

    LPA_LEVEL_CREATE("Lpa_Level_Create"),
    LPA_LEVEL_UPDATE("Lpa_Level_Update"),
    LPA_LEVEL_DELETE("Lpa_Level_Delete"),

    LPA_GROUP_CREATE("Lpa_Group_Create"),
    LPA_GROUP_UPDATE("Lpa_Group_Update"),
    LPA_GROUP_DELETE("Lpa_Group_Delete"),

    LPA_GROUP_BOND("Lpa_Group_Bond"),
    LPA_GROUP_UNBOND("Lpa_Group_UnBond"),

    LPA_PLAN_CREATE("Lpa_Plan_Create"),
    LPA_PLAN_UPDATE("Lpa_Plan_Update"),
    LPA_PLAN_DELETE("Lpa_Plan_Delete"),

    LPA_PLAN_PUBLISH("Lpa_Plan_Publish"),
    LPA_PLAN_UNPUBLISH("Lpa_Plan_UnPublish"),

    LPA_TASK_START("Lpa_Task_Start"),
    LPA_TASK_SAVE("Lpa_Task_Save"),
    LPA_TASK_COMPLETE("Lpa_Task_Complete"),
    LPA_TASK_COMPLETE_DELAY("Lpa_Task_Complete_Delay"),
    LPA_TASK_CANCEL("Lpa_Task_Cancel"),

    LPA_ISSUE_CLOSE("Lpa_Issue_Close"),
    LPA_ISSUE_DELETE("Lpa_Issue_Delete"),
    LPA_ISSUE_CREATE("Lpa_Issue_Create"),
    LPA_ISSUE_UPDATE("Lpa_Issue_Update"),
    ;
    private final String name;

    WorkFlowApiEventName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }
}
