package cec.jiutian.core.comn.util;

import java.math.BigDecimal;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Consumer;

/**
 * 类型转换，类型转换的时候，如果为null或者空时，赋予默认值，避免出现空指针的情况
 *
 * <AUTHOR>
 */
public final class LangTypes<T> {

    private final T value;

    private LangTypes() {
        this.value = null;
    }

    private LangTypes(T value) {
        this.value = value;
    }

    public static <T> LangTypes<T> init(T value) {
        return new LangTypes<>(value);
    }

    @SuppressWarnings("unchecked")
    public static <T> LangTypes<T> empty() {
        return (LangTypes<T>) new LangTypes<>();
    }

    public static <T> LangTypes<T> formList(T list) {
        if (null != list && list instanceof List) {
            T result = list;
            return init(result);
        }
        return empty();
    }

    public static <T> LangTypes<T> transform(T value, T defaultValue) {
        if (null == value) {
            value = defaultValue;
        }
        T result = null;
        if (null != value) {
            switch (value.getClass().getSimpleName()) {
                case "String":
                case "Integer":
                case "Long":
                case "Short":
                case "Float":
                case "Double":
                case "BigDecimal":
                    break;
                default:
                    throw new IllegalArgumentException(
                            "The supported data types are String, Integer, Long, Short, Float, Double and BigDecimal ");
            }
        }
        result = value;
        return init(result);
    }

    public static <T> T getValueDefNull(String str, Class<T> clazz) {
        if (StringUtils.isBlank(str)) {
            return null;
        }
        return getValue(str, clazz);
    }

    public static <T> T getValueDefZero(String str, Class<T> clazz) {
        return getValue(Optional.ofNullable(str).orElse("0"), clazz);
    }

    @SuppressWarnings("unchecked")
    private static <T> T getValue(String str, Class<T> clazz) {
        T result = null;
        switch (clazz.getSimpleName()) {
            case "Integer":
                result = (T) Integer.valueOf(str);
                break;
            case "Long":
                result = (T) Long.valueOf(str);
                break;
            case "Short":
                result = (T) Short.valueOf(str);
                break;
            case "Float":
                result = (T) Float.valueOf(str);
                break;
            case "Double":
                result = (T) Double.valueOf(str);
                break;
            case "BigDecimal":
                result = (T) new BigDecimal(str);
                break;
            default:
                throw new IllegalArgumentException("Invalid object type " + clazz);
        }
        return result;
    }

    public T value() {
        if (value == null) {
            throw new NoSuchElementException("No value present");
        }
        if ("String".equals(value.getClass().getSimpleName()) && "".equals(value)) {
            return null;
        }
        return value;
    }

    public void ifPresent(Consumer<? super T> consumer) {
        if (value != null) {
            List<?> list = (List<?>) value;
            if (null != list && list.size() != 0) {
                consumer.accept(value);
            }
        }
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }

        if (!(obj instanceof LangTypes)) {
            return false;
        }

        LangTypes<?> other = (LangTypes<?>) obj;
        return Objects.equals(value, other.value);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(value);
    }

    @Override
    public String toString() {
        return value != null ? String.format("LangTypes[%s]", value) : "LangTypes.empty";
    }
}
