package cec.jiutian.core.comn.constant;

/**
 * <AUTHOR>
 * @date 2019/12/17
 * @description pencil服务常量接口
 */
public interface PencilConstant {
    // dateFormat
    String DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";

    String LIMIT = " LIMIT 10";
    String SELECT_UPPER_CASE = "SELECT";
    String SELECT_LOWER_CASE = "select";
    String UPDATE = "UPDATE";
    String SQL_TYPE_UPDATE = "Update";
    String SQL_TYPE_DELETE = "Delete";
    String SQL_TYPE_INSERT = "Insert";
    String WHERE_LOWER_CASE = "where";

    String PREFIX = "mes_service";

    String PATTERN = "[^a-zA-Z_0-9]*";
}
