package cec.jiutian.core.comn.constant;

/**
 * <AUTHOR>
 * @date 2019/12/16
 * @description
 */
public enum WmsMasterApiEventName {
    // alternativeMaterialGroup
    CREATE_PRODUCT_SPEC_GROUP("Create_Product_Spec_Group"),
    UPDATE_PRODUCT_SPEC_GROUP("Update_Product_Spec_Group"),
    DELETE_PRODUCT_SPEC_GROUP("Delete_Product_Spec_Group"),
    SPEC_GROUP_BOND("Spec_Group_Bond"),
    SPEC_GROUP_UNBOND("Spec_Group_UnBond"),
    SPEC_GROUP_UPDATE("Spec_Group_Update"),
    TO_EXAMINE_PRODUCT_SPEC_GROUP("To_Examine_Product_Spec_Group"),

    // po
    PO_CREATE("Created_PO"),
    PO_AUDIT("To_Examine_PO"),
    PO_CLOSE("Closed_PO"),
    PO_ADJUST("Adjustment_PO"),
    PO_RECEIVE("PO_Receive"),

    // return
    RETURN_CREATE("Created_RTN"),
    RETURN_CLOSE("Closed_RTN"),
    RETURN_AUDIT("To_Examine_RTN"),
    RETURN_ADJUST("Adjustment_RTN"),

    // asn
    ASN_CHECK("ASN_IQC"),
    UPDATE_ASN_PRIORITY("Update_ASN_Priority"),

    // 批次生成
    LOT_CREATE("Created_Lot"),
    LOT_CANCEL("Delete_Lot"),

    // lot
    ADJUSTMENT_LOT("Adjustment_Lot"),
    RECEIVED_LOT("Received_Lot"),
    RETURN_LOT("Return_Lot"),
    ONSHELF_LOT("OnShelf_Lot"),
    MANUAL_CREATE_LOT("Manual_Create_Lot"),
    RECEIVED_PODUCT_LOT("Received_Poduct_Lot"),
    SCRAPPED_LOT("Scrapped_Lot"),
    ADJUSTMENT_SCRAPPED_LOT("Adjustment_Scrapped_Lot"),
    SHIPPED_LOT("Shipped_Lot"),
    CANCEL_PICKED_LOT("Cancel_Picked_Lot"),
    SPLIT_LOT("Split_Lot"),
    MERGE_LOT("Merge_Lot"),
    PICKED_LOT("Picked_Lot"),

    // carrier
    BIND_CARRIER("Bind_Carrier"),
    UNTYING_CARRIER("Untying_Carrier"),
    CREATE_CARRIER("Create_Carrier"),
    DELETE_CARRIER("Delete_Carrier"),
    UPDATE_CARRIER("Update_Carrier"),
    SCRAPPED_CARRIER("Scrapped_Carrier"),

    // carrierSpec
    CREATE_CARRIER_SPEC("Create_Carrier_Spec"),
    DELETE_CARRIER_SPEC("Delete_Carrier_Spec"),
    UPDATE_CARRIER_SPEC("Update_Carrier_Spec"),

    // saveInventory
    CREATE_PRODUCT_SAFE_INVENTORY("Create_Product_Safe_Inventory"),
    UPDATE_PRODUCT_SAFE_INVENTORY("Update_Product_Safe_Inventory"),
    DELETE_PRODUCT_SAFE_INVENTORY("Delete_Product_Safe_Inventory"),

    // productSpec
    CREATE_PRODUCT_SPEC("Create_Product_Spec"),
    UPDATE_PRODUCT_SPEC("Update_Product_Spec"),

    // so
    CREATED_SO("Created_SO"),
    CLOSED_SO("Closed_SO"),
    TO_EXAMINE_SO("To_Examine_SO"),
    ADJUSTMENT_SO("Adjustment_SO"),
    PARTIAL_SHIPPED("Partial_Shipped"),

    // stockPlan
    CREATED_INV("Created_INV"),
    UPDATE_INV("Update_INV"),
    DELETE_INV("Delete_INV"),
    START_COUNTING("Start_Counting"),
    INVENTORY_ENTRY("Inventory_Entry"),

    // onHold
    DO_ONHOLD("Do_OnHold"),
    DO_NOT_ONHOLD("Do_Not_OnHold"),

    // holdException
    CREATED_HOLD_EXCEPTION("Created_Hold_Exception"),

    // move
    DO_MOVE("Do_Move"),

    // task
    CREATE_TASK("Create_Task"),

    // warehouse
    CREATE_WHS("Create_Whs"),
    DELETE_WHS("Delete_Whs"),
    UPDATE_WHS("Update_Whs"),

    // block
    CREATE_BOLCK("Create_Bolck"),
    UPDATE_BOLCK("Update_Bolck"),
    DELETE_BOLCK("Delete_Bolck"),

    // shelf
    CREATE_SHELF("Create_Shelf"),
    UPDATE_SHELF("Update_Shelf"),
    DELETE_SHELF("Delete_Shelf"),

    ;
    private String name;

    WmsMasterApiEventName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }
}
