# Fabos Security Backend



## 模块简介

其中保密仓严格控制权限，完全不依赖fabos-framework和fabos-business。 

在fabos-framework中视为第三方依赖进行管理。

fabos-security的版本及依赖都自行管理。

该模块也可通过maven-release-plugin进行升级。

## 关于该仓库中的base-interface、core-comn、core-service包的说明

这三个包由3.0.4-SNAPSHOT的主分支中迁移而来，形成security仓库独有的基础包。

理论上security仓库不应再依赖这三个包，但是由于直接去掉依赖会造成不可预估的影响，所以暂时迁移进仓库并增加security前缀，不会影响主分支的包。

## 针对license模块的特别说明

由于license包将视为第三方依赖在fabos产品中使用，license包在引入fabos中时，只可引入RELEASE版本的包。

