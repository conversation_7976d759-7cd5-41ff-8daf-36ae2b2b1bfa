package cec.jiutian.core.config;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.cache.annotation.CachingConfigurerSupport;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.interceptor.KeyGenerator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.io.Serializable;
import java.time.Duration;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

@EnableCaching
@Configuration
@AutoConfigureAfter(RedisAutoConfiguration.class)
public class RedisConfig extends CachingConfigurerSupport {

    @Value("${base.redis.cacheTime}")
    private Long cacheTime;

    @Value("${base.redis.cacheNullValues}")
    private Boolean cacheNullValues;

    @Bean
    @Override
    public KeyGenerator keyGenerator() {
        return (target, method, params) -> {
            StringBuilder sb = new StringBuilder();
            sb.append(target.getClass().getName());
            sb.append(":");
            sb.append(method.getName());
            if (ArrayUtils.isNotEmpty(params)) {
                String collect = Arrays.stream(params).map(x -> x.getClass().getSimpleName())
                        .collect(Collectors.joining(",", "(", ")"));
                sb.append(collect);
            }
            return sb.toString();
        };
    }

    @Bean
    public RedisCacheManager cacheManager(RedisConnectionFactory redisConnectionFactory) {

        // 设置缓存有效时间
        RedisCacheConfiguration errorCodeCacheConfig = RedisCacheConfiguration.defaultCacheConfig()
                .entryTtl(Duration.ofSeconds(cacheTime));
        if (!cacheNullValues) {
            errorCodeCacheConfig.disableCachingNullValues();
        }
        // 缓存配置
        Map<String, RedisCacheConfiguration> redisCacheConfigurationMap = new HashMap<>();
        redisCacheConfigurationMap.put("error-code", errorCodeCacheConfig);

        // 初始化RedisCacheManager
        RedisCacheManager cacheManager = RedisCacheManager.builder(redisConnectionFactory)
                .withInitialCacheConfigurations(redisCacheConfigurationMap).transactionAware().build();
        cacheManager.afterPropertiesSet();
        return cacheManager;
    }

    /**
     * 配置自定义baseRedisTemplate
     *
     * @return
     */
    @Bean
    public RedisTemplate<String, Serializable> baseRedisTemplate(LettuceConnectionFactory redisConnectionFactory) {
        RedisTemplate<String, Serializable> errorCodeRedisTemplate = new RedisTemplate<>();
        initRedisTemplate(errorCodeRedisTemplate, redisConnectionFactory);
        return errorCodeRedisTemplate;
    }

    @Bean
    public RedisTemplate<String, Long> longRedisTemplate(LettuceConnectionFactory redisConnectionFactory) {
        RedisTemplate<String, Long> errorCodeRedisTemplate = new RedisTemplate<>();
        initRedisTemplate(errorCodeRedisTemplate, redisConnectionFactory);
        return errorCodeRedisTemplate;
    }

    @Bean
    public RedisTemplate<String, Integer> intRedisTemplate(LettuceConnectionFactory redisConnectionFactory) {
        RedisTemplate<String, Integer> errorCodeRedisTemplate = new RedisTemplate<>();
        initRedisTemplate(errorCodeRedisTemplate, redisConnectionFactory);
        return errorCodeRedisTemplate;
    }

    public void initRedisTemplate(RedisTemplate<String, ?> redisTemplate, LettuceConnectionFactory redisConnectionFactory) {
        Jackson2JsonRedisSerializer<Object> jackson2JsonRedisSerializer = new Jackson2JsonRedisSerializer<>(Object.class);
        //json转对象类，不设置默认的会将json转成hashmap
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        objectMapper.activateDefaultTyping(objectMapper.getPolymorphicTypeValidator(), ObjectMapper.DefaultTyping.NON_FINAL);
        jackson2JsonRedisSerializer.setObjectMapper(objectMapper);
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.setValueSerializer(jackson2JsonRedisSerializer);
        redisTemplate.setHashKeySerializer(new StringRedisSerializer());
        redisTemplate.setHashValueSerializer(jackson2JsonRedisSerializer);
        redisTemplate.setConnectionFactory(redisConnectionFactory);
    }
}
