package cec.jiutian.core.config;

import org.springframework.cloud.stream.config.BindingProperties;
import org.springframework.cloud.stream.config.BindingServiceProperties;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

@Configuration
public class MessageQueueConfig {

    private final ApplicationContext applicationContext;

    public MessageQueueConfig(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    @Bean
    public void setHistoryProducer() {
        BindingServiceProperties bindingServiceProperties = applicationContext.getBean(BindingServiceProperties.class);
        BindingProperties bindingProperties = new BindingProperties();
        bindingProperties.setBinder("littleRabbit");
        bindingProperties.setDestination("historyReportDestination");
        bindingProperties.setContentType("application/json");
        BindingProperties bindingPropertiesForFile = new BindingProperties();
        bindingPropertiesForFile.setBinder("littleRabbit");
        bindingPropertiesForFile.setDestination("fileDestination");
        bindingPropertiesForFile.setContentType("application/json");
        Map<String, BindingProperties> map = new HashMap<String, BindingProperties>();
        map.put("outputHistoryMessage", bindingProperties);
        map.put("outputFileMessage", bindingPropertiesForFile);
        bindingServiceProperties.setBindings(map);
    }
}
