package cec.jiutian.core.config;

public interface ServiceNames {
    String MES_BASE_ALARM = "mes-service-base-alarm";
    //	String MES_BASE_AUTH = "mes-service-base-auth";
    String MES_BASE_BOM = "mes-service-base-bom";
    String MES_BASE_CARRIER = "mes-service-base-carrier";
    String MES_BASE_CFM = "mes-service-base-cfm";
    //	String MES_BASE_CONFIG = "mes-service-base-config";
    String MES_BASE_EQP = "mes-service-base-eqp";
    String MES_BASE_FACTORY = "mes-service-base-factory";
    String MES_BASE_ORDER = "mes-service-order";
    String MES_BASE_PENCIL = "mes-service-base-pencil";
    String MES_BASE_WIP = "mes-service-wip";
    String WMS_MASTER = "wms-master";
    String FABOS_COMS = "fabos-coms-server";
    String DSP_MASTER = "dsp-master";
    String MESSAGE_MASTER = "message-master";
    String SCADA_MASTER = "scada-master";
    String SPC_MASTER = "spc-master";
    String WORKFLOW = "workflow-master";
    String MES_BASE_UPLOAD = "mes-service-base-upload";
    String FABOS_FEATURE = "fabos-base-feature";
    String FABOS_FOUNDATION = "fabos-base-foundation";
    String FABOS_MODELER = "fabos-modeler-factory";
    String FABOS_BASE_FILE = "fabos-base-file";
}
