package cec.jiutian.core.config;

import brave.Tracer;
import cec.jiutian.core.service.DataSourceService;
import cec.jiutian.core.service.TableIdConfigService;
import cec.jiutian.core.service.TokenAnalysisService;
import cec.jiutian.core.intercepter.AutoIdInterceptor;
import cec.jiutian.core.intercepter.DataDomainInsertInterceptor;
import cec.jiutian.core.intercepter.DataDomainQueryInterceptor;
import cec.jiutian.core.intercepter.HistoryInsertAndUpdateInterceptor;
import cec.jiutian.core.intercepter.HistoryUpdateOldObjectInterceptor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.boot.SpringBootConfiguration;

import jakarta.annotation.PostConstruct;

/**
 * @title: InterceptorConfig.java
 * @package cec.jiutian.core.config
 * @description: 定义ibatis拦截器注册机制，先注册后执行
 * @author: <EMAIL>
 * @date: 2022-4-19 16:08
 * @version: 2.5.3
 */
@SpringBootConfiguration
public class InterceptorConfig {
    private final TokenAnalysisService tokenAnalysisService;
    private final DataSourceService dataSourceService;
    private final SqlSessionFactory sqlSessionFactory;
    private final DataDomainEnv env;
    private final TableIdConfigService tableIdConfigService;
    private final Tracer tracer;

    public InterceptorConfig(TokenAnalysisService tokenAnalysisService
            , DataSourceService dataSourceService
            , SqlSessionFactory sqlSessionFactory
            , DataDomainEnv env, TableIdConfigService tableIdConfigService, Tracer tracer) {
        this.tokenAnalysisService = tokenAnalysisService;
        this.dataSourceService = dataSourceService;
        this.sqlSessionFactory = sqlSessionFactory;
        this.env = env;
        this.tableIdConfigService = tableIdConfigService;
        this.tracer = tracer;
    }

    @PostConstruct
    public void registeredInterceptors() {
        org.apache.ibatis.session.Configuration configuration = sqlSessionFactory.getConfiguration();
        configuration.addInterceptor(new HistoryInsertAndUpdateInterceptor(tracer));
        configuration.addInterceptor(new DataDomainInsertInterceptor(env, tokenAnalysisService, dataSourceService));
        configuration.addInterceptor(new HistoryUpdateOldObjectInterceptor(tracer));
        configuration.addInterceptor(new DataDomainQueryInterceptor(env, tokenAnalysisService, dataSourceService));
        configuration.addInterceptor(new AutoIdInterceptor(tableIdConfigService));
    }
}
