package cec.jiutian.core.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @title: DataDomainEnv.java
 * @package cec.jiutian.core.config
 * @description: 数据域拦截器的环境变量
 * @author: <EMAIL>
 * @date: 2022-4-20 9:08
 * @version: 2.5.3
 */
@Component
public class DataDomainEnv {
    @Value("${spring.application.name:app}")
    public String serverName;
    @Value("#{'${data.domain.exclude.uris:aaa}'.split(',')}")
    public String[] excludeUris;
    @Value("#{'${data.domain.exclude.login.uris:aaa}'.split(',')}")
    public String[] loginExcludeUris;
    @Value("${data.domain.valid.flag:Y}")
    public String dataDomainValidFlag;
    @Value("${spring.datasource.url}")
    public String dataBaseName = "";
}
