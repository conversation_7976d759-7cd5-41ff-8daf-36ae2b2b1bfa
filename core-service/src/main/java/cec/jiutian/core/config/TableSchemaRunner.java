package cec.jiutian.core.config;

import cec.jiutian.core.util.BaseRedisCacheUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
public class TableSchemaRunner implements ApplicationRunner {

    @Value("${spring.datasource.url}")
    protected String dataBaseName = "";

    private final SqlSessionFactory sqlSessionFactory;

    public TableSchemaRunner( SqlSessionFactory sqlSessionFactory) {
        this.sqlSessionFactory = sqlSessionFactory;
    }

    public void getTableSchema() {
        SqlSession sqlSession = null;
        try {
            sqlSession = sqlSessionFactory.openSession();
            Connection con = sqlSession.getConnection();
            DatabaseMetaData metaData = con.getMetaData();
            ResultSet tables = metaData.getTables(null, null, "%", new String[]{"TABLE"});
            while (tables.next()) {
                // 列的个数
                int columnCount = tables.getMetaData().getColumnCount();
                List<String> colNamesList;
                String TABLE_NAME = tables.getString("TABLE_NAME");
                colNamesList = getTableStructure(TABLE_NAME, metaData);
                // 先删再存
                try{
                    BaseRedisCacheUtil.hDelete(dataBaseName, TABLE_NAME.toUpperCase());
                }catch (Exception e){
                    e.printStackTrace();
                }
                BaseRedisCacheUtil.hPutForObject(dataBaseName, TABLE_NAME.toUpperCase(),  colNamesList.stream().map(x->x.toUpperCase()).collect(Collectors.toList()));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            if (null != sqlSession) {
                sqlSession.close();
            }
        }
    }

    public static List<String> getTableStructure(String tableName, DatabaseMetaData metaData) {
        List<String> columnModelList = new ArrayList<>();
        try {
            ResultSet columnSet = metaData.getColumns(null, "%", tableName, "%");
            while (columnSet.next()) {
                String column = columnSet.getString("COLUMN_NAME");
                columnModelList.add(column);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return columnModelList;
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
        getTableSchema();
    }
}
