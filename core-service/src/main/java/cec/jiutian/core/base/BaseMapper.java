package cec.jiutian.core.base;

import cec.jiutian.core.mapper.DeleteBatchMapper;
import cec.jiutian.core.mapper.InsertBatchMapper;
import cec.jiutian.core.mapper.SelectForUpdateMapper;
import cec.jiutian.core.mapper.SelectTreeMapper;
import cec.jiutian.core.mapper.UpdateBatchMapper;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Options;
import tk.mybatis.mapper.additional.update.force.UpdateByPrimaryKeySelectiveForceMapper;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.provider.base.BaseInsertProvider;

/**
 * 通用Mapper接口,其他接口继承该接口即可
 *
 * @param <T> 不能为空
 */
public interface BaseMapper<T> extends Mapper<T>, InsertBatchMapper<T>, UpdateBatchMapper<T>, DeleteBatchMapper<T>
        , SelectForUpdateMapper<T>, UpdateByPrimaryKeySelectiveForceMapper<T>, SelectTreeMapper<T> {

    /**
     * 保存一个实体，null的属性不会保存，会使用数据库默认值
     *
     * @param record
     * @return
     */
    @Override
    @InsertProvider(type = BaseInsertProvider.class, method = "dynamicSQL")
    @Options(useGeneratedKeys = true, keyProperty = "identifier", keyColumn = "id")
    int insertSelective(T record);
}
