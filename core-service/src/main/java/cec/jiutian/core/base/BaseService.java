package cec.jiutian.core.base;

import cec.jiutian.core.comn.util.StringUtils;
import cec.jiutian.core.exception.MesErrorCodeException;
import cec.jiutian.core.intercepter.DynamicSortInterceptor;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import jakarta.validation.Validator;
import java.io.Serializable;
import java.util.List;

public abstract class BaseService<T, PK extends Serializable> implements IBaseService<T, PK> {

    @Autowired
    protected BaseMapper<T> baseMapper;
    @Autowired
    protected Validator validator;

    @Value("${mybatis.configuration.database-id:mysql}")
    protected String databaseId;

    @Override
    public int save(T entity) {
        return baseMapper.insertSelective(entity);
    }

    @Override
    public int update(T entity) {
        return baseMapper.updateByPrimaryKey(entity);
    }

    @Override
    public int updateSelective(T entity) {
        return baseMapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    public T getById(PK id) {
        return baseMapper.selectByPrimaryKey(id);
    }

    @Override
    public T getByIdForUpdate(PK id) {
        return baseMapper.selectByPrimaryKeyForUpdate(id);
    }

    @Override
    public int deleteById(PK id) {
        return baseMapper.deleteByPrimaryKey(id);
    }

    /**
     * service层的批量插入方法，实际采用循环插入的方式得到自增ID。不需要取回自增ID和复合主键的情况下建议使用mapper层的批量插入方法。
     */
    @Override
    public int insertBatch(List<T> recordList) {
        if (StringUtils.equalsIgnoreCase(databaseId, "oracle")) {
            return this.insertList(recordList);
        } else if (StringUtils.equalsIgnoreCase(databaseId, "mysql")) {
            return this.insertList(recordList);
        }
        return baseMapper.insertBatch(recordList);
    }

    private int insertList(List<T> recordList) {
        int count = 0;
        if (null != recordList) {
            for (T record : recordList) {
                count += this.save(record);
            }
        }
        return count;
    }

    @Override
    public int updateBatchByPrimaryKey(List<T> recordList) {
        return baseMapper.updateBatchByPrimaryKey(recordList);
    }

    @Override
    public int updateBatch(List<T> recordList) {
        return baseMapper.updateBatch(recordList);
    }

    @Override
    public int deleteBatchByPrimaryKey(List<T> recordList) {
        return baseMapper.deleteBatchByPrimaryKey(recordList);
    }

    public void startPage(Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize, true, null, true).boundSqlInterceptor(new DynamicSortInterceptor());
    }

    protected void throwException(String errorCode, String... msgs) {
        MesErrorCodeException exception = new MesErrorCodeException(errorCode);
        for (String msg : msgs) {
            exception.addMsgItem(msg);
        }
        throw exception;
    }

    protected void throwException(Exception exp, String errorCode, String... msgs) {
        MesErrorCodeException exception = new MesErrorCodeException(exp, errorCode);
        for (String msg : msgs) {
            exception.addMsgItem(msg);
        }
        throw exception;
    }
}
