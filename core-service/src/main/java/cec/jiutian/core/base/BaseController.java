package cec.jiutian.core.base;

import cec.jiutian.core.result.ErrorCodeResult;
import cec.jiutian.core.util.BizParamUtils;
import cec.jiutian.core.util.FillerUtil;
import cec.jiutian.security.base.dto.BaseQueryDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.ModelAttribute;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import jakarta.validation.Validator;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.Response.Status;

@Component
public class BaseController {

    @Autowired
    protected Validator validator;
    protected HttpServletRequest request;
    protected HttpServletResponse response;
    protected HttpSession session;

    @ModelAttribute
    public void setReqAndRes(HttpServletRequest req, HttpServletResponse res) {
        this.request = req;
        this.response = res;
        this.session = req.getSession();
    }

    /*
     * @Description 返回操作成功后的数据
     * @Date 9:14 2022-3-28
     * @Param [input, output, msg, path, lastEventUser, lastEventComment]
     * @return javax.ws.rs.core.Response
     * 已废弃，请使用三参数或两参数方法
     **/
    @Deprecated
    protected Response respSuccessResult(Object input, Object output, String msg, String path,
                                         String lastEventUser, String lastEventComment) {
        ErrorCodeResult errorCodeResult = new ErrorCodeResult();
        errorCodeResult.setCode("000");
        FillerUtil.fillErrorCodeResult(errorCodeResult);
        BaseResponse baseResponse = new BaseResponse();
        baseResponse.setStatus(Status.OK.getStatusCode());
        baseResponse.setEntity(errorCodeResult);
        FillerUtil.setOutput(baseResponse, output);
        return baseResponse;
    }

    /*
     * @Description 返回操作失败后的数据
     * @Date 9:14 2022-3-28
     * @Param [input, output, msg, path, lastEventUser, lastEventComment]
     * @return javax.ws.rs.core.Response
     * 已废弃，请使用三参数或两参数方法
     **/
    @Deprecated
    protected Response respFaultResult(Object input, Object output, String msg, String path,
                                       String lastEventUser, String lastEventComment) {
        ErrorCodeResult errorCodeResult = new ErrorCodeResult();
        errorCodeResult.setCode("111");
        FillerUtil.fillErrorCodeResult(errorCodeResult);
        BaseResponse baseResponse = new BaseResponse();
        baseResponse.setStatus(Status.SERVICE_UNAVAILABLE.getStatusCode());
        baseResponse.setEntity(errorCodeResult);
        FillerUtil.setOutput(baseResponse, output);
        return baseResponse;
    }

    /**
     * <p>简单合并返回成功和失败操作</p>
     *
     * @param result
     * @param input
     * @param output
     * @param successMsg
     * @param faultMsg
     * @param path
     * @param lastEventUser
     * @param lastEventComment
     * @return
     * <AUTHOR>
     * 已废弃，请使用三参数或两参数方法
     **/
    @Deprecated
    protected Response respResult(boolean result, Object input, Object output, String successMsg, String faultMsg, String path, String lastEventUser, String lastEventComment) {
        return result ? this.respSuccessResult(input, output, successMsg, path, lastEventUser, lastEventComment)
                : this.respFaultResult(input, output, faultMsg, path, lastEventUser, lastEventComment);
    }

    /*
     * @Description 返回操作成功后的数据
     * @Date 9:14 2022-3-28
     * @Param [input, output, msg, path, lastEventUser, lastEventComment]
     * @return javax.ws.rs.core.Response
     **/
    protected Response respSuccessResult(Object output, String message) {
        ErrorCodeResult errorCodeResult = new ErrorCodeResult();
        errorCodeResult.setCode("000");
        errorCodeResult.setMessage(message);
        FillerUtil.fillErrorCodeResult(errorCodeResult);
        BaseResponse baseResponse = new BaseResponse();
        baseResponse.setStatus(Status.OK.getStatusCode());
        baseResponse.setEntity(errorCodeResult);
        BaseQueryDTO queryDTO = (BaseQueryDTO) BizParamUtils.getParam("BaseQueryDTO");
        if(null != queryDTO){
            output = FillerUtil.filtrationRowColumn(queryDTO, output);
        }
        FillerUtil.setOutput(baseResponse, output);
        return baseResponse;
    }

    protected Response respSuccessResult(String message) {
        return respSuccessResult(null, message);
    }

    /*
     * @Description 返回操作失败后的数据
     * @Date 9:14 2022-3-28
     * @Param [input, output, msg, path, lastEventUser, lastEventComment]
     * @return javax.ws.rs.core.Response
     **/
    protected Response respFaultResult(Object output, String message) {
        ErrorCodeResult errorCodeResult = new ErrorCodeResult();
        errorCodeResult.setCode("111");
        errorCodeResult.setMessage(message);
        FillerUtil.fillErrorCodeResult(errorCodeResult);
        BaseResponse baseResponse = new BaseResponse();
        baseResponse.setStatus(Status.SERVICE_UNAVAILABLE.getStatusCode());
        baseResponse.setEntity(errorCodeResult);
        FillerUtil.setOutput(baseResponse, output);
        return baseResponse;
    }

    protected Response respFaultResult(String message) {
        return respFaultResult(null, message);
    }

    /**
     * <p>简单合并返回成功和失败操作</p>
     *
     * @param result
     * @param output
     * @param successMsg
     * @param faultMsg
     * @return
     * <AUTHOR>
     */
    protected Response respResult(boolean result, Object output, String successMsg, String faultMsg) {
        return result ? this.respSuccessResult(output, successMsg)
                : this.respFaultResult(output, faultMsg);
    }

    protected Response respResult(boolean result, String successMsg, String faultMsg) {
        return respResult(result, null, successMsg, faultMsg);
    }
}
