package cec.jiutian.core.base;

import cec.jiutian.core.entity.Domain;
import cec.jiutian.core.exception.MesErrorCodeException;
import cec.jiutian.core.intercepter.DynamicSortInterceptor;
import cec.jiutian.core.intercepter.GeneralQueryInterceptor;
import cec.jiutian.core.util.SpringContextUtils;
import cec.jiutian.security.base.annotation.UniqueColumn;
import cec.jiutian.security.base.entity.BaseEntity;
import cec.jiutian.core.comn.errorCode.BaseErrorCode;
import cec.jiutian.core.comn.util.BeanUtils;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import tk.mybatis.mapper.entity.Example;

import jakarta.validation.Validator;
import java.io.Serializable;
import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/3/3
 * @warning 批量插入自动保存历史功能不适用于Oracle数据库的自增主键表
 */
@Slf4j
public abstract class BaseDomainService<E, D extends Domain<E>, PK extends Serializable> implements IBaseService<D, PK> {
    @Autowired
    protected BaseMapper<E> baseMapper;
    @Autowired
    protected Validator validator;
    @Value("${mybatis.configuration.database-id:mysql}")
    protected String databaseId;
    private DomainClassCache domainClassCache;

    @Override
    public int save(D domain) {
        return save(domain, false);
    }

    public int save(D domain, boolean compositeKey) {
        if (domain == null || domain.getEntity() == null) {
            return 0;
        }
        domain.setAuditField();
        int insert = 0;
        if (compositeKey) {
            insert = baseMapper.insert(domain.getEntity());
        } else {
            insert = baseMapper.insertSelective(domain.getEntity());

        }
        return insert;
    }
    @Override
    public int update(D domain) {
        return update(domain, false);
    }

    public int update(D domain, boolean isHistory) {
        if (domain == null || domain.getEntity() == null) {
            return 0;
        }
        domain.setAuditField();
        if (isHistory) {
            saveHistory(domain);
        }
        return baseMapper.updateByPrimaryKey(domain.getEntity());
    }

    @Override
    public int updateSelective(D domain) {
        return updateSelective(domain, false);
    }

    public int updateSelective(D domain, boolean isHistory) {
        if (domain == null || domain.getEntity() == null) {
            return 0;
        }
        domain.setAuditField();
        if (isHistory) {
            saveHistory(domain);
        }
        return baseMapper.updateByPrimaryKeySelective(domain.getEntity());
    }

    @Override
    public D getById(PK id) {
        return getById(id, false);
    }

    public D checkExistById(PK id, String message, Boolean isForUpdate) {
        D d = getById(id, isForUpdate);
        if (d == null) {
            MesErrorCodeException exception = new MesErrorCodeException(BaseErrorCode.DATA_NOT_EXIST.getCode())
                    .addMsgItem(message + " " + id.toString());
            log.error("{}", exception.getMessage(), exception);
            throw exception;
        }
        return d;
    }

    public D checkExistById(PK id) {
        return checkExistById(id, "", true);
    }

    public D checkExistById(PK id, String message) {
        return checkExistById(id, message, true);
    }

    public void checkUniqueById(PK id, Boolean isForUpdate) {
        D d = getById(id, isForUpdate);
        if (d != null) {
            MesErrorCodeException exception = new MesErrorCodeException(BaseErrorCode.DATA_EXISTED.getCode())
                    .addMsgItem(id.toString());
            log.error("{}", exception.getMessage(), exception);
            throw exception;
        }
    }

    public void checkUniqueById(PK id) {
        checkUniqueById(id, false);
    }

    @SuppressWarnings("unchecked")
    public D getById(PK id, boolean isForUpdate) {
        E entity = isForUpdate ? baseMapper.selectByPrimaryKeyForUpdate(id) : baseMapper.selectByPrimaryKey(id);
        if (entity == null) {
            return null;
        }
        if (domainClassCache == null || domainClassCache.getConstructor() == null || domainClassCache.getSetEntity() == null) {
            initDomainClassCache();
        }
        try {
            D d = (D) domainClassCache.getConstructor().newInstance();
            domainClassCache.getSetEntity().invoke(d, entity);
            return d;
        } catch (InstantiationException | IllegalAccessException | InvocationTargetException e) {
            MesErrorCodeException exception = new MesErrorCodeException(e, BaseErrorCode.EXCEPTION.getCode());
            log.error("{}", exception.getMessage(), exception);
            throw exception;
        }
    }

    @Override
    public D getByIdForUpdate(PK id) {
        return getById(id, true);
    }

    @Override
    public int deleteById(PK id) {
        return deleteById(id, false);
    }

    public int deleteById(PK id, boolean isHistory) {
        if (isHistory) {
            D domain = getById(id);
            domain.setAuditField();
            saveHistory(domain);
        }
        return baseMapper.deleteByPrimaryKey(id);
    }

    public int delete(D domain) {
        return delete(domain, false);
    }

    private int delete(D domain, boolean isHistory) {
        int result = baseMapper.deleteByPrimaryKey(domain.getEntity());
        if (isHistory) {
            domain.setAuditField();
            saveHistory(domain);
        }
        return result;
    }

    public int saveBatch(List<D> recordList) {
        return insertBatch(recordList, false);
    }

    @Override
    public int insertBatch(List<D> recordList) {
        return insertBatch(recordList, false);
    }

    public int insertBatch(List<D> recordList, boolean compositeKey) {
        int result = 0;
        if (CollectionUtils.isNotEmpty(recordList)) {
            recordList.forEach(Domain::setAuditField);
            List<E> collect = recordList.stream().map(D::getEntity).filter(Objects::nonNull).collect(Collectors.toList());
            if (compositeKey) {
                result = baseMapper.insertBatchCompositeKey(collect);
            } else {
                result = baseMapper.insertBatch(collect);

            }
        }
        return result;
    }

    @Override
    public int updateBatchByPrimaryKey(List<D> recordList) {
        return updateBatchByPrimaryKey(recordList, false);
    }

    @Override
    public int updateBatch(List<D> recordList) {
        return updateBatchByPrimaryKey(recordList);
    }

    public int updateBatchByPrimaryKey(List<D> recordList, boolean isHistory) {
        int result = 0;
        if (CollectionUtils.isNotEmpty(recordList)) {
            recordList.forEach(Domain::setAuditField);
            List<E> collect = recordList.stream().map(D::getEntity).filter(Objects::nonNull).collect(Collectors.toList());
            result = baseMapper.updateBatch(collect);
            if (isHistory) {
                saveHistories(recordList);
            }
        }
        return result;
    }

    @Override
    public int deleteBatchByPrimaryKey(List<D> recordList) {
        return deleteBatchByPrimaryKey(recordList, false);
    }

    public int deleteBatch(List<D> recordList) {
        return deleteBatchByPrimaryKey(recordList, false);
    }

    public int deleteBatch(List<D> recordList, boolean isHistory) {
        return deleteBatchByPrimaryKey(recordList, isHistory);
    }

    public int deleteBatchByPrimaryKey(List<D> recordList, boolean isHistory) {
        int result = 0;
        if (CollectionUtils.isNotEmpty(recordList)) {
            recordList.forEach(Domain::setAuditField);
            List<E> collect = recordList.stream().map(D::getEntity).filter(Objects::nonNull).collect(Collectors.toList());
            result = baseMapper.deleteBatchByPrimaryKey(collect);
            if (isHistory) {
                saveHistories(recordList);
            }
        }
        return result;
    }

    public void startPage(Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize, true, null, true)
                .boundSqlInterceptor(DynamicSortInterceptor.getInstance())
                .boundSqlInterceptor(GeneralQueryInterceptor.getInstance());
    }

    @SuppressWarnings("unchecked")
    private void initDomainClassCache() {
        Class<D> dClass = (Class<D>) ((ParameterizedType) getClass().getGenericSuperclass()).getActualTypeArguments()[1];
        try {
            Method setEntity;
            if (dClass.getSuperclass().getSimpleName().equals("AbstractDomain")) {
                setEntity = dClass.getMethod("setEntity", BaseEntity.class);
            } else {
                setEntity = dClass.getMethod("setEntity", Object.class);
            }
            Constructor<? super D> constructor = dClass.getConstructor();
            this.domainClassCache = new DomainClassCache(constructor, setEntity);
        } catch (NoSuchMethodException e) {
            MesErrorCodeException exception = new MesErrorCodeException(e, BaseErrorCode.EXCEPTION.getCode());
            log.error("{}", exception.getMessage(), exception);
            throw exception;
        }
    }

    private void saveHistory(D domain) {
        E entity = domain.getEntity();
        Class<?> aClass = entity.getClass();
        try {
            Class<?> history = Class.forName(aClass.getName().replace("PO", "HistoryPO"));
            Object tHistory = history.newInstance();
            BeanUtils.copyProperties(entity, tHistory);
            String simpleName = history.getSimpleName();
            String first = simpleName.replaceFirst(String.valueOf(simpleName.charAt(0)), String.valueOf(Character.toLowerCase(simpleName.charAt(0))));
            Object historyMapper = SpringContextUtils.getBean(first.replace("PO", "Mapper"));
            Method historySave = historyMapper.getClass().getMethod("insertSelective", Object.class);
            historySave.invoke(historyMapper, tHistory);
        } catch (ClassNotFoundException | InstantiationException | IllegalAccessException | NoSuchMethodException | InvocationTargetException e) {
            MesErrorCodeException exception = new MesErrorCodeException(e, BaseErrorCode.EXCEPTION.getCode());
            log.error("{}", exception.getMessage(), exception);
            throw exception;
        }
    }

    private void saveHistories(List<D> domains) {
        if (null == domains || domains.size() == 0) {
            return;
        }
        E entity = domains.get(0).getEntity();
        Class<?> aClass = entity.getClass();
        try {
            Class<?> history = Class.forName(aClass.getName().replace("PO", "HistoryPO"));
            List<Object> tHistoryList = new ArrayList<>();
            for (D d : domains) {
                d.setAuditField();
                entity = d.getEntity();
                Object tHistory = history.newInstance();
                BeanUtils.copyProperties(entity, tHistory);
                tHistoryList.add(tHistory);
            }
            String simpleName = history.getSimpleName();
            String first = simpleName.replaceFirst(String.valueOf(simpleName.charAt(0)), String.valueOf(Character.toLowerCase(simpleName.charAt(0))));
            Object historyMapper = SpringContextUtils.getBean(first.replace("PO", "Mapper"));
            Method historySave = historyMapper.getClass().getMethod("insertBatch", List.class);
            historySave.invoke(historyMapper, tHistoryList);
        } catch (ClassNotFoundException | InstantiationException | IllegalAccessException | NoSuchMethodException | InvocationTargetException e) {
            MesErrorCodeException exception = new MesErrorCodeException(e, BaseErrorCode.EXCEPTION.getCode());
            log.error("{}", exception.getMessage(), exception);
            throw exception;
        }
    }

    public class DomainClassCache {
        private final Constructor<? super D> constructor;
        private final Method setEntity;

        public DomainClassCache(Constructor<? super D> constructor, Method setEntity) {
            this.constructor = constructor;
            this.setEntity = setEntity;
        }

        public Constructor<? super D> getConstructor() {
            return constructor;
        }

        public Method getSetEntity() {
            return setEntity;
        }
    }

    public void checkUniqueColumns(D domain) {
        E entity = domain.getEntity();
        if (null == entity) {
            return;
        }
        Class<?> aClass = entity.getClass();
        try {
            Boolean checkUniqueFlag = false;
            Example example = new Example(aClass);
            Example.Criteria criteria = example.createCriteria();
            List<Field> fields = BeanUtils.getAllFields(aClass);
            StringBuilder warningStr = new StringBuilder();
            for (Field field : fields) {
                field.setAccessible(true);
                if (!field.isAnnotationPresent(UniqueColumn.class)) {
                    continue;
                }
                Object fieldValue = field.get(entity);
                if (null == fieldValue) {
                    return;
                }
                criteria.andEqualTo(field.getName(), fieldValue);
                warningStr.append("{")
/*                        .append(field.getName())
                        .append(":")*/
                        .append(fieldValue)
                        .append("}");
                checkUniqueFlag = true;
            }
            if (!checkUniqueFlag) {
                return;
            }
            String simpleName = aClass.getSimpleName();
            String first = simpleName.replaceFirst(String.valueOf(simpleName.charAt(0)), String.valueOf(Character.toLowerCase(simpleName.charAt(0))));
            Object mapper = SpringContextUtils.getBean(first.replace("PO", "Mapper"));
            Method selectByExample = mapper.getClass().getMethod("selectByExample", Object.class);
            List<E> poList = (List<E>) selectByExample.invoke(mapper, example);
            if (CollectionUtils.isNotEmpty(poList)) {
                MesErrorCodeException exception = new MesErrorCodeException(BaseErrorCode.DATA_EXISTED.getCode())
                        .addMsgItem(warningStr.toString());
                log.error("{}", exception.getMessage(), exception);
                throw exception;
            }

        } catch (IllegalAccessException | NoSuchMethodException | InvocationTargetException e) {
            log.error("{}", e.getMessage());
            MesErrorCodeException exception = new MesErrorCodeException(e, BaseErrorCode.EXCEPTION.getCode());
            log.error("{}", exception.getMessage(), exception);
            throw exception;
        }
    }

}
