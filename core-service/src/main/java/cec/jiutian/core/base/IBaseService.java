package cec.jiutian.core.base;

import java.io.Serializable;
import java.util.List;

public interface IBaseService<T, PK extends Serializable> {

    /**
     * <p>添加一条记录</p>
     *
     * @param entity 要添加的实体对象
     * @return
     * <AUTHOR>
     */
    int save(T entity);

    /**
     * <p>更新一条记录</p>
     *
     * @param entity 要更新的实体对象
     * @return
     * <AUTHOR>
     */
    int update(T entity);

    /**
     * <p>选择性的更新一条记录，实体中没有的值不更新</p>
     *
     * @param entity 要更新的实体对象
     * @return
     * <AUTHOR>
     */
    int updateSelective(T entity);

    /**
     * <p>根据主键ID获取一条记录</p>
     *
     * @param id 主键ID
     * @return 表记录对应的实体对象
     * <AUTHOR>
     */
    T getById(PK id);

    /**
     * <p>根据主键ID获取一条记录,并加上悲观锁</p>
     *
     * @param id 主键ID
     * @return 表记录对应的实体对象
     * <AUTHOR>
     */
    T getByIdForUpdate(PK id);

    /**
     * <p>根据主键ID删除一条记录</p>
     *
     * @param id 主键ID
     * @return
     * <AUTHOR>
     */
    int deleteById(PK id);

    /**
     * <p>批量插入</p>
     *
     * @param id 主键ID
     * @return
     * <AUTHOR>
     */
    int insertBatch(List<T> recordList);

    /**
     * <p>批量更新</p>
     *
     * @param id 主键ID
     * @return
     * <AUTHOR>
     */
    int updateBatchByPrimaryKey(List<T> recordList);

    /**
     * <p>批量更新</p>
     *
     * @return
     * @des 根据主键更新所有的字段，包括为null的字段。使用于所有情况（单主键复合主键皆可，MySQL、Oracle都支持）
     * <AUTHOR>
     */
    int updateBatch(List<T> recordList);

    /**
     * <p>批量删除</p>
     *
     * @param id 主键ID
     * @return
     * <AUTHOR>
     */
    int deleteBatchByPrimaryKey(List<T> recordList);

}
