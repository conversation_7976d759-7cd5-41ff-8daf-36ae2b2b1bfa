package cec.jiutian.core.base;

import cec.jiutian.core.result.Pager;
import lombok.Data;

import javax.ws.rs.core.MultivaluedMap;
import javax.ws.rs.core.Response;
/**
 * @author: <EMAIL>
 * @date: 2022-3-28 17:40
 * 前端接收的返回
 */
@Data
public class BaseResponse extends Response{
    Object output;
    Pager pager;
    int status;
    Object entity;

    @Override
    public MultivaluedMap<String, Object> getMetadata() {
        return null;
    }
}
