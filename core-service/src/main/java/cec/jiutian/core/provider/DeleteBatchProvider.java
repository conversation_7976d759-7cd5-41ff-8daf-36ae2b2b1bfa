package cec.jiutian.core.provider;

import cec.jiutian.core.comn.errorCode.BaseErrorCode;
import cec.jiutian.core.exception.MesErrorCodeException;
import org.apache.ibatis.mapping.MappedStatement;
import tk.mybatis.mapper.entity.EntityColumn;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;
import tk.mybatis.mapper.mapperhelper.MapperTemplate;
import tk.mybatis.mapper.mapperhelper.SqlHelper;

import java.sql.SQLException;
import java.util.Iterator;
import java.util.Set;

public class DeleteBatchProvider extends MapperTemplate {

    public DeleteBatchProvider(Class<?> mapperClass, MapperHelper mapperHelper) {
        super(mapperClass, mapperHelper);
    }

    /**
     * 批量删除
     *
     * @param ms
     */
    public String deleteBatchForSinglePrimaryKey(MappedStatement ms) {
        final Class<?> entityClass = getEntityClass(ms);
        //获取单个主键的主键和属性名
        Set<EntityColumn> pks = EntityHelper.getPKColumns(entityClass);
        Iterator<EntityColumn> it = pks.iterator();
        String pkColumn = "";
        String pkProperty = "";
        while (it.hasNext()) {
            EntityColumn pk = it.next();
            pkColumn = pk.getColumn();
            pkProperty = pk.getProperty();
        }

        //开始拼sql
        StringBuilder sql = new StringBuilder();
        sql.append(SqlHelper.deleteFromTable(entityClass, tableName(entityClass)));
        sql.append(" WHERE ");
        sql.append(" " + pkColumn + " IN ");
        sql.append("<trim prefix=\"(\" suffix=\")\">");
        sql.append("<foreach collection=\"list\" separator=\", \" item=\"i\" index=\"index\" >");
        sql.append("#{i." + pkProperty + "}");
        sql.append("</foreach>");
        sql.append("</trim>");

        return sql.toString();
    }

    /**
     * 批量删除
     *
     * @param ms
     */
    public String deleteBatchByPrimaryKey(MappedStatement ms) {
        final Class<?> entityClass = getEntityClass(ms);
        //获取单个主键的主键和属性名
        Set<EntityColumn> pks = EntityHelper.getPKColumns(entityClass);
        Iterator<EntityColumn> it = pks.iterator();
        if (pks.isEmpty()) {
            SQLException sqlException = new SQLException("There is no primary key!");
            MesErrorCodeException exception = new MesErrorCodeException(sqlException, BaseErrorCode.SQL_OP_ERROR.getCode());
            throw exception;
        }
        String pkColumn = "";
        String pkProperty = "";

        //开始拼sql
        StringBuilder sql = new StringBuilder();
        sql.append(SqlHelper.deleteFromTable(entityClass, tableName(entityClass)));
        sql.append(" WHERE 1=1 AND ");
        sql.append("<foreach collection=\"list\" separator=\" OR \" item=\"i\" index=\"index\" >");
        sql.append("<trim prefix=\"(\" suffix=\")\">");
        if (it.hasNext()) {
            EntityColumn pk = it.next();
            pkColumn = pk.getColumn();
            pkProperty = pk.getProperty();
            sql.append(pkColumn);
            sql.append(" = #{i." + pkProperty + "}");
        }
        while (it.hasNext()) {
            EntityColumn pk = it.next();
            pkColumn = pk.getColumn();
            pkProperty = pk.getProperty();
            sql.append(" AND " + pkColumn);
            sql.append(" = #{i." + pkProperty + "}");
        }
        sql.append("</trim>");
        sql.append("</foreach>");

        return sql.toString();
    }
}
