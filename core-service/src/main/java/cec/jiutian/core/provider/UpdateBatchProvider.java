package cec.jiutian.core.provider;

import org.apache.ibatis.mapping.MappedStatement;
import tk.mybatis.mapper.entity.EntityColumn;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;
import tk.mybatis.mapper.mapperhelper.SqlHelper;
import tk.mybatis.mapper.provider.ExampleProvider;

import java.util.Iterator;
import java.util.Set;

public class UpdateBatchProvider extends ExampleProvider {

    private static final String POSTGRESQL = "postgresql";

    public UpdateBatchProvider(Class<?> mapperClass, MapperHelper mapperHelper) {
        super(mapperClass, mapperHelper);
    }

    @Deprecated
    public String updateBatchByPrimaryKey(MappedStatement ms) {
        String databaseId = ms.getConfiguration().getDatabaseId() == null ? "mysql" : ms.getConfiguration().getDatabaseId();
        String sql;
        switch (databaseId) {
            case "oracle":
                sql = updateBatchForOracle(ms);
                // sql = updateBatchMultiKeyForOracle(ms);
                break;
            default:
                sql = updateBatchForMySQL(ms);
                // sql = updateBatchMultiKeyForMySQL(ms);

                break;
        }
        return sql;
    }

    /**
     * 拼update sql, 使用case when方式，id为主键
     *
     * @param ms
     * @return
     */
    @Deprecated
    public String updateBatchByPrimaryKeySelective(MappedStatement ms) {
        final Class<?> entityClass = getEntityClass(ms);
        //开始拼sql
        StringBuilder sql = new StringBuilder();
        sql.append(SqlHelper.updateTable(entityClass, tableName(entityClass)));
        sql.append("<trim prefix=\"set\" suffixOverrides=\",\">");
        //获取单个主键的主键和属性名
        Set<EntityColumn> pks = EntityHelper.getPKColumns(entityClass);
        Iterator<EntityColumn> it = pks.iterator();
        String pkColumn = "";
        String pkProperty = "";
        while (it.hasNext()) {
            EntityColumn pk = it.next();
            pkColumn = pk.getColumn();
            pkProperty = pk.getProperty();
        }

        //获取全部列
        Set<EntityColumn> columnList = EntityHelper.getColumns(entityClass);
        for (EntityColumn column : columnList) {
            if (!column.isId() && column.isUpdatable()) {
                sql.append("  <trim prefix=\"" + column.getColumn() + " =case\" suffix=\"end,\">");
                sql.append("    <foreach collection=\"list\" item=\"i\" index=\"index\">");
                sql.append("      <if test=\"i." + column.getEntityField().getName() + "!=null\">");
                sql.append("         when " + pkColumn + "=#{i." + pkProperty + "} then #{i." + column.getEntityField().getName() + "}");
                sql.append("      </if>");
                sql.append("    </foreach>");
                sql.append("  </trim>");
            }
        }

        sql.append("</trim>");
        sql.append("WHERE");
        sql.append(" " + pkColumn + " IN ");
        sql.append("<trim prefix=\"(\" suffix=\")\">");
        sql.append("<foreach collection=\"list\" separator=\", \" item=\"i\" index=\"index\" >");
        sql.append("#{i." + pkProperty + "}");
        sql.append("</foreach>");
        sql.append("</trim>");

        return sql.toString();
    }

    /**
     * 拼update sql, 使用case when方式，id为主键
     *
     * @param ms
     * @return
     */
    @Deprecated
    public String updateBatchForOracle(MappedStatement ms) {
        final Class<?> entityClass = getEntityClass(ms);
        //开始拼sql
        StringBuilder sql = new StringBuilder();
        sql.append(SqlHelper.updateTable(entityClass, tableName(entityClass)));
        sql.append("<trim prefix=\"set\" suffixOverrides=\",\">");
        //获取单个主键的主键和属性名
        Set<EntityColumn> pks = EntityHelper.getPKColumns(entityClass);
        Iterator<EntityColumn> it = pks.iterator();
        String pkColumn = "";
        String pkProperty = "";

        EntityColumn pk = it.next();
        pkColumn = pk.getColumn();
        pkProperty = pk.getProperty();

        //获取全部列
        Set<EntityColumn> columnList = EntityHelper.getColumns(entityClass);
        for (EntityColumn column : columnList) {
            if (!column.isId() && column.isUpdatable()) {
                sql.append("  <trim prefix=\"" + column.getColumn() + " =case\" suffix=\"end,\">");
                sql.append("    <foreach collection=\"list\" item=\"i\" index=\"index\">");

                sql.append("         when (" + pkColumn + "=#{i." + pkProperty + "} ");
                while (it.hasNext()) {
                    pk = it.next();
                    pkColumn = pk.getColumn();
                    pkProperty = pk.getProperty();
                    sql.append(" and " + pkColumn + "=#{i." + pkProperty + "} ");
                }

                sql.append(") then ");
                if (column.getJavaType().getName().contains("String")) {
                    sql.append("         #{i." + column.getEntityField().getName() + "}");
                } else {
                    sql.append("      <if test=\"i." + column.getEntityField().getName() + "!=null\">");
                    sql.append("         #{i." + column.getEntityField().getName() + "}");
                    sql.append("      </if>");
                    sql.append("      <if test=\"i." + column.getEntityField().getName() + "==null\">");
                    sql.append("         null");
                    sql.append("      </if>");
                }
                sql.append("    </foreach>");
                sql.append("  </trim>");
            }
        }

        sql.append("</trim>");
        sql.append("WHERE 1=0 ");

        sql.append("<foreach collection=\"list\" item=\"i\" index=\"index\" >");

        it = pks.iterator();
        pk = it.next();
        pkColumn = pk.getColumn();
        pkProperty = pk.getProperty();
        sql.append(" or (" + pkColumn + "=#{i." + pkProperty + "} ");
        while (it.hasNext()) {
            pk = it.next();
            pkColumn = pk.getColumn();
            pkProperty = pk.getProperty();
            sql.append(" and " + pkColumn + "=#{i." + pkProperty + "} ");
        }
        sql.append(")");

        sql.append("</foreach>");

        return sql.toString();
    }

    /**
     * 拼update sql, 使用case when方式，id为主键
     * * 20211101修改：支持多字段的主键
     *
     * @param ms
     * @return
     */
    @Deprecated
    public String updateBatchMultiKeyForOracle(MappedStatement ms) {
        final Class<?> entityClass = getEntityClass(ms);
        //开始拼sql
        StringBuilder sql = new StringBuilder();
        sql.append(SqlHelper.updateTable(entityClass, tableName(entityClass)));
        sql.append("<trim prefix=\"set\" suffixOverrides=\",\">");
        //获取单个主键的主键和属性名
        Set<EntityColumn> pks = EntityHelper.getPKColumns(entityClass);
        Iterator<EntityColumn> it = pks.iterator();
/*        String pkColumn = "";
        String pkProperty = "";

        EntityColumn pk = it.next();
        pkColumn = pk.getColumn();
        pkProperty = pk.getProperty();*/

        //获取全部列
        Set<EntityColumn> columnList = EntityHelper.getColumns(entityClass);
        for (EntityColumn column : columnList) {
            if (!column.isId() && column.isUpdatable()) {
                sql.append("  <trim prefix=\"" + column.getColumn() + " =case\" suffix=\"end,\">");
                sql.append("    <foreach collection=\"list\" item=\"i\" index=\"index\">");

                sql.append(" when ( ");
                for (EntityColumn pk : pks) {
                    String pkColumn = pk.getColumn();
                    String pkProperty = pk.getProperty();
                    sql.append("  " + pkColumn + " =#{i." + pkProperty + "}" + "  and");
                }
                sql.delete(sql.length() - 4, sql.length());
                sql.append(") then ");
                if (column.getJavaType().getName().contains("String")) {
                    sql.append("         #{i." + column.getEntityField().getName() + "}");
                } else {
                    sql.append("      <if test=\"i." + column.getEntityField().getName() + "!=null\">");
                    sql.append("         #{i." + column.getEntityField().getName() + "}");
                    sql.append("      </if>");
                    sql.append("      <if test=\"i." + column.getEntityField().getName() + "==null\">");
                    sql.append("         null");
                    sql.append("      </if>");
                }
                sql.append("    </foreach>");
                sql.append("  </trim>");
            }
        }

        sql.append("</trim>");
        sql.append("WHERE ");
        sql.append("( ");
        for (EntityColumn pk : pks) {
            String pkColumn = pk.getColumn();
            sql.append(pkColumn + " ,");
        }
        sql.deleteCharAt(sql.length() - 1);
        sql.append(" ) IN ");
        sql.append("<trim prefix=\"(\" suffix=\")\">");
        sql.append("<foreach collection=\"list\" separator=\", \" item=\"i\" index=\"index\" >");
        sql.append("( ");
        for (EntityColumn pk : pks) {
            String pkProperty = pk.getProperty();
            sql.append("#{i." + pkProperty + "}" + " ,");
        }
        sql.deleteCharAt(sql.length() - 1);
        sql.append(" )");
        sql.append("</foreach>");
        sql.append("</trim>");
        return sql.toString();
    }

    /**
     * 拼update sql, 使用case when方式，id为主键
     *
     * @param ms
     * @return
     */
    @Deprecated
    public String updateBatchForMySQL(MappedStatement ms) {
        final Class<?> entityClass = getEntityClass(ms);
        //开始拼sql
        StringBuilder sql = new StringBuilder();
        sql.append(SqlHelper.updateTable(entityClass, tableName(entityClass)));
        sql.append("<trim prefix=\"set\" suffixOverrides=\",\">");
        //获取单个主键的主键和属性名
        Set<EntityColumn> pks = EntityHelper.getPKColumns(entityClass);
        Iterator<EntityColumn> it = pks.iterator();
        String pkColumn = "";
        String pkProperty = "";
        while (it.hasNext()) {
            EntityColumn pk = it.next();
            pkColumn = pk.getColumn();
            pkProperty = pk.getProperty();
        }
        //获取全部列
        Set<EntityColumn> columnList = EntityHelper.getColumns(entityClass);
        for (EntityColumn column : columnList) {
            if (!column.isId() && column.isUpdatable()) {
                sql.append("  <trim prefix=\"" + column.getColumn() + " =case\" suffix=\"end,\">");
                sql.append("    <foreach collection=\"list\" item=\"i\" index=\"index\">");
                sql.append("         when " + pkColumn + "=#{i." + pkProperty + "} then");
                if (column.getJavaType().getName().equals("java.util.Date")) {
                    sql.append(" timestamp");
                }
                sql.append(" #{i." + column.getEntityField().getName() + "}");
                sql.append("    </foreach>");
                sql.append("  </trim>");
            }
        }

        sql.append("</trim>");
        sql.append("WHERE");
        sql.append(" " + pkColumn + " IN ");
        sql.append("<trim prefix=\"(\" suffix=\")\">");
        sql.append("<foreach collection=\"list\" separator=\", \" item=\"i\" index=\"index\" >");
        sql.append("#{i." + pkProperty + "}");
        sql.append("</foreach>");
        sql.append("</trim>");

        return sql.toString();
    }

    /**
     * 拼update sql, 使用case when方式，id为主键
     * 20211101修改：支持多字段的主键
     *
     * @param ms
     * @return
     */
    @Deprecated
    public String updateBatchMultiKeyForMySQL(MappedStatement ms) {
        final Class<?> entityClass = getEntityClass(ms);
        //开始拼sql
        StringBuilder sql = new StringBuilder();
        sql.append(SqlHelper.updateTable(entityClass, tableName(entityClass)));
        sql.append("<trim prefix=\"set\" suffixOverrides=\",\">");
        //获取单个主键的主键和属性名
        Set<EntityColumn> pks = EntityHelper.getPKColumns(entityClass);
        Iterator<EntityColumn> it = pks.iterator();
//        String pkColumn = "";
//        String pkProperty = "";
        //获取全部列
        Set<EntityColumn> columnList = EntityHelper.getColumns(entityClass);
        for (EntityColumn column : columnList) {
            if (!column.isId() && column.isUpdatable()) {
                sql.append("  <trim prefix=\"" + column.getColumn() + " =case\" suffix=\"end,\">");
                sql.append("    <foreach collection=\"list\" item=\"i\" index=\"index\">");
                // sql.append("         when " + pkColumn + "=#{i." + pkProperty + "} then #{i." + column.getEntityField().getName() + "}");
                sql.append(" when ( ");
                for (EntityColumn pk : pks) {
                    String pkColumn = pk.getColumn();
                    String pkProperty = pk.getProperty();
                    sql.append("  " + pkColumn + " =#{i." + pkProperty + "}" + "  and");
                }
                sql.delete(sql.length() - 4, sql.length());
                sql.append(" ) then #{i." + column.getEntityField().getName() + "}");
                sql.append("    </foreach>");
                sql.append("  </trim>");
            }
        }
        sql.append("</trim>");
        sql.append("WHERE ");
        sql.append("( ");
        for (EntityColumn pk : pks) {
            String pkColumn = pk.getColumn();
            sql.append(pkColumn + " ,");
        }
        sql.deleteCharAt(sql.length() - 1);
        sql.append(" ) IN ");
        sql.append("<trim prefix=\"(\" suffix=\")\">");
        sql.append("<foreach collection=\"list\" separator=\", \" item=\"i\" index=\"index\" >");
        sql.append("( ");
        for (EntityColumn pk : pks) {
            String pkProperty = pk.getProperty();
            sql.append("#{i." + pkProperty + "}" + " ,");
        }
        sql.deleteCharAt(sql.length() - 1);
        sql.append(" )");
        sql.append("</foreach>");
        sql.append("</trim>");
        return sql.toString();
    }

    /**
     * 支持多主键的批量更新函数
     */
    public String updateBatch(MappedStatement ms) {
        String databaseId = ms.getConfiguration().getDatabaseId() == null ? "mysql" : ms.getConfiguration().getDatabaseId();
        final Class<?> entityClass = getEntityClass(ms);
        //开始拼sql
        StringBuilder sql = new StringBuilder();
        sql.append(SqlHelper.updateTable(entityClass, tableName(entityClass)));
        sql.append("<trim prefix=\"set\" suffixOverrides=\",\">");
        //获取单个主键的主键和属性名
        Set<EntityColumn> pks = EntityHelper.getPKColumns(entityClass);
        //获取全部列
        Set<EntityColumn> columnList = EntityHelper.getColumns(entityClass);
        for (EntityColumn column : columnList) {
            if (!column.isId() && column.isUpdatable()) {
                sql.append("  <trim prefix=\"").append(column.getColumn()).append(" =case\" suffix=\"end,\">");
                sql.append("    <foreach collection=\"list\" item=\"i\" index=\"index\">");
                sql.append("         when (");
                for (EntityColumn pk : pks) {
                    String pkColumn = pk.getColumn();
                    String pkProperty = pk.getProperty();
                    sql.append(pkColumn).append("=#{i.").append(pkProperty).append("} ").append("and ");
                }
                sql.delete(sql.length() - 5, sql.length() - 1);
                sql.append(") then ");
                // 经测试，在Oracle数据库中，非字符串类型的数据，如果为null，无法正常的拼接到case when中，需要以下逻辑进行处理
                // 在PostgreSQL数据库时，对日期类型，数字类型，需要进行类型转换
                if (column.getJavaType().getName().contains("String")) {
                    sql.append("         #{i.").append(column.getEntityField().getName()).append("}");
                } else if (column.getJavaType().getName().equals("java.util.Date") && databaseId.equals(POSTGRESQL)) {
                    sql.append("         #{i.").append(column.getEntityField().getName()).append("}::TIMESTAMP");
                } else if ((column.getJavaType().getName().equals("java.lang.Double") || column.getJavaType().getName().equals("java.math.BigDecimal") || column.getJavaType().getName().equals("java.lang.Float")) && databaseId.equals(POSTGRESQL)) {
                    sql.append("         #{i.").append(column.getEntityField().getName()).append("}::NUMERIC");
                } else if ((column.getJavaType().getName().equals("java.lang.Integer") || column.getJavaType().getName().equals("java.lang.Long")) && databaseId.equals(POSTGRESQL)) {
                    sql.append("         #{i.").append(column.getEntityField().getName()).append("}::BIGINT");
                } else {
                    sql.append("      <if test=\"i.").append(column.getEntityField().getName()).append("!=null\">");
                    sql.append("         #{i.").append(column.getEntityField().getName()).append("}");
                    sql.append("      </if>");
                    sql.append("      <if test=\"i.").append(column.getEntityField().getName()).append("==null\">");
                    sql.append("         null");
                    sql.append("      </if>");
                }
                sql.append("    </foreach>");
                sql.append("  </trim>");
            }
        }
        sql.append("</trim>");
        sql.append("WHERE 1=0 ");
        sql.append("<foreach collection=\"list\" item=\"i\" index=\"index\" >");
        sql.append(" or (");
        for (EntityColumn pk : pks) {
            String pkColumn = pk.getColumn();
            String pkProperty = pk.getProperty();
            sql.append(pkColumn).append("=#{i.").append(pkProperty).append("} ").append(" and ");
        }
        sql.delete(sql.length() - 5, sql.length() - 1);
        sql.append(")");
        sql.append("</foreach>");
        return sql.toString();
    }
}
