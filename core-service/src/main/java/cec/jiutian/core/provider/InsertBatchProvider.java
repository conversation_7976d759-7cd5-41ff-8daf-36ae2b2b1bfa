package cec.jiutian.core.provider;

import cec.jiutian.security.base.annotation.AutoId;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.reflection.MetaObject;
import tk.mybatis.mapper.entity.EntityColumn;
import tk.mybatis.mapper.mapperhelper.EntityHelper;
import tk.mybatis.mapper.mapperhelper.MapperHelper;
import tk.mybatis.mapper.mapperhelper.MapperTemplate;
import tk.mybatis.mapper.mapperhelper.SqlHelper;
import tk.mybatis.mapper.util.MetaObjectUtil;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;

public class InsertBatchProvider extends MapperTemplate {

    public InsertBatchProvider(Class<?> mapperClass, MapperHelper mapperHelper) {
        super(mapperClass, mapperHelper);
    }

    /**
     * 批量插入 根据databaseId选择对应的批量插入策略，默认执行MySQL的批量插入
     *
     * @param ms
     */
    public String insertBatch(MappedStatement ms) {
        String databaseId = ms.getConfiguration().getDatabaseId() == null ? "mysql" : ms.getConfiguration().getDatabaseId();
        String sql;
        switch (databaseId) {
            case "oracle":
                sql = insertBatchForOracle(ms);
                break;
            case "postgresql":
                sql = insertBatchForPostgreSQL(ms);
                break;
            default:
                sql = insertBatchForMySQL(ms);
                break;
        }
        return sql;
    }

    /**
     * 批量插入 根据databaseId选择对应的批量插入策略，默认执行MySQL的批量插入
     *
     * @param ms
     */
    public String insertBatchCompositeKey(MappedStatement ms) {
        String databaseId = ms.getConfiguration().getDatabaseId() == null ? "mysql" : ms.getConfiguration().getDatabaseId();
        String sql;
        switch (databaseId) {
            case "oracle":
                sql = insertBatchForOracle(ms);
                break;
            case "postgresql":
                sql = insertBatchForPostgreSQL(ms);
                break;
            default:
                sql = insertBatchForMySQL(ms);
                break;
        }
        return sql;
    }

    // TODO: huay 2021/4/25 insert all性能不如单条insert，考虑优化拼接的SQL
    public String insertBatchForOracle(MappedStatement ms) {
        final Class<?> entityClass = getEntityClass(ms);
        //开始拼sql
        StringBuilder sql = new StringBuilder();
        sql.append("INSERT ALL\n");
        sql.append("<foreach collection=\"list\" item=\"record\">\n");
        String tableName = SqlHelper.getDynamicTableName(entityClass, tableName(entityClass), "list[0]");
        String columns = SqlHelper.insertColumns(entityClass, false, false, false);
        sql.append(" INTO ").append(tableName).append(" ").append(columns).append("\n");
        sql.append(" VALUES ");

        sql.append("<trim prefix=\"(\" suffix=\")\" suffixOverrides=\",\">");

        Set<EntityColumn> columnList = EntityHelper.getColumns(entityClass);
        //当某个列有主键策略时，不需要考虑他的属性是否为空，因为如果为空，一定会根据主键策略给他生成一个值
        for (EntityColumn column : columnList) {
            if (column.isInsertable()) {
                sql.append(column.getColumnHolder("record") + ",");
            }
        }
        sql.append("</trim>\n");

        sql.append("</foreach>\n");
        sql.append("SELECT 1 FROM DUAL");
        return sql.toString();
    }

    public String insertBatchForMySQL(MappedStatement ms) {
        final Class<?> entityClass = getEntityClass(ms);
        Set<EntityColumn> pkColumns = EntityHelper.getPKColumns(entityClass);
        setKeyPropertiesAndColumns(pkColumns, ms);
        //开始拼sql
        StringBuilder sql = new StringBuilder();
        sql.append(SqlHelper.insertIntoTable(entityClass, tableName(entityClass)));
        sql.append(SqlHelper.insertColumns(entityClass, false, false, false));
        sql.append(" VALUES ");
        sql.append("<foreach collection=\"list\" item=\"record\" separator=\",\" >");
        sql.append("<trim prefix=\"(\" suffix=\")\" suffixOverrides=\",\">");
        //获取全部列
        Set<EntityColumn> columnList = EntityHelper.getColumns(entityClass);
        //当某个列有主键策略时，不需要考虑他的属性是否为空，因为如果为空，一定会根据主键策略给他生成一个值
        for (EntityColumn column : columnList) {
            if (column.isInsertable()) {
                sql.append(column.getColumnHolder("record") + ",");
            }
        }
        sql.append("</trim>");
        sql.append("</foreach>");
        return sql.toString();
    }

    public String insertBatchForPostgreSQL(MappedStatement ms) {
        final Class<?> entityClass = getEntityClass(ms);
        Set<EntityColumn> pkColumns = EntityHelper.getPKColumns(entityClass);
        setKeyPropertiesAndColumns(pkColumns, ms);
        //开始拼sql
        StringBuilder sql = new StringBuilder();
        boolean isAutoIdTable= hashExistAnnotated(entityClass,AutoId.class);
        sql.append(SqlHelper.insertIntoTable(entityClass, tableName(entityClass)));
        sql.append(insertColumns(entityClass, false, false, false,isAutoIdTable));
        sql.append(" VALUES ");
        sql.append("<foreach collection=\"list\" item=\"record\" separator=\",\" >");
        sql.append("<trim prefix=\"(\" suffix=\")\" suffixOverrides=\",\">");
        //获取全部列
        Set<EntityColumn> columnList = EntityHelper.getColumns(entityClass);
        //当某个列有主键策略时，一定要考虑他的属性是否为空，因为如果为空，postgre一定会报错，麻烦😑
        for (EntityColumn column : columnList) {
            if (column.isInsertable() && ((!column.getColumn().equalsIgnoreCase("id"))||isAutoIdTable)) {
                sql.append(column.getColumnHolder("record")).append(",");
            }
        }
        sql.append("</trim>");
        sql.append("</foreach>");
        return sql.toString();
    }

    private boolean hashExistAnnotated(Class<?> clazz, Class annotationClass) {
        AtomicBoolean hasExists = new AtomicBoolean();
        org.springframework.util.ReflectionUtils.doWithFields(clazz, field -> {
            if (field.isAnnotationPresent(annotationClass)) {
                hasExists.set(true);
            }
        });
        return hasExists.get();
    }

    private void setKeyPropertiesAndColumns(Set<EntityColumn> pkColumns, MappedStatement ms) {
        if (pkColumns == null || pkColumns.isEmpty()) {
            return;
        }

        List<String> keyProperties = new ArrayList<>(pkColumns.size());
        List<String> keyColumns = new ArrayList<>(pkColumns.size());
        for (EntityColumn column : pkColumns) {
            keyProperties.add(column.getProperty());
            keyColumns.add(column.getColumn());
        }

        MetaObject metaObject = MetaObjectUtil.forObject(ms);
        metaObject.setValue("keyProperties", keyProperties.toArray(new String[]{}));
        metaObject.setValue("keyColumns", keyColumns.toArray(new String[]{}));
    }

    public static String insertColumns(Class<?> entityClass, boolean skipId, boolean notNull, boolean notEmpty,boolean isAutoIdTable) {
        StringBuilder sql = new StringBuilder();
        sql.append("<trim prefix=\"(\" suffix=\")\" suffixOverrides=\",\">");
        //获取全部列
        Set<EntityColumn> columnSet = EntityHelper.getColumns(entityClass);
        //当某个列有主键策略时，不需要考虑他的属性是否为空，因为如果为空，一定会根据主键策略给他生成一个值
        for (EntityColumn column : columnSet) {
            if (!column.isInsertable()) {
                continue;
            }
            if (skipId && column.isId()) {
                continue;
            }
            if (column.getColumn().equalsIgnoreCase("id")&&!isAutoIdTable) {
                continue;
            }
            if (notNull) {
                sql.append(SqlHelper.getIfNotNull(column, column.getColumn() + ",", notEmpty));
            } else {
                sql.append(column.getColumn()).append(",");
            }
        }
        sql.append("</trim>");
        return sql.toString();
    }
}
