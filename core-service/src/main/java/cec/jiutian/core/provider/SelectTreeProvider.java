package cec.jiutian.core.provider;

import cec.jiutian.core.sqlhelper.ExtendSqlHelper;
import org.apache.ibatis.mapping.MappedStatement;
import tk.mybatis.mapper.mapperhelper.MapperHelper;
import tk.mybatis.mapper.mapperhelper.SqlHelper;
import tk.mybatis.mapper.provider.base.BaseSelectProvider;

public class SelectTreeProvider extends BaseSelectProvider {

    public SelectTreeProvider(Class<?> mapperClass, MapperHelper mapperHelper) {
        super(mapperClass, mapperHelper);
    }

    public String selectTreeChild(MappedStatement ms) {
        final Class<?> entityClass = getEntityClass(ms);
        //将返回值修改为实体类型
        setResultType(ms, entityClass);
        StringBuilder sql = new StringBuilder();
        sql.append(" WITH RECURSIVE p AS (");
        sql.append(" SELECT 'p' nodeType, b.* ");
        sql.append(SqlHelper.fromTable(entityClass, tableName(entityClass)));
        sql.append(" b  ");
        sql.append(SqlHelper.whereAllIfColumns(entityClass, isNotEmpty()));
        sql.append(" UNION ALL ");
        sql.append(" SELECT 'c' nodeType, c.* ");
        sql.append(SqlHelper.fromTable(entityClass, tableName(entityClass)));
        sql.append(" c,p ");
        sql.append(" WHERE\n" +
                "    c.pid = p.id ");
        sql.append(" ) SELECT\n" +
                "  (select count(1) \n");
        sql.append(SqlHelper.fromTable(entityClass, tableName(entityClass)));
        sql.append("  d where d.pid = p.id) childCount,  \n" +
                "    p.*\n" +
                "FROM\n" +
                "    p\n" +
                "ORDER BY\n" +
                "    CRTE_TM DESC ");
        return sql.toString();
    }

    public String selectTreeRoot(MappedStatement ms) {
        final Class<?> entityClass = getEntityClass(ms);
        //将返回值修改为实体类型
        setResultType(ms, entityClass);
        StringBuilder sql = new StringBuilder();
        sql.append(" WITH RECURSIVE p AS (");
        sql.append(" SELECT 'p' nodeType, b.* ");
        sql.append(SqlHelper.fromTable(entityClass, tableName(entityClass)));
        sql.append(" b  ");
        sql.append(ExtendSqlHelper.whereAllIfColumns(entityClass, isNotEmpty()));
        sql.append(" AND b.pid is null ");
        sql.append(" UNION ALL ");
        sql.append(" SELECT 'c' nodeType, c.* ");
        sql.append(SqlHelper.fromTable(entityClass, tableName(entityClass)));
        sql.append(" c,p ");
        sql.append(" WHERE\n" +
                "    c.pid = p.id ");
        sql.append(" ) SELECT\n" +
                "  (select count(1) \n");
        sql.append(SqlHelper.fromTable(entityClass, tableName(entityClass)));
        sql.append("  d where d.pid = p.id) childCount,  \n" +
                "    p.*\n" +
                "FROM\n" +
                "    p\n" +
                "ORDER BY\n" +
                "    CRTE_TM DESC ");
        return sql.toString();
    }

    public String selectTreeParent(MappedStatement ms) {
        final Class<?> entityClass = getEntityClass(ms);
        //将返回值修改为实体类型
        setResultType(ms, entityClass);
        StringBuilder sql = new StringBuilder();
        sql.append(" WITH RECURSIVE p AS (");
        sql.append(" SELECT 'c' nodeType, b.* ");
        sql.append(SqlHelper.fromTable(entityClass, tableName(entityClass)));
        sql.append(" b  ");
        sql.append(ExtendSqlHelper.whereAllIfColumnsLike(entityClass, isNotEmpty()));
        sql.append(" UNION ALL ");
        sql.append(" SELECT 'p' nodeType, c.* ");
        sql.append(SqlHelper.fromTable(entityClass, tableName(entityClass)));
        sql.append(" c,p ");
        sql.append(" WHERE\n" +
                "    c.id = p.pid ");
        sql.append(" ) SELECT distinct \n" +
                "  (select count(1) \n");
        sql.append(SqlHelper.fromTable(entityClass, tableName(entityClass)));
        sql.append("  d where d.pid = p.id) childCount,  \n" +
                "    p.*\n" +
                "FROM\n" +
                "    p\n" +
                "ORDER BY\n" +
                "    CRTE_TM DESC ");
        return sql.toString();
    }
}
