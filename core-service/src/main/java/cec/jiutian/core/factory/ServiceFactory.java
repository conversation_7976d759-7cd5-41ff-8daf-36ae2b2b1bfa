package cec.jiutian.core.factory;

import cec.jiutian.core.util.FactoryNameUtil;
import cec.jiutian.core.util.FileUploadUtils;
import cec.jiutian.core.util.ModeConvertUtils;
import cec.jiutian.core.util.NameCodeUtil;
import cec.jiutian.core.util.PrintRecordUtils;
import cec.jiutian.core.util.SpringContextUtils;
import cec.jiutian.core.util.TokenUtils;

/**
 * 基础的工厂类，使用静态方法预先定义好的bean返回方法，可以在各个服务中直接使用
 *
 * <AUTHOR>
 * @date 2021/4/15
 */
public class ServiceFactory {
    public static FileUploadUtils getUploadUtils() {
        return SpringContextUtils.getBean(FileUploadUtils.class);
    }
    public static NameCodeUtil getNameCodeUtil() {
        return SpringContextUtils.getBean(NameCodeUtil.class);
    }

    public static ModeConvertUtils getModeUtils() {
        return SpringContextUtils.getBean(ModeConvertUtils.class);
    }

    public static FactoryNameUtil getFactoryNameUtil() {
        return SpringContextUtils.getBean(FactoryNameUtil.class);
    }

    public static TokenUtils getTokenUtils() {
        return SpringContextUtils.getBean(TokenUtils.class);
    }


    public static PrintRecordUtils getPrintRecordUtils() {
        return SpringContextUtils.getBean(PrintRecordUtils.class);
    }

}
