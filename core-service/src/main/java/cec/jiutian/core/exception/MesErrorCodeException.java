package cec.jiutian.core.exception;

import cec.jiutian.core.result.ErrorCodeResult;

import java.io.PrintStream;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.List;

public class MesErrorCodeException extends RuntimeException {

    private static final long serialVersionUID = -772460477538768284L;

    private String errorCode;
    private List<String> msgList;
    private List<String> actionList;
    private ErrorCodeResult errorCodeResult;
    private String message;

    public MesErrorCodeException(String errorCode) {
        this.errorCode = errorCode;
    }

    public MesErrorCodeException(ErrorCodeResult errorCodeResult) {
        this.errorCodeResult = errorCodeResult;
    }

    public MesErrorCodeException(Exception exception, String errorCode) {
        super(exception.getMessage(), exception.getCause());
        this.errorCode = errorCode;
    }

    public MesErrorCodeException addMsgList(List<String> msgs) {
        if (null == msgList) {
            msgList = new ArrayList<String>();
        }
        this.msgList.addAll(msgs);
        message = String.join(",", msgList);
        return this;
    }

    public MesErrorCodeException addMsgItem(String item) {
        if (null == msgList) {
            msgList = new ArrayList<String>();
        }
        msgList.add(item);
        message = item;
        return this;
    }

    public MesErrorCodeException addActionList(List<String> actions) {
        if (null == actionList) {
            actionList = new ArrayList<String>();
        }
        this.actionList.addAll(actions);
        return this;
    }


    public MesErrorCodeException addActionItem(String item) {
        if (null == actionList) {
            actionList = new ArrayList<String>();
        }
        actionList.add(item);
        return this;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public List<String> getMsgList() {
        return msgList;
    }

    public void setMsgList(List<String> msgList) {
        this.msgList = msgList;
    }

    public List<String> getActionList() {
        return actionList;
    }

    public void setActionList(List<String> actionList) {
        this.actionList = actionList;
    }

    public ErrorCodeResult getErrorCodeResult() {
        return errorCodeResult;
    }

    public void setErrorCodeResult(ErrorCodeResult errorCodeResult) {
        this.errorCodeResult = errorCodeResult;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    @Override
    public void printStackTrace() {
        this.printStackTrace(System.err);
    }

    @Override
    public void printStackTrace(PrintStream s) {
        synchronized (s) {
            this.printStackTrace(new PrintWriter(s));
        }
    }

    @Override
    public void printStackTrace(PrintWriter w) {
        synchronized (w) {
            w.println(this);
            StackTraceElement[] trace = getStackTrace();
            for (int i = 0; i < trace.length; i++)
                w.println("\tat " + trace[i]);

            Throwable ourCause = getCause();
            if (ourCause != null) {
                ourCause.printStackTrace(w);
            }
            w.flush();
        }
    }
}
