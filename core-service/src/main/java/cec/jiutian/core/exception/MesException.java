package cec.jiutian.core.exception;

import cec.jiutian.core.base.BaseResponse;
import cec.jiutian.core.errorCode.CoreErrorCode;
import cec.jiutian.core.result.ErrorCodeResult;
import cec.jiutian.core.service.CoreErrorCodeService;
import cec.jiutian.core.util.BaseEntityUtils;
import cec.jiutian.core.util.FillerUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.ConstraintViolationException;
import javax.ws.rs.core.Response;
import java.util.Enumeration;

@ControllerAdvice("cec.jiutian")
@Slf4j
public class MesException {

    private final static String EN_LANG = "en";
    private final static String CN_LANG = "cn";
    @Value("${base.detail.rowCount:100}")
    private Integer detailRowCount;
    @Autowired
    private CoreErrorCodeService coreErrorCodeService;

    /**
     * <p>异常处理</p>
     *
     * @param exception
     * @return
     * <AUTHOR>
    @ExceptionHandler(MesErrorCodeException.class)
    @ResponseBody
    public Response exceptionHandler(MesErrorCodeException exception, HttpServletRequest req, HttpServletResponse res) {
        log.error("{}", exception.getMessage(), exception);
        BaseEntityUtils.clearThreadVariable();
        res.setStatus(Response.Status.SERVICE_UNAVAILABLE.getStatusCode());
        if (exception.getErrorCodeResult() != null) {
            return generateBaseResponse(res.getStatus(), exception.getErrorCodeResult());
        }
        ErrorCodeResult result = new ErrorCodeResult();
        this.setDetail(result, exception);
        //这种方式只设置detail
//        result.setPath(req.getContextPath());
        //获取语言信息
        String langHeader = req.getHeader("lang");
        Enumeration<String> langValues = req.getHeaders("lang");
        if (null == langHeader || null == langValues || !langValues.hasMoreElements()) {
            result.setCode("111");
            result.setMessage("没有选中语言！");
            FillerUtil.fillErrorCodeResult(result);
            return generateBaseResponse(res.getStatus(), result);
        }

        String errorCode = exception.getErrorCode();
        CoreErrorCode coreErrorCode = coreErrorCodeService.getCoreErrorCodeByCode(errorCode);

        if (null == coreErrorCode) {
            result.setCode("111");
            result.setMessage("MES ErrorCode 异常: " + errorCode);
            FillerUtil.fillErrorCodeResult(result);
            return generateBaseResponse(res.getStatus(), result);

        }
        //填充基础数据
        result.setCode(exception.getErrorCode());
        FillerUtil.fillErrorCodeResult(result, req);
        //获取语言
        String lang = langValues.nextElement();
        if (StringUtils.equalsIgnoreCase(lang, CN_LANG)) {
            result.setMessage(FillerUtil.fill(coreErrorCode.getErrorDescription(), exception.getMsgList()));
            result.setAction(FillerUtil.fill(coreErrorCode.getActionText(), exception.getActionList()));
        } else if (StringUtils.equalsIgnoreCase(lang, EN_LANG)) {
            result.setMessage(FillerUtil.fill(coreErrorCode.getErrorDescription(), exception.getMsgList()));
            result.setAction(FillerUtil.fill(coreErrorCode.getActionText(), exception.getActionList()));
        } else {
            result.setCode("111");
            result.setMessage("语言设置错误！");
            FillerUtil.fillErrorCodeResult(result);
            return generateBaseResponse(res.getStatus(), result);
        }
        exception.addMsgItem(result.getDetail());
//        this.setDetail(result, exception);
        exception.printStackTrace();
        log.error("{}, {}, {}", exception.getErrorCode(), result.getMessage(), result.getDetail());
        //语言设置成功，设置其余选项
        FillerUtil.fillErrorCodeResult(result);
        return generateBaseResponse(res.getStatus(), result);
    }

    /**
     * <p>异常处理</p>
     *
     * @param exception
     * @return
     * <AUTHOR>
     */
    @ExceptionHandler(RuntimeException.class)
    @ResponseBody
    public Response exceptionHandler(RuntimeException exception, HttpServletResponse res) {
        log.error("{}", exception.getMessage(), exception);
        BaseEntityUtils.clearThreadVariable();
        res.setStatus(Response.Status.SERVICE_UNAVAILABLE.getStatusCode());
        exception.printStackTrace();
        ErrorCodeResult result = this.getErrorCodeResult(exception);
//		result.setCode("111");
//		result.setMsg("Unkown Exception Exists! Check It!");
//		FillerUtil.fillErrorCodeResult(result);
        return generateBaseResponse(res.getStatus(), result);
    }

    /**
     * <p>异常处理</p>
     *
     * @param exception
     * @return
     * <AUTHOR>
     */
    @ExceptionHandler(Exception.class)
    @ResponseBody
    public Response exceptionHandler(Exception exception, HttpServletResponse res) {
        log.error("{}", exception.getMessage(), exception);
        BaseEntityUtils.clearThreadVariable();
        res.setStatus(Response.Status.SERVICE_UNAVAILABLE.getStatusCode());
        exception.printStackTrace();
        ErrorCodeResult result = this.getErrorCodeResult(exception);
        return generateBaseResponse(res.getStatus(), result);
    }

    /**
     * <p>异常处理</p>
     *
     * @param exception
     * @return
     * <AUTHOR>
     */
    @ExceptionHandler(ConstraintViolationException.class)
    @ResponseBody
    public Response exceptionHandler(ConstraintViolationException exception, HttpServletResponse res) {
        log.error("{}", exception.getMessage(), exception);
        BaseEntityUtils.clearThreadVariable();
        res.setStatus(Response.Status.SERVICE_UNAVAILABLE.getStatusCode());
        exception.printStackTrace();
        ErrorCodeResult result = this.getErrorCodeResult(exception);
        return generateBaseResponse(res.getStatus(), result);
    }

    /**
     * 获取ErrorCodeResult，将Exception中的消息放到detail中，将Message放到msg中
     *
     * @param exception
     * @return
     */
    private ErrorCodeResult getErrorCodeResult(Exception exception) {
        ErrorCodeResult result = new ErrorCodeResult();
        result.setCode("111");
        result.setMessage(exception.getMessage());
        this.setDetail(result, exception);
        FillerUtil.fillErrorCodeResult(result);
        return result;
    }

    /**
     * 打印数据到detail字段中，仅打印固定行数的数据
     *
     * @param result
     * @param exception
     */
    private void setDetail(ErrorCodeResult result, Exception exception) {
        StringBuilder sb = new StringBuilder(exception + "\n");
        StackTraceElement[] stackTraceElements = exception.getStackTrace();
        for (int index = 0; index < detailRowCount && index < stackTraceElements.length; index++) {
            StackTraceElement stackTraceElement = stackTraceElements[index];
            sb.append("\tat " + stackTraceElement + "\n");
        }
        result.setDetail(sb.toString());
        log.error("{}", exception.getMessage(), exception);
    }

    private BaseResponse generateBaseResponse(int status, ErrorCodeResult result){
        BaseResponse baseResponse = new BaseResponse();
        baseResponse.setStatus(status);
        baseResponse.setEntity(result);
        return baseResponse;
    }
}
