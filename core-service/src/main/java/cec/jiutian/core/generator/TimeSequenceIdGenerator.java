package cec.jiutian.core.generator;

import cec.jiutian.security.base.po.TableIdConfigPO;
import java.lang.reflect.Field;

/**
 * 对序列速度要求生成
 */
public class TimeSequenceIdGenerator extends IdGenerator {

    //表Id
    private long tableId;
    //机器ID  2进制5位  32位减掉1位 31个
    private long workerId;
    //代表一毫秒内生成的多个id的最新序号  12位 4096 -1 = 4095 个
    private long sequence;
    //设置一个时间初始值    2^41 - 1   差不多可以用69年
    private long twepoch = 1585644268888L;
    //每毫秒内产生的id数 2 的 12次方
    private long sequenceBits = 12L;
    private long timestampLeftShift = sequenceBits;
    // 时间戳需要向左移的位数
    private long sequenceMask = -1L ^ (-1L << sequenceBits);
    //记录产生时间毫秒数，判断是否是同1毫秒
    private long lastTimestamp = -1L;

    public TimeSequenceIdGenerator(Field field, TableIdConfigPO configPO) {
        super(field,configPO);
        this.tableId = configPO.getIdentifier();
        this.workerId = configPO.getServiceId();
    }

    /**
     * 1、插入主键
     */
    @Override
    void handle(Field field, Object object) throws Throwable {
        field.set(object, occupied(tableId,3)+"."+occupied(workerId,3)+"."+computeSequence(nextId(),maxNum,maxUnit));
    }

    /**
     * 该方法基于雪花算法去掉机器和机房位只保留时间和序列位
     * @return
     */
    public synchronized long nextId() {
        // 这儿就是获取当前时间戳，单位是毫秒
        long timestamp = timeGen();
        if (timestamp < lastTimestamp) {
            throw new RuntimeException(
                    String.format("Clock moved backwards. Refusing to generate id for %d milliseconds",
                            lastTimestamp - timestamp));
        }

        // 下面是说假设在同一个毫秒内，又发送了一个请求生成一个id
        if (lastTimestamp == timestamp) {
            sequence = (sequence + 1) & sequenceMask;
            //当某一毫秒的时间，产生的id数 超过4095，系统会进入等待，直到下一毫秒，系统继续产生ID
            if (sequence == 0) {
                timestamp = tilNextMillis(lastTimestamp);
            }
        } else {
            sequence = 0;
        }
        // 这儿记录一下最近一次生成id的时间戳，单位是毫秒
        lastTimestamp = timestamp;
        return ((timestamp - twepoch) << timestampLeftShift) | sequence;
    }

    /**
     * 获取当前时间戳
     * @return
     */
    private long timeGen() {
        return System.currentTimeMillis();
    }

    /**
     * 当某一毫秒的时间，产生的id数 超过4095，系统会进入等待，直到下一毫秒，系统继续产生ID
     *
     * @param lastTimestamp
     * @return
     */
    private long tilNextMillis(long lastTimestamp) {
        long timestamp = timeGen();
        while (timestamp <= lastTimestamp) {
            timestamp = timeGen();
        }
        return timestamp;
    }
}
