package cec.jiutian.core.generator;

import cec.jiutian.security.base.po.TableIdConfigPO;
import cec.jiutian.core.service.TableIdConfigService;

import java.lang.reflect.Field;

/**
 * 对序列速度要求生成
 */
public class SequenceIdGenerator extends IdGenerator {

    public SequenceIdGenerator(Field field, TableIdConfigPO configPO, TableIdConfigService tableIdConfigService) {
        super(field,configPO,tableIdConfigService);
    }

    /**
     * 1、插入主键
     */
    @Override
    void handle(Field field, Object object) throws Throwable {
        Long sequence =tableIdConfigService.getSequence(configPO.getSequenceName());
        field.set(object, occupied(configPO.getIdentifier(),3)+"."+occupied(configPO.getServiceId(),3)+"."+computeSequence(sequence,super.maxNum,super.maxUnit));
    }
}
