package cec.jiutian.core.generator;

import cec.jiutian.security.base.po.TableIdConfigPO;
import cec.jiutian.core.service.TableIdConfigService;

import java.lang.reflect.Field;

/**
 * 一般小表id生成
 */
public class GeneralIdGenerator extends IdGenerator {

   public GeneralIdGenerator(Field field,TableIdConfigPO configPO, TableIdConfigService tableIdConfigService) {
       super(field,configPO,tableIdConfigService);
    }

    @Override
    void handle(Field field, Object object) throws Throwable {
        Long sequence = tableIdConfigService.getSimulateSequence(configPO.getIdentifier());
        field.set(object,occupied(configPO.getIdentifier(),3) +"."+occupied(configPO.getServiceId(),3)+"."+computeSequence(sequence,super.maxNum,super.maxUnit));
    }
}
