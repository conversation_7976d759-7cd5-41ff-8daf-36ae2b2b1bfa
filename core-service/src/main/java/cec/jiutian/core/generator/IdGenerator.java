package cec.jiutian.core.generator;

import cec.jiutian.security.base.po.TableIdConfigPO;
import cec.jiutian.core.service.TableIdConfigService;

import java.lang.reflect.Field;

public abstract class IdGenerator {

    TableIdConfigPO configPO;
    /**
     * 字段
     */
    Field field;

    static String str = "0123456789abcdefghijklmnopqrstuvwsyz";

    /**
     * 最大数
     */
    long maxNum=1000000L;

    /**
     * 最大位数
     */
    int maxUnit=6;

    TableIdConfigService tableIdConfigService;

    IdGenerator(Field field, TableIdConfigPO configPO, TableIdConfigService tableIdConfigService) {
        this.field = field;
        this.configPO = configPO;
        this.tableIdConfigService = tableIdConfigService;
    }

    IdGenerator(Field field, TableIdConfigPO configPO) {
        this.field = field;
        this.configPO = configPO;
    }

    abstract void handle(Field field, Object object) throws Throwable;

    private boolean checkField(Object object, Field field) throws IllegalAccessException {
        if (!field.isAccessible()) {
            field.setAccessible(true);
        }
        //如果该注解对应的属性已经被赋值，那么就不用通过生成器ID
        return field.get(object) == null;
    }

    public void accept(Object o) throws Throwable {
        if (checkField(o, field)) {
            handle(field, o);
        }
    }

    /**
     * 计算序列
     * 13546464/1000000.13546464%1000000
     * 0000AC.546464
     */
    public String computeSequence(Long sequence,long maxNum,int digits){
        if(sequence==null||sequence==0){
            return "000000.000000";
        }
        long upperNumber = sequence/maxNum;
        Long downNumber = sequence%maxNum;
        return occupied(HexToStr(upperNumber),digits)+"."+occupied(downNumber,digits);
    }

    /**
     * 占位,不够位数的用0占位
     * @param num
     * @param length
     * @return
     */
    public static String occupied(Object num,int length){
        if(num==null){
            return null;
        }
        String numStr=num.toString();
        StringBuilder sb=new StringBuilder();
        if(numStr.length()<length){
            for (int i = 0; i < length-numStr.length(); i++) {
                sb.append("0");
            }
        }
        sb.append(numStr);
        return sb.toString();
    }

    /**
     * 转为36进制
     * @param i
     * @return
     */
    public static String HexToStr(long i){
        if(i==0){
            return "0";
        }
        StringBuffer sb = new StringBuffer();
        for (int j = 0; i >= 36; j++) {
            long a = i%36;
            i/=36;
            sb.append(str.charAt((int) a));
        }
        sb.append(str.charAt((int) i));
        return sb.reverse().toString();
    }

}
