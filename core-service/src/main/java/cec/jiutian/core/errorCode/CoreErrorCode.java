package cec.jiutian.core.errorCode;

import cec.jiutian.security.base.annotation.UniqueColumn;

import javax.persistence.Column;
import java.io.Serializable;

public class CoreErrorCode implements Serializable {

    private static final long serialVersionUID = -6587451109452949801L;
    private Long errorCodeIdentifier;
    /*
     * 错误码编号
     */
    @UniqueColumn
    @Column(name = "ERR_CD")
    private String errorCode;

    /*
     * 错误码中文消息标题模板
     */
    @Column(name = "ERR_NM")
    private String errorName;

    /*
     * 错误码中文消息详情模板
     */
    @Column(name = "ERR_DS")
    private String errorDescription;


    /**
     * 所属系统代码
     */
    @Column(name = "SYSTM_CD")
    private String systemCode;

    /**
     * 操作码
     */
    @Column(name = "OPRTN_CD")
    private String operationCode;

    /*
     * 行为
     */
    @Column(name = "ACTN_TX")
    private String actionText;

    public void setErrorCodeIdentifier(Long errorCodeIdentifier) {
        this.errorCodeIdentifier = errorCodeIdentifier;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorName() {
        return errorName;
    }

    public void setErrorName(String errorName) {
        this.errorName = errorName;
    }

    public String getErrorDescription() {
        return errorDescription;
    }

    public void setErrorDescription(String errorDescription) {
        this.errorDescription = errorDescription;
    }

    public String getSystemCode() {
        return systemCode;
    }

    public void setSystemCode(String systemCode) {
        this.systemCode = systemCode;
    }

    public String getOperationCode() {
        return operationCode;
    }

    public void setOperationCode(String operationCode) {
        this.operationCode = operationCode;
    }

    public String getActionText() {
        return actionText;
    }

    public void setActionText(String actionText) {
        this.actionText = actionText;
    }

    @Override
    public String toString() {
        return "CoreErrorCode{" +
                "errorCodeIdentifier=" + errorCodeIdentifier +
                ", errorCode='" + errorCode + '\'' +
                ", errorName='" + errorName + '\'' +
                ", errorDescription='" + errorDescription + '\'' +
                ", systemCode='" + systemCode + '\'' +
                ", operationCode='" + operationCode + '\'' +
                ", actionText='" + actionText + '\'' +
                '}';
    }
}
