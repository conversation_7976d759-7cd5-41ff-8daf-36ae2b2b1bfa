package cec.jiutian.core.result;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
@Data
public class ErrorCodeResult implements Serializable {

    private static final long serialVersionUID = -2051487665881277026L;

    /**
     * 默认值为"000"，成功状态
     */
    private String code = "000";

    /**
     * 中英message
     */
    private String message;

    /**
     * 错误明细
     */
    private String detail;

    /**
     * 行为
     */
    private String action;

    /**
     * 输入数据
     */
    /*    private Object input;*/

    /**
     * 输出数据
     */
    /*private Object output;*/

    /**
     * 最后操作人员
     */
    /*    private String lastEventUser;*/

    /**
     * 最后操作备注
     */
    /*    private String lastEventComment;*/

    /**
     * 操作路径
     */
//    private String path;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }
}
