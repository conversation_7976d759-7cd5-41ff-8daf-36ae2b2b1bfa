package cec.jiutian.core.feign;

import cec.jiutian.core.config.ServiceNames;
import cec.jiutian.core.feign.fallback.PrintRecordFallBack;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @author: bait
 * @create: 2022-07-14 16:55
 * @description: 打印记录创建 FEGIN接口调用
 **/
@FeignClient(value = ServiceNames.FABOS_FEATURE, contextId = "PrintRecordFeign", fallback = PrintRecordFallBack.class)
public interface PrintRecordServiceFeign {


    @PostMapping("/remote/print/record/create")
    String createPrintRecord(@RequestParam("printRecordCreateStr") String printRecordCreateStr);



}
