package cec.jiutian.core.feign;

import cec.jiutian.core.config.ServiceNames;
import cec.jiutian.core.feign.fallback.ModeServiceFallBack;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @date 2021/4/15
 */
@FeignClient(value = ServiceNames.FABOS_FEATURE, contextId = "ModeServiceFeign", fallback = ModeServiceFallBack.class)
public interface ModeServiceFeign {
    @PostMapping("/remote/getInitialMode")
    String getInitialMode(@RequestParam String modeModelCode);

    @PostMapping("/remote/getNextModeByCondition")
    String getNextModeByCondition(@RequestParam String modeModelCode,
                                  @RequestParam String originalMode,
                                  @RequestParam(required = false) String conditionFieldName,
                                  @RequestParam(required = false) String conditionValueText);
}
