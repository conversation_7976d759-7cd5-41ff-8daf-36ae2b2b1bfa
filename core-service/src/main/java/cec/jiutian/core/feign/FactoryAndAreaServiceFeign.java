package cec.jiutian.core.feign;

import cec.jiutian.core.config.ServiceNames;
import cec.jiutian.core.feign.fallback.FactoryAndAreaFallBack;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;
import java.util.Map;

/**
 * 工厂逻辑调用类，实际为coms服务的FeignClient
 */
@FeignClient(value = ServiceNames.FABOS_COMS, contextId = "FactoryAndAreaServiceFeign", fallback = FactoryAndAreaFallBack.class)
public interface FactoryAndAreaServiceFeign {
    /**
     * @return 厂别
     */
    @PostMapping("/remote/getFactory")
    List<String> getFactory();

    /**
     * @return 区域
     */
    @PostMapping("/remote/getAreaByFactory")
    Map<String, List<String>> getAreaByFactory();
}
