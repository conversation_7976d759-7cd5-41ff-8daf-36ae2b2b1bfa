package cec.jiutian.core.feign.fallback;

import cec.jiutian.core.comn.errorCode.BaseErrorCode;
import cec.jiutian.core.exception.MesErrorCodeException;
import cec.jiutian.core.feign.FactoryAndAreaServiceFeign;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class FactoryAndAreaFallBack implements FactoryAndAreaServiceFeign {
    @Override
    public List<String> getFactory() {
        MesErrorCodeException exception = new MesErrorCodeException(BaseErrorCode.FEIGN_GET_NAME_FAIL.getCode());
        log.error("{}", exception.getMessage(), exception);
        throw exception;
    }

    @Override
    public Map<String, List<String>> getAreaByFactory() {
        MesErrorCodeException exception = new MesErrorCodeException(BaseErrorCode.FEIGN_GET_NAME_FAIL.getCode());
        log.error("{}", exception.getMessage(), exception);
        throw exception;
    }
}
