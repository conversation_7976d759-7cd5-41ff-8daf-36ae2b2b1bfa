package cec.jiutian.core.feign.fallback;

import cec.jiutian.core.comn.errorCode.BaseErrorCode;
import cec.jiutian.core.exception.MesErrorCodeException;
import cec.jiutian.core.feign.PrintRecordServiceFeign;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @author: bait
 * @create: 2022-07-14 16:57
 * @description:
 **/
@Slf4j
@Component
public class PrintRecordFallBack implements PrintRecordServiceFeign {
    @Override
    public String createPrintRecord(String printRecordCreateStr) {
        MesErrorCodeException exception = new MesErrorCodeException(BaseErrorCode.FEIGN_ERROR.getCode());
        log.error("{}", exception.getMessage(), exception);
        throw exception;
    }
}
