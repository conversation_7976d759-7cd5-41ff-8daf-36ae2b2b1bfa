package cec.jiutian.core.feign.fallback;

import cec.jiutian.security.base.entity.FeignString;
import cec.jiutian.core.comn.errorCode.BaseErrorCode;
import cec.jiutian.core.entity.FileOperation;
import cec.jiutian.core.exception.MesErrorCodeException;
import cec.jiutian.core.feign.FileUploadServiceFeign;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @date 2021/4/15
 */
@Slf4j
@Component
public class FileUploadFallBack implements FileUploadServiceFeign {
    @Override
    public FeignString fileUpload(MultipartFile file, String filePath, String sourceCode, String lastEventUser, String functionFolder) {
        MesErrorCodeException exception = new MesErrorCodeException(BaseErrorCode.FILE_UPLOAD_ERROR.getCode());
        log.error("{}", exception.getMessage(), exception);
        throw exception;
    }

    @Override
    public FeignString multiFileUploadAndSaveData(MultipartFile[] files, String filePath, String sourceCode, String lastEventUser, String functionFolder) {
        MesErrorCodeException exception = new MesErrorCodeException(BaseErrorCode.FILE_UPLOAD_ERROR.getCode());
        log.error("{}", exception.getMessage(), exception);
        throw exception;
    }

    @Override
    public void changeFilename(FileOperation fileOperation) {
        MesErrorCodeException exception = new MesErrorCodeException(BaseErrorCode.FEIGN_ERROR.getCode());
        log.error("{}", exception.getMessage(), exception);
        throw exception;
    }

    @Override
    public void deleteFileById(String uuid) {
        MesErrorCodeException exception = new MesErrorCodeException(BaseErrorCode.FILE_DELETE_ERROR.getCode());
        log.error("{}", exception.getMessage(), exception);
        throw exception;
    }

    @Override
    public void deleteBatchFile(String uuids) {
        MesErrorCodeException exception = new MesErrorCodeException(BaseErrorCode.FILE_DELETE_ERROR.getCode());
        log.error("{}", exception.getMessage(), exception);
        throw exception;
    }

    @Override
    public String multiFileUploadAndSaveDataForFront(MultipartFile[] files, String filePath, String functionFolder, String fileOperation) {
        MesErrorCodeException exception = new MesErrorCodeException(BaseErrorCode.FILE_UPLOAD_ERROR.getCode());
        log.error("{}", exception.getMessage(), exception);
        throw exception;
    }
}
