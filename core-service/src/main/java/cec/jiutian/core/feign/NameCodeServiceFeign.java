package cec.jiutian.core.feign;

import cec.jiutian.core.config.ServiceNames;
import cec.jiutian.core.feign.fallback.NameCodeFallBack;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * 命名规则主键生成方法类，实际为coms服务的FeignClient，原有的NameCodeUtil调用此处的逻辑，但外部接口保持不变
 *
 * <AUTHOR>
 * @date 2021/2/3
 */
@FeignClient(value = ServiceNames.FABOS_FEATURE, contextId = "NameCodeServiceFeign", fallback = NameCodeFallBack.class)
public interface NameCodeServiceFeign {
    /**
     * @param ruleCode    命名规则的名称
     * @param num         此次获取主键的数量
     * @param variableMap 如果命名规则参数中定义了变量，则此处需要传递变量的key和value
     * @return 指定数量的主键
     */
    @PostMapping("/remote/getNameCode")
    List<String> getNameCode(@RequestParam String ruleCode, @RequestParam int num, @RequestParam(required = false) Map<String, String> variableMap);

    @PostMapping("/remote/getLabelNameCode")
    List<String> getLabelNameCode(@RequestParam String ruleCode, @RequestParam int num, @RequestParam(required = false) Map<String, String> variableMap);
}
