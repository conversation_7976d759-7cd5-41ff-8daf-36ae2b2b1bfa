package cec.jiutian.core.feign;

import cec.jiutian.core.config.ServiceNames;
import cec.jiutian.core.entity.FileOperation;
import cec.jiutian.core.feign.fallback.FileUploadFallBack;
import cec.jiutian.security.base.entity.FeignString;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @date 2021/4/15
 */
@FeignClient(value = ServiceNames.FABOS_BASE_FILE, contextId = "FileUploadServiceFeign", fallback = FileUploadFallBack.class)
public interface FileUploadServiceFeign {
    @PostMapping(value = "/remote/fileUploadAndSaveData", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    FeignString fileUpload(@RequestPart MultipartFile file, @RequestParam String filePath,
                           @RequestParam(required = false) String sourceCode, @RequestParam String lastEventUser,
                           @RequestParam(required = false) String functionFolder);

    @PostMapping(value = "/remote/multiFileUploadAndSaveData", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    FeignString multiFileUploadAndSaveData(@RequestPart MultipartFile[] files, @RequestParam String filePath,
                                           @RequestParam(required = false) String sourceCode, @RequestParam String lastEventUser,
                                           @RequestParam(required = false) String functionFolder);

    @PostMapping("/remote/changeFilename")
    void changeFilename(@RequestBody FileOperation fileOperation);

    @PostMapping("/remote/deleteFileById")
    void deleteFileById(@RequestParam String uuid);

    @PostMapping("/remote/deleteBatchFile")
    void deleteBatchFile(@RequestParam String uuids);

    /**
     * 大文件上传可能出现超时现象
     */
    @PostMapping(value = "/remote/multiFileUploadAndSaveDataForFront", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    String multiFileUploadAndSaveDataForFront(@RequestPart MultipartFile[] files, @RequestParam String filePath,
                                              @RequestParam String functionFolder, @RequestParam String fileOperation);
}
