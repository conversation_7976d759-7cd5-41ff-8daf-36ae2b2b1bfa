package cec.jiutian.core.feign.fallback;

import cec.jiutian.core.comn.errorCode.BaseErrorCode;
import cec.jiutian.core.exception.MesErrorCodeException;
import cec.jiutian.core.feign.NameCodeServiceFeign;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/2/3
 */
@Slf4j
@Component
public class NameCodeFallBack implements NameCodeServiceFeign {
    @Override
    public List<String> getNameCode(String ruleCode, int num, Map<String, String> variableMap) {
        MesErrorCodeException exception = new MesErrorCodeException(BaseErrorCode.FEIGN_GET_NAME_FAIL.getCode());
        log.error("{}", exception.getMessage(), exception);
        throw exception;
    }

    @Override
    public List<String> getLabelNameCode(String ruleCode, int num, Map<String, String> variableMap) {
        MesErrorCodeException exception = new MesErrorCodeException(BaseErrorCode.FEIGN_GET_NAME_FAIL.getCode());
        log.error("{}", exception.getMessage(), exception);
        throw exception;
    }
}
