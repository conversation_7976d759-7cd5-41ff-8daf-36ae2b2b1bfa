package cec.jiutian.core.feign.fallback;

import cec.jiutian.core.comn.errorCode.BaseErrorCode;
import cec.jiutian.core.exception.MesErrorCodeException;
import cec.jiutian.core.feign.ModeServiceFeign;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2021/4/15
 */
@Component
@Slf4j
public class ModeServiceFallBack implements ModeServiceFeign {
    @Override
    public String getInitialMode(String modeModelCode) {
        MesErrorCodeException exception = new MesErrorCodeException(BaseErrorCode.FEIGN_ERROR.getCode());
        log.error("{}", exception.getMessage(), exception);
        throw exception;
    }

    @Override
    public String getNextModeByCondition(String modeModelCode, String originalMode, String conditionFieldName, String conditionValueText) {
        MesErrorCodeException exception = new MesErrorCodeException(BaseErrorCode.FEIGN_ERROR.getCode());
        log.error("{}", exception.getMessage(), exception);
        throw exception;
    }
}
