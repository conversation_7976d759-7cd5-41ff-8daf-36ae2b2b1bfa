package cec.jiutian.core.message;

import cec.jiutian.security.base.dto.HistoryMessageSendDTO;
import cec.jiutian.security.base.dto.HistoryResultDTO;
import cec.jiutian.core.comn.message.HistoryMessageClient;
import cec.jiutian.core.comn.util.JsonUtils;
import cec.jiutian.core.util.BaseRedisCacheUtil;
import cec.jiutian.core.util.BizParamUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/10
 */
@Component
@Slf4j
//@EnableBinding(HistoryMessageClient.class)
public class HistoryDataSender {
    private final HistoryMessageClient historyMessageClient;

    public HistoryDataSender(HistoryMessageClient historyMessageClient) {
        this.historyMessageClient = historyMessageClient;
    }

    @SuppressWarnings("unchecked")
    public void send(String key) {
        List<HistoryResultDTO> historyResultDTOList = (List<HistoryResultDTO>) BaseRedisCacheUtil.get(key);
        if (historyResultDTOList != null) {
            HistoryMessageSendDTO historyMessageSendDTO = new HistoryMessageSendDTO();
            historyMessageSendDTO.setHistoryResultDTOList(historyResultDTOList);
            historyMessageSendDTO.setOperationUUID(key);
            historyMessageSendDTO.setOperationTime(BizParamUtils.getLastEventTime());
            historyMessageSendDTO.setOperationUserName(BizParamUtils.getLastEventUser());
            historyMessageSendDTO.setOperationEventName(BizParamUtils.getOperationName());
            String message = JsonUtils.toJson(historyMessageSendDTO);
            if (message != null) {
                try {
//                    historyMessageClient.output().send(MessageBuilder.withPayload(message).build());
                    // 当send操作成功时，再删除缓存数据
                    BaseRedisCacheUtil.delete(key);
                } catch (Exception e) {
                    log.error(e.getMessage());
                }
            }
        }
    }
}
