package cec.jiutian.core.transaction.aspect;

import cec.jiutian.core.transaction.hook.HistoryDataTransactionHook;
import cec.jiutian.core.message.HistoryDataSender;
import cec.jiutian.core.util.BaseRedisCacheUtil;
import cec.jiutian.core.util.SpringContextUtils;
import cec.jiutian.core.util.TraceUtils;
import io.seata.core.context.RootContext;
import io.seata.tm.api.transaction.TransactionHookManager;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

@Aspect
@Component
@Slf4j
//@EnableBinding(HistoryMessageClient.class)
public class HistoryDataTransactionAspect {

    @Pointcut("execution(* cec.jiutian..*.service..*.*(..)) " +
            "&& !execution(* cec.jiutian.core.service..*.*(..)) " +
            "&& !execution(* cec.jiutian.core.comn.service..*.*(..)) " +
            "&& !execution(* cec.jiutian.*..service.business.PurgeBizService.*(..)) " +
            "&& !execution(* cec.jiutian.file.service..*.*(..))")
    public void pointCut() {
    }

    /**
     * 针对全局事务注解的
     */
    @Pointcut("@annotation(io.seata.spring.annotation.GlobalTransactional)")
    public void globalTransaction() {
    }

    @After("pointCut()")
    public void after() {
        String historyDataKey = TraceUtils.getHistoryDataKey();
        if (historyDataKey != null) {
            String xid = RootContext.getXID();
            // 存在分布式事务时，此处不做处理，交给HistoryDataTransactionHook去执行历史数据的最终提交操作
            // 在全局事务发起方的service方法结束后，全局事务并未commit，目前xid还存在，可以使用此判断
            if (xid == null) {
                // 当不存在分布式事务时，当前方法结束后直接对历史数据进行处理
                if (!TransactionAspectSupport.currentTransactionStatus().isRollbackOnly() && TransactionAspectSupport.currentTransactionStatus().isNewTransaction() && (BaseRedisCacheUtil.get(historyDataKey) != null)) {
                    SpringContextUtils.getBean(HistoryDataSender.class).send(historyDataKey);
                } else if (TransactionAspectSupport.currentTransactionStatus().isRollbackOnly() && BaseRedisCacheUtil.get(historyDataKey) != null) {
                    BaseRedisCacheUtil.delete(historyDataKey);
                }
            }
        }
    }

    /**
     * <p>对具有seata全局事务注解的方法进行切面处理</p>
     * <p>此方法名为beforeCommit，意为切面仅仅作用于begin之后，因为在进入切面方法之前，seata已经完成了全局事务的begin操作</p>
     * <p>这意味着我们自定义的hook中只有beforeCommit及以后的方法会被执行到，若需要执行begin相关的hook方法需要另行处理，此处忽略</p>
     * <p>因为seata对于hook的设计是事务级别的，每个事务完成后会自动清理当前线程中的所有hook，见{@link io.seata.tm.api.TransactionalTemplate}的execute方法</p>
     * <p>所以此切面的方法主要用于每次为新开启的全局事务注册hook</p>
     */
    @Before("globalTransaction()")
    public void beforeGlobalTransactionCommit() {
        // 在方法执行前，进行全局事务hook的注册
        TransactionHookManager.registerHook(HistoryDataTransactionHook.getInstance());
    }
}
