package cec.jiutian.core.transaction.hook;

import cec.jiutian.core.message.HistoryDataSender;
import cec.jiutian.core.util.BaseRedisCacheUtil;
import cec.jiutian.core.util.SpringContextUtils;
import cec.jiutian.core.util.TraceUtils;
import io.seata.core.context.RootContext;
import io.seata.tm.api.transaction.TransactionHook;

import java.util.Set;

/**
 * <p>针对历史数据以及seata全局事务的hook，单例模式</p>
 *
 * <AUTHOR>
 * @date 2023/1/10
 */
public class HistoryDataTransactionHook implements TransactionHook {

    private static final HistoryDataTransactionHook HISTORY_DATA_TRANSACTION_HOOK = new HistoryDataTransactionHook();

    public static HistoryDataTransactionHook getInstance() {
        return HISTORY_DATA_TRANSACTION_HOOK;
    }

    @Override
    public void beforeBegin() {
        // 此方法无法被调用
    }

    @Override
    public void afterBegin() {
        // 此方法无法被调用
    }

    @Override
    public void beforeCommit() {
    }

    @Override
    public void afterCommit() {
        if (RootContext.getXID() == null) {
            // 当xid不为空时，表示分支事务提交，此时不代表全局事务完成，不做处理
            // 当xid为空时，表示全局事务已经提交完成，此时可以进行历史数据的提交操作
            HistoryDataSender sender = SpringContextUtils.getBean(HistoryDataSender.class);
            sender.send(TraceUtils.getHistoryDataKey());
        }
    }

    @Override
    public void beforeRollback() {

    }

    @Override
    public void afterRollback() {
        if (RootContext.getXID() == null) {
            // 当xid为空时，表示全局事务已经提交完成，此时可以进行历史数据的提交操作
            // 执行历史数据的清理操作，删除redis中的缓存数据
            Set<String> keys = BaseRedisCacheUtil.getCacheKeys(TraceUtils.getTraceIdString());
            // delete方法不需要NPE
            BaseRedisCacheUtil.delete(keys);
        }
    }

    @Override
    public void afterCompletion() {
    }
}
