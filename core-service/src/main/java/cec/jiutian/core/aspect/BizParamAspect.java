package cec.jiutian.core.aspect;

import cec.jiutian.core.entity.TokenAnalysisEntity;
import cec.jiutian.core.service.TokenAnalysisService;
import cec.jiutian.security.base.dto.BaseOpDTO;
import cec.jiutian.security.base.dto.BaseQueryDTO;
import cec.jiutian.core.util.BizParamUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * <p>业务参数切面，包含对lastEventUser,lastEventComment,lastEventTime的处理</p>
 * <p>该切面作用于controller包下面的所有方法，即接口方法，在接口方法执行仅处理继承了{@link BaseOpDTO}的业务DTO中的lastEventUser,lastEventComment，</p>
 * <P>或者参数形式为form-data中的lastEventUser,lastEventComment，但只要是http接口，均会在本地线程中存储lastEventTime。</P>
 * <P>在方法执行后，会清空本地线程中的业务参数数据，防止内存泄漏。</P>
 *
 * <AUTHOR>
 * @date 2021/5/26
 */
@Aspect
@Component
@Slf4j
public class BizParamAspect {
    private static final String APPLICATION_JSON_CONTENT_TYPE = "application/json";
    private static final String MULTIPART_CONTENT_TYPE = "multipart/form-data";
    private static final String DISABLE_REASON_TEXT = "disableReasonText";
    private static final String OBSOLETE_REASON_TEXT = "obsoleteReasonText";
    private static final String LAST_EVENT_COMMENT = "lastEventComment";

    private final TokenAnalysisService tokenAnalysisService;

    public BizParamAspect(TokenAnalysisService tokenAnalysisService) {
        this.tokenAnalysisService = tokenAnalysisService;
    }

    @Pointcut("execution(* cec.jiutian.*..controller..*.*(..))||execution(* cec.jiutian.*..facade.remote..*.*(..))||execution(* cec.jiutian.*..schedule..*.*(..))")
    public void pointCut() {
    }

    @Before("pointCut()")
    public void before(JoinPoint joinPoint) throws Throwable {
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (servletRequestAttributes != null) {
            BizParamUtils.BizParam bizParam = new BizParamUtils.BizParam();
            HttpServletRequest request = servletRequestAttributes.getRequest();
            String token = request.getHeader("Authorization");
            TokenAnalysisEntity tokenAnalysisEntity = null;
            if (null != token) {
                tokenAnalysisEntity = tokenAnalysisService.decodeToken(token);
            }
            String contentType = request.getContentType();
            if (contentType == null) {
                BizParamUtils.setBizParam(bizParam);
                return;
            }
            if (null != tokenAnalysisEntity) {
                String lastEventUser = tokenAnalysisEntity.getActionUser();
                bizParam.setLastEventUser(lastEventUser);
                bizParam.setUserId(tokenAnalysisEntity.getUserId());
            }
            Map map = bizParam.getMap() == null ? new HashMap<>() : bizParam.getMap();
            if (contentType.contains(APPLICATION_JSON_CONTENT_TYPE)) {
                if (joinPoint.getArgs() != null && joinPoint.getArgs().length > 0) {
                    Object arg = joinPoint.getArgs()[0];
                    if (arg instanceof BaseOpDTO) {
                        bizParam.setLastEventComment(((BaseOpDTO) arg).getLastEventComment());
                        map.put(DISABLE_REASON_TEXT, ((BaseOpDTO) arg).getDisableReasonText());
                        map.put(OBSOLETE_REASON_TEXT, ((BaseOpDTO) arg).getObsoleteReasonText());
                    } else if(arg instanceof BaseQueryDTO) {
                        map.put("BaseQueryDTO", arg);
                    }
                } else {
                    log.info("{}", "The method has no param!");
                }
            } else if (contentType.contains(MULTIPART_CONTENT_TYPE)) {
                String[] disableReasonText = request.getParameterMap().get(DISABLE_REASON_TEXT);
                String[] obsoleteReasonText = request.getParameterMap().get(OBSOLETE_REASON_TEXT);
                if (disableReasonText != null) {
                    map.put(DISABLE_REASON_TEXT, disableReasonText[0]);
                }
                if (obsoleteReasonText != null) {
                    map.put(OBSOLETE_REASON_TEXT, obsoleteReasonText[0]);
                }
                String[] comment = request.getParameterValues(LAST_EVENT_COMMENT);
                if (comment != null) {
                    bizParam.setLastEventComment(comment[0]);
                }
            } else {
                log.info("{}", "The request's content type is " + contentType + " and can't handle it!");
            }
            bizParam.setMap(map);
            BizParamUtils.setBizParam(bizParam);
        } else {
            log.info("{}", "This is not a http request, so there is no biz param!");
        }
    }

    @After("pointCut()")
    public void after() {
        BizParamUtils.clearBizParam();
    }
}
