package cec.jiutian.core.aspect;

import cec.jiutian.core.comn.errorCode.BaseErrorCode;
import cec.jiutian.core.exception.MesErrorCodeException;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.util.Arrays;
import java.util.List;

/**
 * <p>对mapper层的持久化方法的返回值进行判断，通常情况下认为持久化操作未更新任何数据时表示此次操作失败</p>
 * <AUTHOR>
 * @date 2022/3/30
 */
@Aspect
@Component
@Slf4j
public class MapperPersistentAspect {

    @Pointcut("execution(public int cec.jiutian.*..mapper..*.updateByPrimaryKey(..)) "
            + "|| execution(public int cec.jiutian.*..mapper..*.insert(..)) "
            + "|| execution(public int cec.jiutian.*..mapper..*.insertSelective(..))"
            + "|| execution(public int cec.jiutian.*..mapper..*.updateByPrimaryKeySelective(..))"
            + "|| execution(public int cec.jiutian.*..mapper..*.deleteByPrimaryKey(..))")
    public void singlePersistenceCut() {
    }

    @Pointcut("execution(public int cec.jiutian.*..mapper..*.deleteBatchByPrimaryKey(..))"
            + "|| execution(public int cec.jiutian.*..mapper..*.insertBatch(..))"
            + "|| execution(public int cec.jiutian.*..mapper..*.updateBatch(..))"
            + "|| execution(public int cec.jiutian.*..mapper..*.updateBatchByPrimaryKey(..))")
    public void multiPersistenceCut() {
    }

    @AfterReturning(value = "singlePersistenceCut()", returning = "result")
    public void singlePersistenceReturning(int result) {
        log.info("本次单条数据的持久化执行结果：" + result);
        if (result != 1 && !TransactionAspectSupport.currentTransactionStatus().isRollbackOnly()) {
            MesErrorCodeException exception = new MesErrorCodeException(BaseErrorCode.SQL_OP_ERROR.getCode());
            log.error("{}", "本次持久化数据失败！" + exception.getMessage(), exception);
            throw exception;
        }
    }

    @AfterReturning(value = "multiPersistenceCut()", returning = "result")
    public void multiPersistenceReturning(JoinPoint joinPoint, int result) {
        Object[] args = joinPoint.getArgs();
        log.info("本地批量持久化的参数为：" + Arrays.toString(args));
        log.info("本次批量持久化执行结果:" + result);
        if (((List<?>) args[0]).size() != result && !TransactionAspectSupport.currentTransactionStatus().isRollbackOnly()) {
            MesErrorCodeException exception = new MesErrorCodeException(BaseErrorCode.SQL_OP_ERROR.getCode());
            log.error("{}", "本次持久化数据失败！" + exception.getMessage(), exception);
            throw exception;
        }
    }
}
