package cec.jiutian.core.aspect;

import brave.Tracer;
import cec.jiutian.core.comn.util.StringUtils;
import cec.jiutian.core.util.DomainParamUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;

/*
 * <AUTHOR>
 * <p>数据域参数切面，保存数据域相关的参数到本地线程中</p>
 * <p>该切面作用于controller包下面的所有方法，从接口层面获取token信息</p>
 * @Date 13:40 2022-3-28
 **/
@Aspect
@Component
@Slf4j
public class DomainAspect {
    @Value("${spring.datasource.url}")
    protected String dataBaseName = "";

    private final Tracer tracer;

    public DomainAspect(Tracer tracer) {
        this.tracer = tracer;
    }

    @Pointcut("execution(* cec.jiutian.*..controller..*.*(..))||execution(* cec.jiutian.*..facade.remote..*.*(..))||execution(* cec.jiutian.*..schedule..*.*(..))||execution(* cec.jiutian.*..restapi.*.*(..))")
    public void pointCut() {
    }
    @Before("pointCut()")
    public void before(JoinPoint joinPoint) throws Throwable {
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        String token = StringUtils.EMPTY;
        if (servletRequestAttributes != null) {
            DomainParamUtils.DomainParam threadParam = new DomainParamUtils.DomainParam();
            DomainParamUtils.setDomainParam(threadParam);
            HttpServletRequest request = servletRequestAttributes.getRequest();
            // 保存token

            long tid = Thread.currentThread().getId();
            log.info(   "********ThreadId: "  +tid );

            request.getHeader("Authorization");
            token = request.getHeader("Authorization");
            log.info(   "traceId:" + tracer.currentSpan().context().traceIdString() + "   /n  parent-token:  " +token );
            threadParam.setToken(token);
            String tokenStr = (String)DomainParamUtils.getParam("token");
            log.info("traceId:" + tracer.currentSpan().context().traceIdString() +   "   /n  Domain-token:  " +tokenStr );
            String uri = request.getRequestURI();
            log.info( "traceId:" + tracer.currentSpan().context().traceIdString() +   "   /n  Domain-uri:  " +tokenStr );
            threadParam.setUri(request.getRequestURI());
            threadParam.setDataBaseName(dataBaseName);
            DomainParamUtils.setDomainParam(threadParam);
        } else {
            log.info("{}", "This is not a http request, so there is no domain param!");
        }
        //Feign异步调用，但是在调用前，把父线程request信息绑定给子线程
        RequestContextHolder.setRequestAttributes(RequestContextHolder.getRequestAttributes(), true);
        DomainParamUtils.setToken(token);

    }

    @After("pointCut()")
    public void after() {
        DomainParamUtils.clearDomainParam();
    }
}
