package cec.jiutian.core.aspect;

import brave.Tracer;
import cec.jiutian.security.base.annotation.PathUrl;
import cec.jiutian.security.base.dto.FileMessageSendDTO;
import cec.jiutian.core.comn.message.FileMessageClient;
import cec.jiutian.core.comn.util.BeanUtils;
import cec.jiutian.core.util.BizParamUtils;
import io.seata.core.exception.TransactionException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date: 2022/11/7
 */
@Aspect
@Component
@Slf4j
//@EnableBinding(FileMessageClient.class)
@AllArgsConstructor
public class FileAspect {
    private final Tracer tracer;
    private final FileMessageClient fileMessageClient;

    @Pointcut("execution(* cec.jiutian.*..service.business..*.*(..)) " +
            "&& !execution(* cec.jiutian.core.service..*.*(..)) " +
            "&& !execution(* cec.jiutian.core.comn.service..*.*(..)) " +
            "&& !execution(* cec.jiutian.*..service.business.PurgeBizService.*(..)) " +
            "&& !execution(* cec.jiutian.file.service..*.*(..))")
    public void pointCut() {
    }

    public void fileSend(JoinPoint joinPoint, String transactionCompleteFlag) throws IllegalAccessException {
        String key = tracer.currentSpan().context().traceIdString() + "_" + TransactionSynchronizationManager.getCurrentTransactionName() + "_" + BizParamUtils.getLastEventTime();
        FileMessageSendDTO fileMessageSendDTO = new FileMessageSendDTO();
        fileMessageSendDTO.setTransactionCompleteFlag(transactionCompleteFlag);
        fileMessageSendDTO.setTransactionIdentifier(key);
        List<String> fileUUIDList = (List<String>) BizParamUtils.getParam("fileUUIDList");
        if (CollectionUtils.isNotEmpty(fileUUIDList)) {
            fileMessageSendDTO.setFileUUIDList(fileUUIDList);
        } else {
            Object[] argList = joinPoint.getArgs();
            List<String> fileUUIDListForFront = new ArrayList<>();
            for (Object arg : argList) {
                if (arg != null) {
                    List<Field> fieldList = BeanUtils.getAllFields(arg.getClass());
                    if (CollectionUtils.isNotEmpty(fieldList)) {
                        for (Field field : fieldList) {
                            if (field.isAnnotationPresent(PathUrl.class)) {
                                field.setAccessible(true);
                                fileUUIDListForFront.addAll(Arrays.asList(field.get(arg).toString().split(",")));
                            }
                        }
                    }
                }
            }
            fileMessageSendDTO.setFileUUIDList(fileUUIDListForFront);
        }
        try {
            if (CollectionUtils.isNotEmpty(fileMessageSendDTO.getFileUUIDList())) {
//                fileMessageClient.output().send(MessageBuilder.withPayload(JsonUtils.toJson(fileMessageSendDTO)).build());
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }

    @After("pointCut()")
    public void after(JoinPoint joinPoint) throws TransactionException, IllegalAccessException {
        if (tracer.currentSpan() != null) {
            if (!TransactionAspectSupport.currentTransactionStatus().isRollbackOnly() && TransactionAspectSupport.currentTransactionStatus().isNewTransaction()) {
                fileSend(joinPoint, "Y");
            } else if (TransactionAspectSupport.currentTransactionStatus().isRollbackOnly()) {
                fileSend(joinPoint, "N");
            }
        }
    }
}
