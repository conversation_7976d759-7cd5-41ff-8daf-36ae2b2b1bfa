package cec.jiutian.core.aspect;

import cec.jiutian.core.comn.errorCode.BaseErrorCode;
import cec.jiutian.core.exception.MesErrorCodeException;
import cec.jiutian.core.util.BaseEntityUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2021/8/22
 */
@Aspect
@Component
@Slf4j
public class BaseEntityBatchAspect {
    @Pointcut("execution(* cec.jiutian.*..service..*.*(..))")
    public void pointCut() {
    }

    @Before("pointCut()")
    public void before(JoinPoint joinPoint) {
        String name = joinPoint.getSignature().toLongString();
        if (BaseEntityUtils.getServiceMethodName() == null) {
            BaseEntityUtils.setServiceMethodName(name);
        }
    }

    @Around("pointCut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        Object proceed = joinPoint.proceed();
        String name = joinPoint.getSignature().toLongString();
        if (name.equals(BaseEntityUtils.getServiceMethodName())) {
            BaseEntityUtils.setServiceMethodName(null);
            if (BaseEntityUtils.isBatch()) {
                Boolean result = BaseEntityUtils.batchPersistent();
                // 异常处理，事务回滚
                if (!result) {
                    MesErrorCodeException exception = new MesErrorCodeException(BaseErrorCode.SQL_OP_ERROR.getCode());
                    log.error("{}", exception.getMessage(), exception);
                    throw exception;
                }
            }
        }
        return proceed;

    }
}
