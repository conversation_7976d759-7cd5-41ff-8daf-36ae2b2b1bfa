package cec.jiutian.core.mapper;

import cec.jiutian.security.base.po.TableIdConfigPO;
import cec.jiutian.core.base.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import java.util.List;

@Mapper
public interface TableIdConfigMapper extends BaseMapper<TableIdConfigPO> {

    @Select("select t.id as identifier,t.table_name as tableName,t.sequence_name as sequenceName,t.now_sequence as nowSequence,id_type as identifierType from fd_table_id_config t where t.table_name=#{tableName}")
    TableIdConfigPO getTableIdConfigByName(@Param("tableName") String tableName);

    @Select("select t.id as identifier,t.table_name as tableName,t.sequence_name as sequenceName,t.now_sequence as nowSequence,id_type as identifierType from fd_table_id_config t")
    List<TableIdConfigPO> getAllTableIdConfigList();

    @Select("SELECT a.table_name FROM (SELECT regexp_split_to_table( #{tableNames} , ',' ) AS table_name ) A LEFT JOIN fd_table_id_config b ON A.table_name = b.TABLE_NAME where b.id is null")
    List<String> getTableIdNotConfigList(@Param("tableNames") String tableNames);

    @Update("CREATE SEQUENCE ${sequenceName} INCREMENT BY 1 MINVALUE 0 MAXVALUE 9999999999999999 START WITH 1 NO CYCLE ")
    int createSequence(@Param("sequenceName") String sequenceName);

    @Options(flushCache = Options.FlushCachePolicy.TRUE)
    @Select("SELECT nextval(#{sequenceName}) as sequenceNum")
    Long getSequence(@Param("sequenceName") String sequenceName);

}
