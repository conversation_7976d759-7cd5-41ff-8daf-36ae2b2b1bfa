package cec.jiutian.core.mapper;

import cec.jiutian.core.provider.DeleteBatchProvider;
import org.apache.ibatis.annotations.DeleteProvider;
import tk.mybatis.mapper.annotation.RegisterMapper;

import java.util.List;

@RegisterMapper
public interface DeleteBatchMapper<T> {

    /**
     * 通用Mapper接口,特殊方法，批量删除,仅支持单主键
     *
     * @param recordList
     * @return
     * <AUTHOR>
     */
    @DeleteProvider(type = DeleteBatchProvider.class, method = "dynamicSQL")
    int deleteBatchForSinglePrimaryKey(List<T> recordList);

    /**
     * 该接口支持复合主键实体批量删除！
     *
     * @param recordList 不能为空！否则报错！
     * @return 返回实际删除数据的数量
     */
    @DeleteProvider(type = DeleteBatchProvider.class, method = "dynamicSQL")
    int deleteBatchByPrimaryKey(List<T> recordList);

}
