package cec.jiutian.core.mapper;

import cec.jiutian.core.provider.UpdateBatchProvider;
import org.apache.ibatis.annotations.UpdateProvider;
import tk.mybatis.mapper.annotation.RegisterMapper;

import java.util.List;

@RegisterMapper
public interface UpdateBatchMapper<T> {

    /**
     * 根据Example条件批量更新实体`record`包含的不是null的属性值，适用于单主键
     * <p>该方法将在下一版本进行删除，请使用{@link UpdateBatchMapper#updateBatch(List)}方法</p>
     */
    @UpdateProvider(type = UpdateBatchProvider.class, method = "dynamicSQL")
    @Deprecated
    int updateBatchByPrimaryKeySelective(List<? extends T> recordList);

    /**
     * 根据Example条件批量更新实体`record`的所有键值，适用于单主键
     * <p>该方法将在下一版本进行删除，请使用{@link UpdateBatchMapper#updateBatch(List)}方法</p>
     */
    @Deprecated
    @UpdateProvider(type = UpdateBatchProvider.class, method = "dynamicSQL")
    int updateBatchByPrimaryKey(List<? extends T> recordList);

    /**
     * 根据主键更新所有的字段，包括为null的字段。使用于所有情况（单主键复合主键皆可，MySQL、Oracle、PostgreSQL都支持，其余数据库待测试）
     */
    @UpdateProvider(type = UpdateBatchProvider.class, method = "dynamicSQL")
    int updateBatch(List<? extends T> recordList);
}
