package cec.jiutian.core.mapper;

import cec.jiutian.core.provider.SelectTreeProvider;
import org.apache.ibatis.annotations.SelectProvider;
import tk.mybatis.mapper.annotation.RegisterMapper;

import java.util.List;

/**
 * 通用Mapper接口,查询树结构
 *
 * @param <T> 不能为空
 * <AUTHOR>
 */
@RegisterMapper
public interface SelectTreeMapper<T> {
    /**
     * 根据实体中的属性值进行查询，查询条件使用等号
     * 从根节点查到子节点
     *
     * @param record
     * @return
     */
    @SelectProvider(type = SelectTreeProvider.class, method = "selectTreeChild")
    List<T> selectTreeChild(T record);


    /**
     * 只查pid为null的根节点
     * 从根节点查到子节点
     *
     * @param record
     * @return
     */
    @SelectProvider(type = SelectTreeProvider.class, method = "selectTreeRoot")
    List<T> selectTreeRoot(T record);
    /**
     * 根据实体中的属性值进行查询，查询条件使用等号
     * 从子节点查到根节点
     * @param record
     * @return
     */
    @SelectProvider(type = SelectTreeProvider.class, method = "selectTreeParent")
    List<T> selectTreeParent(T record);

}
