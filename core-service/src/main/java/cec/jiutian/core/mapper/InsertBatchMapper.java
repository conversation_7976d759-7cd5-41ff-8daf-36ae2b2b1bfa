package cec.jiutian.core.mapper;

import cec.jiutian.core.provider.InsertBatchProvider;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Options;
import tk.mybatis.mapper.annotation.RegisterMapper;

import java.util.List;

@RegisterMapper
public interface InsertBatchMapper<T> {
    /**
     * 批量插入，支持批量插入的数据库可以使用，例如MySQL,Oracle,H2等，id列无限制（单主键，自增非自增都可以用），MySQL数据库时，该操作插入后会返回主键，Oracle数据库待测试
     *
     * @param recordList 不能为空！否则报错！！！
     * @return 返回值为int，表示该批量操作影响的数据行数，例如批量插入两条数据，则返回2
     * <AUTHOR>
     */
    @InsertProvider(type = InsertBatchProvider.class, method = "dynamicSQL")
    @Options(useGeneratedKeys = true, keyProperty = "identifier", keyColumn = "id")
    int insertBatch(List<T> recordList);

    /**
     * 批量插入，支持批量插入的数据库可以使用，例如MySQL,Oracle,H2等，id列无限制（多主键），MySQL数据库时，该操作插入后会返回主键，Oracle数据库待测试
     *
     * @param recordList 不能为空！否则报错！！！
     * @return 返回值为int，表示该批量操作影响的数据行数，例如批量插入两条数据，则返回2
     * <AUTHOR>
     */
    @InsertProvider(type = InsertBatchProvider.class, method = "dynamicSQL")
    int insertBatchCompositeKey(List<T> recordList);
}
