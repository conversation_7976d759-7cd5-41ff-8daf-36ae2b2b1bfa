package cec.jiutian.core.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020/12/14
 */
@Data
@ApiModel("文件操作信息")
public class FileOperation implements Serializable {
    @ApiModelProperty("文件的UUID")
    private String id;
    @ApiModelProperty("目前支持更新和删除")
    private Operation operation;
    @ApiModelProperty("如果operation为UPDATE，name的值表示更新该文件的名称")
    private String name;

    public enum Operation {
        RENAME,
        DELETE,
    }
}
