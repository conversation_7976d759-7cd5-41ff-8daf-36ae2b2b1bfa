package cec.jiutian.core.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020/10/9
 */
@Data
@Entity
@Table(name = "DATA_DMN_ENTY_DCTNRY")
@ApiModel("数据域实体对应数据库字段表")
public class DataDomainEntityDictionary implements Serializable {
    @Id
    @Column(name = "ENTY_CD")
    @ApiModelProperty(value = "实体类型", allowableValues = "FCTRY,WRHS")
    private String entityCode;

    @Id
    @Column(name = "TBL_NM")
    @ApiModelProperty("数据库表名，用于拦截器判断")
    private String tableName;

    @Column(name = "CLMN_NM")
    @ApiModelProperty("字段名")
    private String columnName;

    @Column(name = "SUB_ENTY_FLG")
    @ApiModelProperty(value = "是否包含子实体字段", allowableValues = "Y,N")
    private String subEntityFlag;

    @Column(name = "SUB_CLMN_NM")
    @ApiModelProperty("子实体字段名")
    private String subColumnName;
}
