package cec.jiutian.core.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020/11/12
 */
@Entity
@Data
@Table(name = "MSTR_DATA_STG")
public class MasterDataStage implements Serializable {
    @Id
    @Column(name = "UUID")
    private String uuid;

    @Column(name = "CNTNT_TX")
    private String contentText;
}
