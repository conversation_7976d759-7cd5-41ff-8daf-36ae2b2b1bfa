package cec.jiutian.core.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import java.io.Serializable;

/**
 * @title: TokenAnalysisEntity.java
 * @package cec.jiutian.mes.core.entity
 * @description: 功能说明（jira task id）
 * @author: <EMAIL>
 * @date: 2022-3-16 10:39
 * @version: 2.5.3
 */
@Data
@Entity
@ApiModel("Token解析DTO")
public class TokenAnalysisEntity implements Serializable {

    private static final long serialVersionUID = 2991285812825997839L;

    @ApiModelProperty(value = "用户名")
    @Column(name = "USR_ID")
    private String userId;

    @ApiModelProperty(value = "管控模式")
    private String controlMode;

    @ApiModelProperty(value = "组织ID")
    @Column(name = "ORG_ID")
    private String organizationIdentifier;

    @Column(name = "MSTR_DATA_CD_CLMN_NM")
    @ApiModelProperty("编码字段名")
    private String masterDataCodeColumnName;

    @ApiModelProperty(value = "控制对象值")
    @Column(name = "CNTL_VLU_TX")
    private String controlValueText;

    @ApiModelProperty(value = "系统编码")
    @Column(name = "SYSTM_CD")
    private String systemCode;

    @ApiModelProperty(value = "name+login id")
    @Column(name = "ACT_USR")
    private String actionUser;

}
