package cec.jiutian.core.entity;

import cec.jiutian.security.base.entity.BaseEntity;
import cec.jiutian.core.util.BizParamUtils;

/**
 * Domain类不参与任何序列化和传输的工作
 *
 * <AUTHOR>
 * @date 2022/3/3
 */
public abstract class AbstractDomain<Entity extends BaseEntity> implements Domain<Entity> {
    protected Entity entity;

    public AbstractDomain(Entity entity) {
        this.entity = entity;
    }

    @Override
    public void setEntity(Entity entity) {
        this.entity = entity;
    }

    @Override
    public Entity getEntity() {
        return entity;
    }

    @Override
    public void setAuditField() {
        entity.setLastEventTime(BizParamUtils.getLastEventTime());
        if (BizParamUtils.getLastEventUser() != null) {
            entity.setLastEventUser(BizParamUtils.getLastEventUser());
        }
        if (BizParamUtils.getLastEventComment() != null) {
            entity.setLastEventComment(BizParamUtils.getLastEventComment());
        }
        if (BizParamUtils.getOperationName() != null) {
            entity.setLastEventName(BizParamUtils.getOperationName());
        }
        if (entity.getCreateTime() == null) {
            entity.setCreateTime(BizParamUtils.getLastEventTime());
            entity.setCreateUser(entity.getLastEventUser());
        }
    }
}
