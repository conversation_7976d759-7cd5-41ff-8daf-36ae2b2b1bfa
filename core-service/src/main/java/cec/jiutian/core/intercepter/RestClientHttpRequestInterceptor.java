package cec.jiutian.core.intercepter;

import cec.jiutian.core.util.DomainParamUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.util.Collections;

/**
 * @description: 拦截RestTemplate 请求
 * @author: chenjx
 * @date: 20220908
 */
@Configuration
@Slf4j
public class RestClientHttpRequestInterceptor {
    @Bean
    @LoadBalanced
    public RestTemplate restTemplate() {
        RestTemplate restTemplate = new RestTemplate();
        restTemplate.setInterceptors(Collections.singletonList(new RestTemplateClientHttpRequestInterceptor()));
        return restTemplate;
    }

    public class RestTemplateClientHttpRequestInterceptor implements ClientHttpRequestInterceptor {
        @Override
        public ClientHttpResponse intercept(HttpRequest request, byte[] body, ClientHttpRequestExecution execution) throws IOException {
            HttpHeaders headers = request.getHeaders();
            // 添加token
            String token = (DomainParamUtils.getToken());
            log.info("RestClientHttpRequestInterceptor-token:  " + token);
            headers.add("Authorization", DomainParamUtils.getToken());
            log.info("RestTemplat拦截器已添加header");
            return execution.execute(request, body);
        }
    }
}
