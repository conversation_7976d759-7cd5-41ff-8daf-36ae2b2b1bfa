package cec.jiutian.core.intercepter;

import cec.jiutian.security.base.annotation.AutoId;
import cec.jiutian.security.base.po.TableIdConfigPO;
import cec.jiutian.core.generator.GeneralIdGenerator;
import cec.jiutian.core.generator.IdGenerator;
import cec.jiutian.core.generator.SequenceIdGenerator;
import cec.jiutian.core.generator.TimeSequenceIdGenerator;
import cec.jiutian.core.service.TableIdConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.plugin.*;
import org.reflections.ReflectionUtils;
import javax.persistence.Table;
import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @Description: mybatis ID自增拦截器
 * @date 2023/4/22 下午15:38
 */
@Intercepts({@Signature(type = Executor.class, method = "update", args = {MappedStatement.class, Object.class})})
@Slf4j
public class AutoIdInterceptor implements Interceptor {


    private TableIdConfigService tableIdConfigService;

    public AutoIdInterceptor(TableIdConfigService tableIdConfigService) {
        this.tableIdConfigService = tableIdConfigService;
    }


    /**
     *  key值为class对象 value可以理解成是该类带有AutoId注解的属性，只不过对属性封装了一层。
     * 它是非常能够提高性能的处理器 它的作用就是不用每一次一个对象经来都要看下它的哪些属性带有AutoId注解
     * 毕竟类的反射在性能上并不友好。只要key包含该对象那就不需要检查它哪些属性带AutoId注解。
     */
    private Map<Class, List<IdGenerator>> generatorMap = new ConcurrentHashMap<>();

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        Object[] args = invocation.getArgs();
        //args数组对应对象就是上面@Signature注解中args对应的对应类型
        MappedStatement mappedStatement = (MappedStatement) args[0];
        if (mappedStatement.getId().equals("cec.jiutian.core.ext.base.mapper.AttrMapper.insertAttr") || mappedStatement.getId().equals("cec.jiutian.core.ext.base.mapper.AttrMapper.insertAttrSet")) {
            return invocation.proceed();
        }
        if (!"INSERT".equalsIgnoreCase(mappedStatement.getSqlCommandType().name())) {
            return invocation.proceed();
        }
        //实体对象
        Object entity = args[1];
        // 获取实体集合
        List<Object> entityList = getEntityList(entity);
        // 批量设置id
        for (Object object : entityList) {
            process(object);
        }
        return invocation.proceed();
    }



    /**
     * object是需要插入的实体数据,它可能是对象,也可能是批量插入的对象。
     * 如果是单个对象,那么object就是当前对象
     * 如果是批量插入对象，那么object就是一个map集合,key值为"list",value为ArrayList集合对象
     */
    private List<Object> getEntityList(Object object) {
        //
        List<Object> list = new ArrayList<>();
        if (object instanceof Map) {
            //批量插入对象
            Collection values = (Collection) ((Map) object).get("list");
            for (Object value : values) {
                if (value instanceof Collection) {
                    list.addAll((Collection) value);
                } else {
                    list.add(value);
                }
            }
        } else {
            //单个插入对象
            list.add(object);
        }
        return list;
    }

    private void process(Object object) throws Throwable {
        Class generatorKey = object.getClass();
        List<IdGenerator> generatorList = generatorMap.get(generatorKey);
        SYNC:
        if (generatorList == null) {
            synchronized (this) {
                generatorList = generatorMap.get(generatorKey);
                //如果到这里map集合已经存在，则跳出到指定SYNC标签
                if (generatorList != null) {
                    break SYNC;
                }
                generatorMap.put(generatorKey, generatorList = new ArrayList<>());
                // 反射工具类 获取带有AutoId注解的所有属性字段
                Set<Field> autoIdFields = ReflectionUtils.getAllFields(
                        object.getClass(),input -> input != null && input.getAnnotation(AutoId.class) != null
                );
                Table table = object.getClass().getAnnotation(Table.class);
                if(table==null){
                    return;
                }
                TableIdConfigPO configPO = tableIdConfigService.getTableIdConfig(table.name());
                if(configPO==null){
                    return;
                }
                configPO.setServiceId(TableIdConfigService.serviceId);
                for (Field field : autoIdFields) {
                    //1、添加作为主键
                    if (field.getType().isAssignableFrom(String.class)) {
                        if (configPO.getIdentifierType().equals(AutoId.IdType.SEQUENCE.name())) {
                            generatorList.add(new SequenceIdGenerator(field,configPO,tableIdConfigService));
                        } else if (configPO.getIdentifierType().equals(AutoId.IdType.GENERAL.name())) {
                            generatorList.add(new GeneralIdGenerator(field,configPO,tableIdConfigService));
                        } else if(configPO.getIdentifierType().equals(AutoId.IdType.TIME_SEQUENCE.name())){
                            generatorList.add(new TimeSequenceIdGenerator(field,configPO));
                        }
                    }
                }
            }
        }
        for (IdGenerator generator : generatorList) {
            generator.accept(object);
        }
    }

    @Override
    public Object plugin(Object target) {
        // 只对要拦截制定类型的对象生成代理
        if(target instanceof Executor){
            // 调用插件
            return Plugin.wrap(target, this);
        }
        return target;
    }

    @Override
    public void setProperties(Properties properties) {

    }
}
