package cec.jiutian.core.intercepter;

import brave.Tracer;
import cec.jiutian.security.base.dto.HistoryResultDTO;
import cec.jiutian.core.comn.util.BeanUtils;
import cec.jiutian.core.comn.util.StringUtils;
import cec.jiutian.core.util.BaseRedisCacheUtil;
import cec.jiutian.core.util.BizParamUtils;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Plugin;
import org.apache.ibatis.plugin.Signature;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.reflection.SystemMetaObject;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.persistence.Id;
import java.io.Serializable;
import java.lang.reflect.Field;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.stream.Collectors;

@SuppressWarnings({"rawtypes"})
@Slf4j
@Intercepts({
        @Signature(type = StatementHandler.class, method = "update", args = {Statement.class})
})

public class HistoryInsertAndUpdateInterceptor implements Interceptor {

    private final Tracer tracer;

    public HistoryInsertAndUpdateInterceptor(Tracer tracer) {
        this.tracer = tracer;
    }

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        Object result = invocation.proceed();
        List<HistoryResultDTO> historyResultDTOList = new ArrayList<>();
        if (tracer.currentSpan() == null) {
            return result;
        }
        List<HistoryResultDTO> existedHistoryResultDTOList = (List<HistoryResultDTO>) BaseRedisCacheUtil.get(tracer.currentSpan().context().traceIdString() + "_" + TransactionSynchronizationManager.getCurrentTransactionName() + "_" + BizParamUtils.getLastEventTime());
        if (CollectionUtils.isNotEmpty(existedHistoryResultDTOList)) {
            historyResultDTOList = existedHistoryResultDTOList;
        }
        Object target = invocation.getTarget();
        MetaObject metaObject = SystemMetaObject.forObject(target);
        //分离代理对象链
        while (metaObject.hasGetter("h")) {
            Object obj = metaObject.getValue("h");
            metaObject = SystemMetaObject.forObject(obj);
        }

        while (metaObject.hasGetter("target")) {
            Object obj = metaObject.getValue("target");
            metaObject = SystemMetaObject.forObject(obj);
        }
        MappedStatement ms = (MappedStatement) metaObject.getValue("delegate.parameterHandler.mappedStatement");
        SqlCommandType sqlCommandType = ms.getSqlCommandType();
        BoundSql boundSql = (BoundSql) metaObject.getValue("boundSql");
        String sql = boundSql.getSql().toUpperCase();
        String tableName = "";
        if (SqlCommandType.INSERT.equals(sqlCommandType)) {
            tableName = sql.substring(sql.indexOf("INTO") + 4, sql.indexOf("(")).trim();
        } else if (SqlCommandType.UPDATE.equals(sqlCommandType)) {
            if (!sql.startsWith("ALTER") && !sql.startsWith("COMMENT")) {
                tableName = sql.substring(sql.indexOf("UPDATE") + 6, sql.indexOf("SET")).trim();
            }
        } else if (SqlCommandType.DELETE.equals(sqlCommandType)) {
            tableName = sql.substring(sql.indexOf("FROM") + 4, sql.indexOf("WHERE")).trim();
        }
        JSONObject hsDfObject = (JSONObject) BaseRedisCacheUtil.get(("HS" + "_" + tableName).toLowerCase());
        if (hsDfObject != null) {
            Object pObject = metaObject.getValue("delegate.parameterHandler.parameterObject");
            if (!SqlCommandType.DELETE.equals(sqlCommandType)) {
                if (pObject instanceof Map) {
                    Map map = (Map) pObject;
                    List<Object> listParamObj = (List<Object>) map.get("list");
                    for (int i = 0; i < listParamObj.size(); i++) {
                        StringBuilder stringBuilder = new StringBuilder();
                        List<String> primKeyList = new ArrayList<>();
                        Object object = listParamObj.get(i);
                        JSONObject jsonObject = (JSONObject) JSONObject.toJSON(object);
                        if (hsDfObject.get("excludeFieldText") != null) {
                            String[] excludeFieldTextList = hsDfObject.get("excludeFieldText").toString().split(",");
                            if (excludeFieldTextList.length > 0 && excludeFieldTextList != null) {
                                for (int n = 0; n < excludeFieldTextList.length; n++) {
                                    jsonObject.remove(excludeFieldTextList[n].trim());
                                }
                            }
                        }
                        String contentText = jsonObject.toJSONStringWithDateFormat(jsonObject, "yyyy-MM-dd HH:mm:ss:SSS", SerializerFeature.WriteDateUseDateFormat);
                        List<Field> fields = BeanUtils.getAllFields(object.getClass());
                        for (Field field : fields) {
                            if (field.isAnnotationPresent(Id.class)) {
                                primKeyList.add(field.getName());
                            }
                        }
                        for (int k = 0; k < primKeyList.size(); k++) {
                            String primKey = primKeyList.get(k);
                            Object keyValue = jsonObject.get(primKey);
                            stringBuilder.append(primKey + "_" + keyValue);
                            if (k != primKeyList.size() - 1) {
                                stringBuilder.append(",");
                            }
                        }
                        String newPrimaryKeyAndValue = stringBuilder.toString();
                        if (SqlCommandType.UPDATE.equals(sqlCommandType)) {
                            String newTableName = tableName;
                            historyResultDTOList.forEach(updateHistoryResultDTO -> {
                                if (StringUtils.equals(updateHistoryResultDTO.getOldTableName(), newTableName) && StringUtils.equals(updateHistoryResultDTO.getOldPrimaryKeyAndValue(), newPrimaryKeyAndValue)) {
                                    updateHistoryResultDTO.setNewTableName(newTableName);
                                    updateHistoryResultDTO.setNewPrimaryKeyAndValue(newPrimaryKeyAndValue);
                                    updateHistoryResultDTO.setNewContentText(contentText);
                                    updateHistoryResultDTO.setCommandType(sqlCommandType.toString());
                                }
                            });
                        } else if (SqlCommandType.INSERT.equals(sqlCommandType)) {
                            HistoryResultDTO historyResultDTO = new HistoryResultDTO();
                            historyResultDTO.setNewTableName(tableName);
                            historyResultDTO.setNewPrimaryKeyAndValue(stringBuilder.toString());
                            historyResultDTO.setNewContentText(contentText);
                            historyResultDTO.setCommandType(sqlCommandType.toString());
                            historyResultDTO.setHistoryDataSetDefinitionIdentifier(Long.valueOf(hsDfObject.get("identifier").toString()));
                            historyResultDTO.setDataSetUUID(hsDfObject.get("dataSetUUID").toString());
                            historyResultDTO.setDataSetName(hsDfObject.get("dataSetName").toString());
                            historyResultDTO.setLevelNumber(Long.valueOf(hsDfObject.get("levelNumber").toString()));
                            historyResultDTOList.add(historyResultDTO);
                        }
                    }
                } else {
                    StringBuilder stringBuilder = new StringBuilder();
                    List<String> primKeyList = new ArrayList<>();
                    JSONObject jsonObjectForSingle = (JSONObject) JSONObject.toJSON(pObject);
                    if (hsDfObject.get("excludeFieldText") != null) {
                        String[] excludeFieldTextList = hsDfObject.get("excludeFieldText").toString().split(",");
                        if (excludeFieldTextList.length > 0 && excludeFieldTextList != null) {
                            for (int n = 0; n < excludeFieldTextList.length; n++) {
                                jsonObjectForSingle.remove(excludeFieldTextList[n].trim());
                            }
                        }
                    }
                    String contentTextForSingle = jsonObjectForSingle.toJSONStringWithDateFormat(jsonObjectForSingle, "yyyy-MM-dd HH:mm:ss:SSS", SerializerFeature.WriteDateUseDateFormat);
                    List<Field> fields = BeanUtils.getAllFields(pObject.getClass());
                    for (Field field : fields) {
                        if (field.isAnnotationPresent(Id.class)) {
                            primKeyList.add(field.getName());
                        }
                    }
                    for (int j = 0; j < primKeyList.size(); j++) {
                        String primKey = primKeyList.get(j);
                        Object keyValue = jsonObjectForSingle.get(primKey);
                        stringBuilder.append(primKey + "_" + keyValue);
                        if (j != primKeyList.size() - 1) {
                            stringBuilder.append(",");
                        }
                    }
                    String newPrimaryKeyAndValue = stringBuilder.toString();
                    if (SqlCommandType.UPDATE.equals(sqlCommandType)) {
                        String newTableName = tableName;
                        historyResultDTOList.forEach(updateHistoryResultDTO -> {
                            if (StringUtils.equals(updateHistoryResultDTO.getOldTableName(), newTableName) && StringUtils.equals(updateHistoryResultDTO.getOldPrimaryKeyAndValue(), newPrimaryKeyAndValue)) {
                                updateHistoryResultDTO.setNewTableName(newTableName);
                                updateHistoryResultDTO.setNewPrimaryKeyAndValue(newPrimaryKeyAndValue);
                                updateHistoryResultDTO.setNewContentText(contentTextForSingle);
                                updateHistoryResultDTO.setCommandType(sqlCommandType.toString());
                            }
                        });
                    } else if (SqlCommandType.INSERT.equals(sqlCommandType)) {
                        log.info("表名:" + tableName + "," + "主键及其值:" + stringBuilder.toString() + "," + "traceId:" + tracer.currentSpan().context().traceIdString());
                        HistoryResultDTO historyResultDTO = new HistoryResultDTO();
                        historyResultDTO.setNewTableName(tableName);
                        historyResultDTO.setNewPrimaryKeyAndValue(stringBuilder.toString());
                        historyResultDTO.setNewContentText(contentTextForSingle);
                        historyResultDTO.setCommandType(sqlCommandType.toString());
                        historyResultDTO.setHistoryDataSetDefinitionIdentifier(Long.valueOf(hsDfObject.get("identifier").toString()));
                        historyResultDTO.setDataSetUUID(hsDfObject.get("dataSetUUID").toString());
                        historyResultDTO.setDataSetName(hsDfObject.get("dataSetName").toString());
                        historyResultDTO.setLevelNumber(Long.valueOf(hsDfObject.get("levelNumber").toString()));
                        historyResultDTOList.add(historyResultDTO);
                    }
                }
            }
            if (SqlCommandType.DELETE.equals(sqlCommandType)) {
                if (pObject instanceof Map) {
                    Map map = (Map) pObject;
                    List<Object> listParamObj = (List<Object>) map.get("list");
                    for (int i = 0; i < listParamObj.size(); i++) {
                        StringBuilder stringBuilder = new StringBuilder();
                        List<String> primKeyList = new ArrayList<>();
                        Object object = listParamObj.get(i);
                        JSONObject jsonObject = (JSONObject) JSONObject.toJSON(object);
                        List<Field> fields = BeanUtils.getAllFields(object.getClass());
                        for (Field field : fields) {
                            if (field.isAnnotationPresent(Id.class)) {
                                primKeyList.add(field.getName());
                            }
                        }
                        for (int k = 0; k < primKeyList.size(); k++) {
                            String primKey = primKeyList.get(k);
                            Object keyValue = jsonObject.get(primKey);
                            stringBuilder.append(primKey + "_" + keyValue);
                            if (k != primKeyList.size() - 1) {
                                stringBuilder.append(",");
                            }
                        }
                        String newTableName = tableName;
                        historyResultDTOList.forEach(historyDeleteResultDTO -> {
                            if (StringUtils.equals(historyDeleteResultDTO.getOldTableName(), newTableName) && StringUtils.equals(historyDeleteResultDTO.getOldPrimaryKeyAndValue(), stringBuilder.toString())) {
                                historyDeleteResultDTO.setCommandType(sqlCommandType.toString());
                            }
                        });
                    }
                } else {
                    String newTableName = tableName;
                    historyResultDTOList.forEach(historyDeleteResultDTO -> {
                        if (StringUtils.equals(historyDeleteResultDTO.getOldTableName(), newTableName)) {
                            historyDeleteResultDTO.setCommandType(sqlCommandType.toString());
                        }
                    });
                }
            }
            historyResultDTOList = historyResultDTOList.stream().filter(item -> StringUtils.isNotBlank(item.getCommandType())).distinct().collect(Collectors.toList());
            BaseRedisCacheUtil.set(tracer.currentSpan().context().traceIdString() + "_" + TransactionSynchronizationManager.getCurrentTransactionName() + "_" + BizParamUtils.getLastEventTime(), (Serializable) historyResultDTOList);
        }
        return result;
    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {

    }
}
