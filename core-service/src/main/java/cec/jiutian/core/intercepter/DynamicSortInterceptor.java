package cec.jiutian.core.intercepter;

import cec.jiutian.security.base.dto.BaseQueryDTO;
import cec.jiutian.security.base.dto.BaseQueryDTO.SortParam;
import cec.jiutian.core.comn.util.StringUtils;
import cec.jiutian.core.util.SpringContextUtils;
import com.github.pagehelper.BoundSqlInterceptor;
import com.github.pagehelper.util.ExecutorUtil;
import com.github.pagehelper.util.MetaObjectUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.ibatis.cache.CacheKey;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.ResultMap;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.session.Configuration;
import org.apache.ibatis.session.SqlSessionFactory;

import java.util.ArrayList;
import java.util.List;

/**
 * 随机字段排序拦截器，基于pageHelper的{@link BoundSqlInterceptor}，在原始SQL的最外层增加前端传递的排序条件
 *
 * <AUTHOR>
 * @date 2021/1/21
 */
@SuppressWarnings("unchecked")
public class DynamicSortInterceptor implements BoundSqlInterceptor {
    private static final Configuration CONFIGURATION = SpringContextUtils.getBean(SqlSessionFactory.class).getConfiguration();

    private static final DynamicSortInterceptor DYNAMIC_SORT_INTERCEPTOR = new DynamicSortInterceptor();

    public static DynamicSortInterceptor getInstance() {
        return DYNAMIC_SORT_INTERCEPTOR;
    }

    @Override
    public BoundSql boundSql(Type type, BoundSql boundSql, CacheKey cacheKey, Chain chain) {
        if (type == Type.ORIGINAL && boundSql.getParameterObject() instanceof BaseQueryDTO) {
            List<SortParam> sort = ((BaseQueryDTO) boundSql.getParameterObject()).getSort();
            if (CollectionUtils.isNotEmpty(sort)) {
                // 需要从cacheKey中取出这条SQL的ID
                MetaObject cache = MetaObjectUtil.forObject(cacheKey);
                List<Object> updateList = (List<Object>) cache.getValue("updateList");
                String sqlId = StringUtils.substringAfterLast(String.valueOf(updateList.get(0)), ".");
                // 此处只应对一个SQL对应一个ResultMap的情况，若出现有多个ResultMap再进行完善
                ResultMap resultMap = ExecutorUtil.getExistedMappedStatement(CONFIGURATION, sqlId).getResultMaps().get(0);
                List<SortParam> actualSort = new ArrayList<>();
                // 根据resultMap和查询参数中的sort筛选和匹配数据库真实的字段名，可避免SQL注入
                sort.stream().filter(it -> StringUtils.equalsAnyIgnoreCase(it.getSortType(), "asc", "desc"))
                        .forEach(sortParam -> resultMap.getResultMappings()
                                .stream()
                                .filter(it -> StringUtils.equals(it.getProperty(), sortParam.getField()))
                                .findFirst()
                                .ifPresent(resultMapping -> actualSort.add(new SortParam(resultMapping.getColumn(), sortParam.getSortType()))));
                if (CollectionUtils.isNotEmpty(actualSort)) {
                    MetaObject metaObject = MetaObjectUtil.forObject(boundSql);
                    metaObject.setValue("sql", getSortSql(actualSort, boundSql.getSql()));
                }
            }
        }
        return boundSql;
    }

    private String getSortSql(List<SortParam> sort, String sql) {
        StringBuilder sqlBuilder = new StringBuilder(sql.length() + 400);
        sqlBuilder.append("SELECT * FROM ( ");
        sqlBuilder.append(sql);
        sqlBuilder.append("\n ) TMP_SORT ORDER BY ");
        sort.forEach(sortParam -> sqlBuilder.append(sortParam.getField()).append(" ").append(sortParam.getSortType()).append(","));
        sqlBuilder.deleteCharAt(sqlBuilder.length() - 1);
        return sqlBuilder.toString();
    }
}
