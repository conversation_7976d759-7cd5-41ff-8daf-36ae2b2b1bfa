package cec.jiutian.core.intercepter;

import cec.jiutian.core.comn.constant.AuthenticationConstant;
import cec.jiutian.core.comn.util.StringUtils;
import cec.jiutian.core.config.DataDomainEnv;
import cec.jiutian.core.entity.TokenAnalysisEntity;
import cec.jiutian.core.service.DataSourceService;
import cec.jiutian.core.service.TokenAnalysisService;
import cec.jiutian.core.util.DomainParamUtils;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.expression.BinaryExpression;
import net.sf.jsqlparser.expression.CaseExpression;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.NotExpression;
import net.sf.jsqlparser.expression.Parenthesis;
import net.sf.jsqlparser.expression.StringValue;
import net.sf.jsqlparser.expression.WhenClause;
import net.sf.jsqlparser.expression.operators.conditional.AndExpression;
import net.sf.jsqlparser.expression.operators.conditional.OrExpression;
import net.sf.jsqlparser.expression.operators.relational.EqualsTo;
import net.sf.jsqlparser.expression.operators.relational.ExistsExpression;
import net.sf.jsqlparser.expression.operators.relational.ExpressionList;
import net.sf.jsqlparser.expression.operators.relational.InExpression;
import net.sf.jsqlparser.expression.operators.relational.ItemsList;
import net.sf.jsqlparser.expression.operators.relational.MultiExpressionList;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.schema.Column;
import net.sf.jsqlparser.schema.Table;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.select.FromItem;
import net.sf.jsqlparser.statement.select.GroupByElement;
import net.sf.jsqlparser.statement.select.Join;
import net.sf.jsqlparser.statement.select.LateralSubSelect;
import net.sf.jsqlparser.statement.select.OrderByElement;
import net.sf.jsqlparser.statement.select.PlainSelect;
import net.sf.jsqlparser.statement.select.Select;
import net.sf.jsqlparser.statement.select.SelectBody;
import net.sf.jsqlparser.statement.select.SelectExpressionItem;
import net.sf.jsqlparser.statement.select.SelectItem;
import net.sf.jsqlparser.statement.select.SetOperationList;
import net.sf.jsqlparser.statement.select.SubJoin;
import net.sf.jsqlparser.statement.select.SubSelect;
import net.sf.jsqlparser.statement.select.ValuesList;
import net.sf.jsqlparser.statement.select.WithItem;
import org.apache.commons.collections.CollectionUtils;
import org.apache.ibatis.cache.CacheKey;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Plugin;
import org.apache.ibatis.plugin.Signature;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.PathMatcher;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Properties;
import java.util.stream.Collectors;

@SuppressWarnings({"rawtypes"})
@Slf4j
@Intercepts({
        @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class}),
        @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class, CacheKey.class, BoundSql.class}),
})
//@Component
public class DataDomainQueryInterceptor implements Interceptor {
    /*    @Value("${spring.application.name:app}")
        protected String serverName;
        @Value("#{'${data.domain.exclude.uris:aaa}'.split(',')}")
        protected String[] excludeUris;
        @Value("#{'${data.domain.exclude.login.uris:aaa}'.split(',')}")
        protected String[] loginExcludeUris;
        @Value("${data.domain.valid.flag:Y}")
        protected String dataDomainValidFlag;*/
    private final DataDomainEnv env;
//    protected TokenAnalysisEntity tokenAnalysisEntity;
//
//    List<String> effectTables;

    private final TokenAnalysisService tokenAnalysisService;
    private final DataSourceService dataSourceService;

    public DataDomainQueryInterceptor(DataDomainEnv env
            , TokenAnalysisService tokenAnalysisService
            , DataSourceService dataSourceService) {
        this.env = env;
        this.tokenAnalysisService = tokenAnalysisService;
        this.dataSourceService = dataSourceService;
    }

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        Object[] args = invocation.getArgs();
        MappedStatement ms = (MappedStatement) args[0];

        Object parameter = args[1];
        RowBounds rowBounds = (RowBounds) args[2];
        ResultHandler resultHandler = (ResultHandler) args[3];
        Executor executor = (Executor) invocation.getTarget();
        CacheKey cacheKey;
        BoundSql boundSql;
        //由于逻辑关系，只会进入一次
        if (args.length == 4) {
            //4 个参数时
            boundSql = ms.getBoundSql(parameter);
            cacheKey = executor.createCacheKey(ms, parameter, rowBounds, boundSql);
        } else {
            //6 个参数时
            cacheKey = (CacheKey) args[4];
            boundSql = (BoundSql) args[5];
        }
        // 本地测试不做拦截
        if ("N".equals(env.dataDomainValidFlag)) {
            return executor.query(ms, parameter, rowBounds, resultHandler, cacheKey, boundSql);
        }
        // 登录专用的接口不做拦截
        if (null != env.loginExcludeUris && Arrays.asList(env.loginExcludeUris).contains(DomainParamUtils.getUri())) {
            return executor.query(ms, parameter, rowBounds, resultHandler, cacheKey, boundSql);
        }
        //检查token  一个线程只检查一次
        if (!"Y".equals(DomainParamUtils.getParam("checkTokenFlag"))) {
            tokenAnalysisService.checkToken();
            DomainParamUtils.putParam("checkTokenFlag", "Y");
        }
        // 一个线程只从Redis中取一次
        TokenAnalysisEntity tokenAnalysisEntity = getTokenAnalysisEntity();
        if (null == tokenAnalysisEntity) {
            return executor.query(ms, parameter, rowBounds, resultHandler, cacheKey, boundSql);
        }
        List<String> domainTables = getDomainTables();
        List<String> orgIdTables = getOrgIdTables();
        String controlMode = tokenAnalysisEntity.getControlMode();
        String orgId = tokenAnalysisEntity.getOrganizationIdentifier();
        // 用户是否为超级管理员
        boolean rootFlag = null != tokenAnalysisEntity && AuthenticationConstant.ROOT_NAME.equals(tokenAnalysisEntity.getUserId());
        // 当前URL是否为排除URL
        boolean excludeUrlFlag = null != env.excludeUris && Arrays.asList(env.excludeUris).contains(DomainParamUtils.getUri());
        // 当前URL是否为 feature下的
        boolean featureFlag = AuthenticationConstant.FEATURE_SERVER_NAME.equals(env.serverName);
        // 当前管控模式是否为NONE
        boolean MODE_NON_FLG = StringUtils.equals(controlMode, AuthenticationConstant.CONTROL_MODE_NON);
        // 当前管控模式是否为ORGANIZATION
        boolean MODE_ORG_FLG = StringUtils.equals(controlMode, AuthenticationConstant.CONTROL_MODE_ORG);
        // 当前管控模式是否为ENTITY
        boolean MODE_ENT_FLG = StringUtils.equals(controlMode, AuthenticationConstant.CONTROL_MODE_ENT);
        // 当前组织ID是否为0,即为主数据组织
        boolean SYS_ORG_FLG = StringUtils.equals(orgId, AuthenticationConstant.SYS_ORG_ID);

        // 当前用户为超级管理员时，并且组织为主数据组织，并且当前url在排除列表。不执行拦截
        if (rootFlag && SYS_ORG_FLG && excludeUrlFlag) {
            return executor.query(ms, parameter, rowBounds, resultHandler, cacheKey, boundSql);
        }
        // 当前管控模式为NONE,URL 不 在feature下。不执行拦截
        if (MODE_NON_FLG && !featureFlag) {
            return executor.query(ms, parameter, rowBounds, resultHandler, cacheKey, boundSql);
        }

        // 当前管控模式为ORGANIZATION, 只拼接ORG_ID
        if (MODE_ORG_FLG) {
            precessOrgId(orgIdTables, boundSql);
            return executor.query(ms, parameter, rowBounds, resultHandler, cacheKey, boundSql);
        }

        //  (当前管控模式为NONE,URL 在feature下)。则只拼接ORG_ID=0
        if (MODE_NON_FLG) {
            precessOrgId(orgIdTables, boundSql, "0");
            return executor.query(ms, parameter, rowBounds, resultHandler, cacheKey, boundSql);
        }

        // 当前管控模式为ENTITY时则拼接 ORG_ID和控制对象值
        if (MODE_ENT_FLG) {
            precessDomain(domainTables, boundSql);
            precessOrgId(orgIdTables, boundSql);
            return executor.query(ms, parameter, rowBounds, resultHandler, cacheKey, boundSql);
        }

        return executor.query(ms, parameter, rowBounds, resultHandler, cacheKey, boundSql);
    }

    private boolean isMatchExclude(String uri, List<String> excludeResources) {
        PathMatcher matcher = new AntPathMatcher();
        if (CollectionUtils.isNotEmpty(excludeResources)) {
            for (String pattern : excludeResources) {
                if (matcher.match(pattern, uri)) {
                    return true;
                }
            }
        }
        return false;
    }

    public void precessDomain(List<String> tables, BoundSql boundSql) throws JSQLParserException, IllegalAccessException, NoSuchFieldException {
        if (CollectionUtils.isEmpty(tables)) {
            return;
        }
        // 一个线程只从Redis中取一次
        TokenAnalysisEntity tokenAnalysisEntity = getTokenAnalysisEntity();
        if (null == tokenAnalysisEntity) {
            return;
        }
        this.setEffectTables(tables.stream().map(String::toUpperCase).collect(Collectors.toList()));
        String controlValue = tokenAnalysisEntity.getControlValueText();
        String columnName = tokenAnalysisEntity.getMasterDataCodeColumnName();
        processBody(boundSql, controlValue, columnName);
    }

    public void precessOrgId(List<String> tables, BoundSql boundSql, String controlValue) throws JSQLParserException, IllegalAccessException, NoSuchFieldException {
        if (CollectionUtils.isEmpty(tables)) {
            return;
        }
        this.setEffectTables(tables.stream().map(String::toUpperCase).collect(Collectors.toList()));
        String columnName = AuthenticationConstant.ORG_ID_COLUMN_NAME;
        processBody(boundSql, controlValue, columnName);
    }

    public void precessOrgId(List<String> tables, BoundSql boundSql) throws JSQLParserException, IllegalAccessException, NoSuchFieldException {
        if (CollectionUtils.isEmpty(tables)) {
            return;
        }
        TokenAnalysisEntity tokenAnalysisEntity = getTokenAnalysisEntity();
        if (null == tokenAnalysisEntity) {
            return;
        }
        this.setEffectTables(tables.stream().map(String::toUpperCase).collect(Collectors.toList()));
        String controlValue = String.valueOf(tokenAnalysisEntity.getOrganizationIdentifier());
        String columnName = AuthenticationConstant.ORG_ID_COLUMN_NAME;
        processBody(boundSql, controlValue, columnName);
    }

    public void processBody(BoundSql boundSql, String controlValue, String columnName) throws JSQLParserException, NoSuchFieldException, IllegalAccessException {

        if (StringUtils.isNoneBlank(controlValue, columnName)) {
            Statement parse = CCJSqlParserUtil.parse(boundSql.getSql());
            processWithItemList(((Select) parse).getWithItemsList(), controlValue, columnName);
            processSelectBody(((Select) parse).getSelectBody(), controlValue, columnName);
            Field field = boundSql.getClass().getDeclaredField("sql");
            field.setAccessible(true);
            field.set(boundSql, parse.toString());
        }

    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {

    }

    public void processWithItemList(List<WithItem> withItemList, String controlValue, String columnName) {
        if (null == withItemList) {
            return;
        }
        for (WithItem withItem : withItemList) {
            processSelectBody(withItem.getSelectBody(), controlValue, columnName);
        }
    }

    public void processSelectItemList(List<SelectItem> selectItemList, String controlValue, String columnName) {
        if (null == selectItemList) {
            return;
        }
        for (SelectItem selectItem : selectItemList) {
            processSelectBody((SelectBody) selectItem, controlValue, columnName);
        }
    }

    //select 语句处理
    public void processSelectBody(SelectBody selectBody, String controlValue, String columnName) {
        if (selectBody instanceof PlainSelect) {
            processPlainSelect((PlainSelect) selectBody, controlValue, columnName);
        } else if (selectBody instanceof WithItem) {
            WithItem withItem = (WithItem) selectBody;
            if (withItem.getSelectBody() != null) {
                processSelectBody(withItem.getSelectBody(), controlValue, columnName);
            }
        } else {
            SetOperationList operationList = (SetOperationList) selectBody;
            if (operationList.getSelects() != null && operationList.getSelects().size() > 0) {
                operationList.getSelects().forEach(it -> processSelectBody(it, controlValue, columnName));
            }
        }
    }

    //处理 PlainSelect, 查询结果列表中的子查询也需要处理
    protected void processPlainSelect(PlainSelect plainSelect, String controlValue, String columnName) {
        List<SelectItem> selectItems = plainSelect.getSelectItems();
        if (selectItems != null && selectItems.size() > 0) {
            selectItems.forEach(j -> processSelectItem(j, controlValue, columnName));
        }
        FromItem fromItem = plainSelect.getFromItem();
        if (fromItem instanceof Table) {
            Table fromTable = (Table) fromItem;
            plainSelect.setWhere(builderExpression(plainSelect.getWhere(), fromTable, controlValue, columnName));
        } else {
            processFromItem(fromItem, controlValue, columnName);
        }
        List<Join> joins = plainSelect.getJoins();
        if (joins != null && joins.size() > 0) {
            joins.forEach(j -> {
                processJoin(j, controlValue, columnName, plainSelect);
                processFromItem(j.getRightItem(), controlValue, columnName);
            });
        }
        Expression where = plainSelect.getWhere();
        processWhere(where, controlValue, columnName);
        GroupByElement groupByElement = plainSelect.getGroupBy();
        processGroupBy(groupByElement, controlValue, columnName);
        List<OrderByElement> orderByElements = plainSelect.getOrderByElements();
        processOrderBy(orderByElements, controlValue, columnName);

    }

    protected void processWhere(Expression where, String controlValue, String columnName) {
        doExpression(where, controlValue, columnName);
    }

    // 处理查询的结果集
    protected void processSelectItem(SelectItem selectItem, String controlValue, String columnName) {
        if (selectItem instanceof SelectExpressionItem) {
            SelectExpressionItem selectExpressionItem = (SelectExpressionItem) selectItem;
            Expression expression = selectExpressionItem.getExpression();
            doExpression(expression, controlValue, columnName);
        }
    }

    // 处理GroupBy
    protected void processGroupBy(GroupByElement groupByElement, String controlValue, String columnName) {
        if (null == groupByElement) {
            return;
        }
        List<Expression> groupByExpressions = groupByElement.getGroupByExpressions();
        if (CollectionUtils.isEmpty(groupByExpressions)) {
            return;
        }
        for (Expression groupByExpression : groupByExpressions) {
            if (groupByExpression instanceof SubSelect) {
                SubSelect subSelect = (SubSelect) groupByExpression;
                if (subSelect.getSelectBody() != null) {
                    processSelectBody(subSelect.getSelectBody(), controlValue, columnName);
                }
            }
        }
    }

    // 处理OrderBy
    protected void processOrderBy(List<OrderByElement> orderByElements, String controlValue, String columnName) {
        if (CollectionUtils.isEmpty(orderByElements)) {
            return;
        }
        for (OrderByElement orderByElement : orderByElements) {
            Expression orderByExpression = orderByElement.getExpression();
            if (orderByExpression instanceof SubSelect) {
                SubSelect subSelect = (SubSelect) orderByExpression;
                if (subSelect.getSelectBody() != null) {
                    processSelectBody(subSelect.getSelectBody(), controlValue, columnName);
                }
            }
        }
    }

    //处理子查询等
    protected void processFromItem(FromItem fromItem, String controlValue, String columnName) {
        if (fromItem instanceof SubJoin) {
            SubJoin subJoin = (SubJoin) fromItem;
            if (subJoin.getJoinList() != null) {
                subJoin.getJoinList().forEach(it -> processJoin(it, controlValue, columnName));
            }
            if (subJoin.getLeft() != null) {
                processFromItem(subJoin.getLeft(), controlValue, columnName);
            }
        } else if (fromItem instanceof SubSelect) {
            SubSelect subSelect = (SubSelect) fromItem;
            if (subSelect.getSelectBody() != null) {
                processSelectBody(subSelect.getSelectBody(), controlValue, columnName);
            }
        } else if (fromItem instanceof ValuesList) {
            log.debug("Perform a subquery, if you do not give us feedback");
        } else if (fromItem instanceof LateralSubSelect) {
            LateralSubSelect lateralSubSelect = (LateralSubSelect) fromItem;
            if (lateralSubSelect.getSubSelect() != null) {
                SubSelect subSelect = lateralSubSelect.getSubSelect();
                if (subSelect.getSelectBody() != null) {
                    processSelectBody(subSelect.getSelectBody(), controlValue, columnName);
                }
            }
        }
    }

    //处理联接语句
    //Sample join时将条件加到where中
    protected void processJoin(Join join, String controlValue, String columnName, PlainSelect plainSelect) {
        if (join.getRightItem() instanceof Table) {
            Table fromTable = (Table) join.getRightItem();
            if (join.isSimple()) {
                plainSelect.setWhere(builderExpression(plainSelect.getWhere(), fromTable, controlValue, columnName));
            } else {
                join.setOnExpression(builderExpression(join.getOnExpression(), fromTable, controlValue, columnName));
            }
        }
    }

    protected void processJoin(Join join, String controlValue, String columnName) {
        if (join.getRightItem() instanceof Table) {
            Table fromTable = (Table) join.getRightItem();
            join.setOnExpression(builderExpression(join.getOnExpression(), fromTable, controlValue, columnName));
        }
    }

    //处理条件:
    //创建InExpression，拼接自定义的数据域字段在where条件中
    // 当前表不在tables中，需要检查，in条件中的子查询的表
    protected Expression builderExpression(Expression currentExpression, Table table, String controlValue, String columnName) {
        List<String> effectTables = getEffectTables();
        if (null == effectTables || !effectTables.contains(table.getName().toUpperCase())) {
            if (currentExpression instanceof InExpression) {
                InExpression inExp = (InExpression) currentExpression;
                ItemsList rightItems = inExp.getRightItemsList();
                if (rightItems instanceof SubSelect) {
                    processSelectBody(((SubSelect) rightItems).getSelectBody(), controlValue, columnName);
                }
            }
            return currentExpression;
        }
        // 构造只包含entity的in语句条件
        InExpression onlyEntity = new InExpression();
        List<Expression> onlyEntityExpressions = new ArrayList<>();
        onlyEntityExpressions.add(new StringValue(controlValue));

        ExpressionList expressionList = new ExpressionList(onlyEntityExpressions);
        onlyEntity.setLeftExpression(getAliasColumn(table, columnName));
        onlyEntity.setRightItemsList(expressionList);
        final Parenthesis totalExpression;
        totalExpression = new Parenthesis(onlyEntity);
        Expression appendExpression;
        if (currentExpression == null) {
            return totalExpression;
        } else {
            appendExpression = totalExpression;
        }
        if (currentExpression instanceof BinaryExpression) {
            BinaryExpression binaryExpression = (BinaryExpression) currentExpression;
            doExpression(binaryExpression.getLeftExpression(), controlValue, columnName);
            doExpression(binaryExpression.getRightExpression(), controlValue, columnName);
        } else if (currentExpression instanceof InExpression) {
            InExpression inExp = (InExpression) currentExpression;
            ItemsList rightItems = inExp.getRightItemsList();
            if (rightItems instanceof SubSelect) {
                processSelectBody(((SubSelect) rightItems).getSelectBody(), controlValue, columnName);
            }
        }
        if (currentExpression instanceof OrExpression) {
            return new AndExpression(new Parenthesis(currentExpression), appendExpression);
        } else {
            return new AndExpression(currentExpression, appendExpression);
        }
    }

    protected void doExpression(Expression expression, String controlValue, String columnName) {
        if (expression instanceof FromItem) {
            processFromItem((FromItem) expression, controlValue, columnName);
        } else if (expression instanceof InExpression) {
            InExpression inExp = (InExpression) expression;
            ItemsList rightItems = inExp.getRightItemsList();
            if (rightItems instanceof SubSelect) {
                processSelectBody(((SubSelect) rightItems).getSelectBody(), controlValue, columnName);
            }
        } else if (expression instanceof OrExpression) {
            OrExpression orExp = (OrExpression) expression;
            Expression leftExpression = orExp.getLeftExpression();
            Expression rightExpression = orExp.getRightExpression();
            doExpression(leftExpression, controlValue, columnName);
            doExpression(rightExpression, controlValue, columnName);
        } else if (expression instanceof CaseExpression) {
            CaseExpression caseExp = (CaseExpression) expression;
            Expression switchExpression = caseExp.getSwitchExpression();
            if (switchExpression instanceof SubSelect) {
                processSelectBody(((SubSelect) switchExpression).getSelectBody(), controlValue, columnName);
            }
            Expression elseExpression = caseExp.getElseExpression();
            if (elseExpression instanceof SubSelect) {
                processSelectBody(((SubSelect) elseExpression).getSelectBody(), controlValue, columnName);
            }
            List<WhenClause> whenClauses = caseExp.getWhenClauses();
            if (CollectionUtils.isEmpty(whenClauses)) {
                return;
            }
            for (WhenClause when : whenClauses) {
                Expression whenExpression = when.getWhenExpression();
                if (whenExpression instanceof BinaryExpression) {
                    BinaryExpression binaryExpression = (BinaryExpression) whenExpression;
                    doExpression(binaryExpression.getLeftExpression(), controlValue, columnName);
                    doExpression(binaryExpression.getRightExpression(), controlValue, columnName);
                }
            }
        } else if (expression instanceof AndExpression) {
            AndExpression andExp = (AndExpression) expression;
            Expression leftExpression = andExp.getLeftExpression();
            Expression rightExpression = andExp.getRightExpression();
            doExpression(leftExpression, controlValue, columnName);
            doExpression(rightExpression, controlValue, columnName);
        } else if (expression instanceof NotExpression) {
            NotExpression notExp = (NotExpression) expression;
            Expression subNotExp = notExp.getExpression();
            doExpression(subNotExp, controlValue, columnName);
        } else if (expression instanceof ExistsExpression) {
            ExistsExpression existsExp = (ExistsExpression) expression;
            Expression rightExp = existsExp.getRightExpression();
            doExpression(rightExp, controlValue, columnName);
        } else if (expression instanceof InExpression) {
            InExpression inExp = (InExpression) expression;
            Expression leftExp = inExp.getLeftExpression();
            ItemsList leftItems = inExp.getLeftItemsList();
            Expression rightExp = inExp.getRightExpression();
            ItemsList rightItems = inExp.getRightItemsList();
            MultiExpressionList multiExp = inExp.getMultiExpressionList();
            doExpression(leftExp, controlValue, columnName);
            doExpression(rightExp, controlValue, columnName);
            doItemsList(leftItems, controlValue, columnName);
            doItemsList(rightItems, controlValue, columnName);
            doMultiExpressionList(multiExp, controlValue, columnName);
        } else if (expression instanceof EqualsTo) {
            EqualsTo eqExp = (EqualsTo) expression;
            Expression leftExp = eqExp.getLeftExpression();
            Expression rightExp = eqExp.getRightExpression();
            doExpression(leftExp, controlValue, columnName);
            doExpression(rightExp, controlValue, columnName);
        }
    }

    protected void doItemsList(ItemsList itemsList, String controlValue, String columnName) {
        if (itemsList instanceof SubSelect) {
            processSelectBody(((SubSelect) itemsList).getSelectBody(), controlValue, columnName);
        }
    }

    protected void doMultiExpressionList(MultiExpressionList multiExp, String controlValue, String columnName) {
        List<ExpressionList> expressionLists = multiExp.getExprList();
        if (CollectionUtils.isNotEmpty(expressionLists)) {
            for (ExpressionList expressionList : expressionLists) {
                List<Expression> expressions = expressionList.getExpressions();
                if (CollectionUtils.isNotEmpty(expressions)) {
                    expressions.forEach(x -> doExpression(x, controlValue, columnName));
                }
            }
        }
    }

    protected Column getAliasColumn(Table table, String columnName) {
        StringBuilder column = new StringBuilder();
        String alias = null == table.getAlias() ? table.getName() : table.getAlias().getName();
        column.append(alias)
                .append(".")
                .append(columnName);
        return new Column(column.toString());
    }

    protected TokenAnalysisEntity getTokenAnalysisEntity() {
        TokenAnalysisEntity tokenAnalysisEntity = (TokenAnalysisEntity) DomainParamUtils.getParam("tokenAnalysisEntity");
        if (null == tokenAnalysisEntity) {
            tokenAnalysisEntity = tokenAnalysisService.getTokenAnalysisEntity();
            if (null == tokenAnalysisEntity) {
                return null;
            } else {
                DomainParamUtils.putParam("tokenAnalysisEntity", tokenAnalysisEntity);
            }
        }
        return tokenAnalysisEntity;
    }

    protected List<String> getDomainTables() {
        List<String> domainTables = (List<String>) DomainParamUtils.getParam("domainTables");
        if (null == domainTables) {
            TokenAnalysisEntity tokenAnalysisEntity = getTokenAnalysisEntity();
            domainTables = dataSourceService.getAllTableNameContainColumn(tokenAnalysisEntity.getMasterDataCodeColumnName());
            DomainParamUtils.putParam("domainTables", domainTables);
        }
        return domainTables;
    }

    protected List<String> getOrgIdTables() {
        List<String> orgIdTables = (List<String>) DomainParamUtils.getParam("orgIdTables");
        if (null == orgIdTables) {
            orgIdTables = dataSourceService.getTableContainOrganization();
            DomainParamUtils.putParam("orgIdTables", orgIdTables);
        }
        return orgIdTables;
    }

    protected List<String> getEffectTables() {
        List<String> effectTables = (List<String>) DomainParamUtils.getParam("effectTables");
        return effectTables;
    }

    protected void setEffectTables(List<String> tables) {
        if (null != tables) {
            DomainParamUtils.putParam("effectTables", tables);
        }
    }
}
