package cec.jiutian.core.intercepter;

import cec.jiutian.core.util.DomainParamUtils;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import java.util.Enumeration;

/**
 * @description: 功能说明（jira task id）
 * @author: chenjx
 * @date: 20220621
 */
@Configuration
@Slf4j
public class FeignInterceptor {

    //定义拦截每次发送feign调用拦截器RequestInterceptor的子类，每次发送feign请求前将token带入请求头
    @Bean
    public RequestInterceptor requestInterceptor() {
        return new RequestInterceptor() {
            @Override
            public void apply(RequestTemplate template) {
                // 获取Request对象
                ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
                if(null!= requestAttributes) {
                    HttpServletRequest request = requestAttributes.getRequest();
                    // 获取请求头
                    Enumeration<String> headerNames = request.getHeaderNames();
                    while (headerNames.hasMoreElements()) {
                        String key = headerNames.nextElement();
                        // 添加请求头 content-length 后，会导致post类型请求循环调用，报错
                        if (!"content-length".equals(key)) {
                            String value = request.getHeader(key);
                            // 在Feign调用的时候手动的把请求头的信息添加到Feign的请求对象中
                            template.header(key, value);
                        }
                    }
                }
                // 添加token
                String token = (DomainParamUtils.getToken());
                log.info( "RequestInterceptor-token:  " +token );
                template.header("Authorization", DomainParamUtils.getToken());
            }
        };
    }
}
