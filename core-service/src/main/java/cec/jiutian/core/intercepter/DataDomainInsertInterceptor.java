package cec.jiutian.core.intercepter;

import cec.jiutian.core.comn.constant.AuthenticationConstant;
import cec.jiutian.core.comn.util.StringUtils;
import cec.jiutian.core.config.DataDomainEnv;
import cec.jiutian.core.entity.TokenAnalysisEntity;
import cec.jiutian.core.service.DataSourceService;
import cec.jiutian.core.service.TokenAnalysisService;
import cec.jiutian.core.util.DomainParamUtils;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.expression.StringValue;
import net.sf.jsqlparser.expression.operators.relational.ExpressionList;
import net.sf.jsqlparser.expression.operators.relational.MultiExpressionList;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.schema.Column;
import net.sf.jsqlparser.schema.Table;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.insert.Insert;
import org.apache.commons.collections.CollectionUtils;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Plugin;
import org.apache.ibatis.plugin.Signature;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.reflection.SystemMetaObject;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.stream.Collectors;

@SuppressWarnings({"rawtypes"})
@Slf4j
@Intercepts({
        @Signature(type = StatementHandler.class, method = "prepare", args = {java.sql.Connection.class, Integer.class})
})

//@Component
public class DataDomainInsertInterceptor implements Interceptor {
/*    @Value("${data.domain.valid.flag:Y}")
    protected String dataDomainValidFlag;*/

    private final DataDomainEnv env;
//    protected TokenAnalysisEntity tokenAnalysisEntity;
//
//    List<String> effectTables;

    private final TokenAnalysisService tokenAnalysisService;
    private final DataSourceService dataSourceService;

    public DataDomainInsertInterceptor(DataDomainEnv env
            , TokenAnalysisService tokenAnalysisService
            , DataSourceService dataSourceService) {
        this.env = env;
        this.tokenAnalysisService = tokenAnalysisService;
        this.dataSourceService = dataSourceService;
    }

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        // 本地测试不做拦截
        if ("N".equals(env.dataDomainValidFlag)) {
            return invocation.proceed();
        }

        Object target = invocation.getTarget();
        MetaObject metaObject = SystemMetaObject.forObject(target);
        //分离代理对象链
        while (metaObject.hasGetter("h")) {
            Object obj = metaObject.getValue("h");
            metaObject = SystemMetaObject.forObject(obj);
        }

        while (metaObject.hasGetter("target")) {
            Object obj = metaObject.getValue("target");
            metaObject = SystemMetaObject.forObject(obj);
        }
        MappedStatement ms = (MappedStatement) metaObject.getValue("delegate.parameterHandler.mappedStatement");
        SqlCommandType sqlCommandType = ms.getSqlCommandType();
        // 非insert语句不处理
        if (!SqlCommandType.INSERT.equals(sqlCommandType)) {
            return invocation.proceed();
        }
        // 登录专用的接口不做拦截
        if (null != env.loginExcludeUris && Arrays.asList(env.loginExcludeUris).contains(DomainParamUtils.getUri())) {
            return invocation.proceed();
        }
        //检查token  一个线程只检查一次
        if (!"Y".equals(DomainParamUtils.getParam("checkTokenFlag"))) {
            tokenAnalysisService.checkToken();
            DomainParamUtils.putParam("checkTokenFlag", "Y");
        }
        // 一个线程只从Redis中取一次
        TokenAnalysisEntity tokenAnalysisEntity = getTokenAnalysisEntity();
        if (null == tokenAnalysisEntity) {
            return invocation.proceed();
        }

        String controlMode = tokenAnalysisEntity.getControlMode();
        String orgId = tokenAnalysisEntity.getOrganizationIdentifier();

        // 用户是否为超级管理员
        boolean rootFlag = null != tokenAnalysisEntity && AuthenticationConstant.ROOT_NAME.equals(tokenAnalysisEntity.getUserId());
        // 当前URL是否为排除URL
        boolean excludeUrlFlag = null != env.excludeUris && Arrays.asList(env.excludeUris).contains(DomainParamUtils.getUri());
        // 当前组织ID是否为0,即为主数据组织
        boolean SYS_ORG_FLG = StringUtils.equals(orgId, AuthenticationConstant.SYS_ORG_ID);


        // 当前管控模式是否为ORGANIZATION
        Boolean MODE_ORG_FLG = StringUtils.equals(controlMode, AuthenticationConstant.CONTROL_MODE_ORG);
        // 当前管控模式是否为ENTITY
        Boolean MODE_ENT_FLG = StringUtils.equals(controlMode, AuthenticationConstant.CONTROL_MODE_ENT);
        // 当前用户为超级管理员时，并且组织为主数据组织，并且当前url在排除列表。不执行拦截
        if (rootFlag && SYS_ORG_FLG && excludeUrlFlag) {
            return invocation.proceed();
        }

        // 当管控模式为 ORGANIZATION 或 ENTITY时，才拼接
        if (!(MODE_ORG_FLG || MODE_ENT_FLG)) {
            return invocation.proceed();
        }

        return processInsert(invocation, metaObject);
    }

    /*
     * @Description //处理组织ID统一存储
     * case1：如果 BoundSql中有，ORG_ID，且参数Object中有 organizationIdentifier则直接设置参数的值，
     * case2: 如果 BoundSql中有，ORG_ID，参数Object中没有 organizationIdentifier，则直接跳过不处理
     * case3：如果 BoundSql中没有ORG_ID，则构造SQL添加ORG_ID字段及其的值
     **/
    private Object processInsert(Invocation invocation, MetaObject metaObject) throws InvocationTargetException, IllegalAccessException, JSQLParserException, NoSuchFieldException {
        // 一个线程只从Redis中取一次
        List<String> orgIdTables = getOrgIdTables();
        TokenAnalysisEntity tokenAnalysisEntity = getTokenAnalysisEntity();
        String controlValue = String.valueOf(tokenAnalysisEntity.getOrganizationIdentifier());
        String columnName = AuthenticationConstant.ORG_ID_COLUMN_NAME;
        if (CollectionUtils.isEmpty(orgIdTables)) {
            return invocation.proceed();
        }
        BoundSql boundSql = (BoundSql) metaObject.getValue("boundSql");
        // 获取参数值
        Object pObject = metaObject.getValue("delegate.parameterHandler.parameterObject");
        Boolean multiFlag = checkMulti(pObject);
        Boolean ORG_ID_COLUMN_FLAG = checkSqlColumn(boundSql, columnName);
        Boolean ORG_ID_FIELD_FLAG = checkObjectContainFieldName(pObject, AuthenticationConstant.ORG_ID_FIELD_NAME, multiFlag);
        // case2：如果 BoundSql中有，ORG_ID，且参数Object中有 organizationIdentifier则直接设置参数的值，
        if (ORG_ID_COLUMN_FLAG && !ORG_ID_FIELD_FLAG) {
            return invocation.proceed();
        }
        //case1：如果 BoundSql中有，ORG_ID，且参数Object中有 organizationIdentifier则直接设置参数的值，
        if (ORG_ID_COLUMN_FLAG) {
            MetaObject pObjectMeta = SystemMetaObject.forObject(pObject);
            if (multiFlag) {
                setMultiParamObject(pObject, AuthenticationConstant.ORG_ID_FIELD_NAME, controlValue);
            } else {
                pObjectMeta.setValue(AuthenticationConstant.ORG_ID_FIELD_NAME, controlValue);
            }
            return invocation.proceed();
        }
        //case3：如果 BoundSql中没有ORG_ID，则构造SQL添加ORG_ID字段及其的值
        processAddColumn(multiFlag, orgIdTables, boundSql, controlValue, columnName);
        metaObject.setValue("boundSql.sql", boundSql.getSql());
        return invocation.proceed();
    }

    private Boolean checkMulti(Object paramObj) {
        if (paramObj instanceof Map) {
            Map map = (Map) paramObj;
            return map.containsKey("list");
        }
        return false;
    }

    private void setMultiParamObject(Object paramObj, String fieldName, String controlValue) {
        Map map = (Map) paramObj;
        List<Object> listParamObj = (List<Object>) map.get("list");
        for (Object obj : listParamObj) {
            MetaObject pObjectMeta = SystemMetaObject.forObject(obj);
            pObjectMeta.setValue(fieldName, controlValue);
        }
    }

    /*
     * @Description //判断的Insert语句的字段中中是否有指定的列
     **/
    public Boolean checkSqlColumn(BoundSql boundSql, String columnName) throws JSQLParserException {
        if (StringUtils.isNoneBlank(columnName)) {
            Statement parse = CCJSqlParserUtil.parse(boundSql.getSql());
            Insert insert = (Insert) parse;
            List<Column> columns = insert.getColumns();
            List<String> columnNames = columns.stream().map(Column::getColumnName).map(String::toUpperCase).collect(Collectors.toList());
            return columnNames.contains(columnName);
        }
        return false;
    }

    /*
     * @Description //Object中是否含有指定名称属性
     **/
    public Boolean checkObjectContainFieldName(Object paramObj, String fieldName, Boolean multiFlag) throws JSQLParserException {
        if (multiFlag) {
            Map map = (Map) paramObj;
            List<Object> listParamObj = (List<Object>) map.get("list");
            for (Object obj : listParamObj) {
                List<String> fieldNames = getFieldNameList(obj.getClass());
                return CollectionUtils.isNotEmpty(fieldNames) && fieldNames.contains(fieldName);
            }
        } else {
            List<String> fieldNames = getFieldNameList(paramObj.getClass());
            return CollectionUtils.isNotEmpty(fieldNames) && fieldNames.contains(fieldName);
        }
        return false;
    }

    /*
     * @Description //当前的BoundSql中没有ORGNZN_ID字段时使用，给BoundSql中绑定ORGNZN_ID列以及值
     * @return 当原来的SQL中含有ORGNZN_ID列时返回 false, 不含返回true并添加ORGNZN_ID并绑定值
     **/
    public void processAddColumn(Boolean multiFlag, List<String> tables, BoundSql boundSql, String controlValue, String columnName) throws JSQLParserException, NoSuchFieldException, IllegalAccessException {
        this.setEffectTables(tables.stream().map(String::toUpperCase).collect(Collectors.toList()));
        if (!StringUtils.isNoneBlank(controlValue, columnName)) {
            return;
        }
        Statement parse = CCJSqlParserUtil.parse(boundSql.getSql());
        Insert insert = (Insert) parse;
        Table table = insert.getTable();
        List<String> effectTables = getEffectTables();
        if (null == effectTables || !effectTables.contains(table.getName().toUpperCase())) {
            return;
        }
        List<Column> columns = insert.getColumns();
        List<String> columnNames = columns.stream().map(Column::getColumnName).map(String::toUpperCase).collect(Collectors.toList());
        if (columnNames.contains(columnName)) {
            return;
        }
        if (multiFlag && insert.getItemsList() instanceof MultiExpressionList) {
            MultiExpressionList multiExpressionList = (MultiExpressionList) insert.getItemsList();
            List<ExpressionList> expressionListList = multiExpressionList.getExprList();
            if (CollectionUtils.isEmpty(expressionListList)) {
                return;
            }
            columns.add(new Column(columnName));
            expressionListList.forEach(X -> doExpressionList(X, controlValue));
        } else {
            ExpressionList expressionList = (ExpressionList) insert.getItemsList();
            columns.add(new Column(columnName));
            doExpressionList(expressionList, controlValue);
        }
        Field field = boundSql.getClass().getDeclaredField("sql");
        field.setAccessible(true);
        field.set(boundSql, parse.toString());
    }

    private void doExpressionList(ExpressionList expressionList, String controlValue) {
        expressionList.getExpressions().add(new StringValue(controlValue));
    }

    public static List<String> getFieldNameList(Class tClass) {
        List<String> fieldNameLists = new ArrayList<>();
        List<Field> fields = new ArrayList<>();
        Class aClass = tClass;
        while (aClass != null) {
            fields.addAll(Arrays.asList(aClass.getDeclaredFields()));
            aClass = aClass.getSuperclass();
            fieldNameLists = fields.stream().map(Field::getName).collect(Collectors.toList());
        }
        return fieldNameLists;
    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {

    }

    protected TokenAnalysisEntity getTokenAnalysisEntity(){
        TokenAnalysisEntity tokenAnalysisEntity = (TokenAnalysisEntity) DomainParamUtils.getParam("tokenAnalysisEntity");
        if (null == tokenAnalysisEntity) {
            tokenAnalysisEntity = tokenAnalysisService.getTokenAnalysisEntity();
            if (null == tokenAnalysisEntity) {
                return null;
            } else {
                DomainParamUtils.putParam("tokenAnalysisEntity", tokenAnalysisEntity);
            }
        }
        return tokenAnalysisEntity;
    }

    protected List<String> getOrgIdTables(){
        List<String> orgIdTables = (List<String>) DomainParamUtils.getParam("orgIdTables");
        if (null == orgIdTables) {
            orgIdTables = dataSourceService.getTableContainOrganization();
            DomainParamUtils.putParam("orgIdTables", orgIdTables);
        }
        return orgIdTables;
    }
    protected List<String> getEffectTables(){
        List<String> effectTables = (List<String>) DomainParamUtils.getParam("effectTables");
        return effectTables;
    }
    protected void setEffectTables(List<String> tables){
        if (null != tables) {
            DomainParamUtils.putParam("effectTables", tables);
        }
    }

}
