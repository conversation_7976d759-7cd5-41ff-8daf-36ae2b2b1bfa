package cec.jiutian.core.intercepter;

import brave.Tracer;
import cec.jiutian.security.base.dto.HistoryResultDTO;
import cec.jiutian.core.comn.util.BeanUtils;
import cec.jiutian.core.util.BaseRedisCacheUtil;
import cec.jiutian.core.util.BizParamUtils;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.ibatis.cache.CacheKey;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Plugin;
import org.apache.ibatis.plugin.Signature;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.persistence.Id;
import java.io.Serializable;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;

@SuppressWarnings({"rawtypes"})
@Slf4j
@Intercepts({
        @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class}),
        @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class, CacheKey.class, BoundSql.class}),
})
public class HistoryUpdateOldObjectInterceptor implements Interceptor {
    private final Tracer tracer;

    public HistoryUpdateOldObjectInterceptor(Tracer tracer) {
        this.tracer = tracer;
    }

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        if (tracer.currentSpan() == null) {
            return invocation.proceed();
        }
        Object[] args = invocation.getArgs();
        MappedStatement ms = (MappedStatement) args[0];
        Object parameter = args[1];
        RowBounds rowBounds = (RowBounds) args[2];
        ResultHandler resultHandler = (ResultHandler) args[3];
        Executor executor = (Executor) invocation.getTarget();
        CacheKey cacheKey;
        BoundSql boundSql;
        //由于逻辑关系，只会进入一次
        if (args.length == 4) {
            //4 个参数时
            boundSql = ms.getBoundSql(parameter);
            cacheKey = executor.createCacheKey(ms, parameter, rowBounds, boundSql);
        } else {
            //6 个参数时
            cacheKey = (CacheKey) args[4];
            boundSql = (BoundSql) args[5];
        }
        String sql = boundSql.getSql().toUpperCase();
        Boolean exist = sql.contains("FOR UPDATE");
        List<Object> resultList = executor.query(ms, parameter, rowBounds, resultHandler, cacheKey, boundSql);
        if (CollectionUtils.isNotEmpty(resultList) && exist) {
            String tableName = sql.substring(sql.indexOf("FROM") + 4, sql.indexOf("WHERE")).trim();
            JSONObject hsDfObject = (JSONObject) BaseRedisCacheUtil.get(("HS" + "_" + tableName).toLowerCase());
            if (hsDfObject != null) {
                List<HistoryResultDTO> existedHistoryResultDTOList = (List<HistoryResultDTO>) BaseRedisCacheUtil.get(tracer.currentSpan().context().traceIdString() + "_" + TransactionSynchronizationManager.getCurrentTransactionName() + "_" + BizParamUtils.getLastEventTime());
                List<HistoryResultDTO> historyResultDTOList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(existedHistoryResultDTOList)) {
                    historyResultDTOList = existedHistoryResultDTOList;
                }
                if (CollectionUtils.isNotEmpty(resultList)) {
                    for (int i = 0; i < resultList.size(); i++) {
                        HistoryResultDTO historyResultDTO = new HistoryResultDTO();
                        Object object = resultList.get(i);
                        JSONObject jsonObject = (JSONObject) JSONObject.toJSON(object);
                        if (hsDfObject.get("excludeFieldText") != null) {
                            String[] excludeFieldTextList = hsDfObject.get("excludeFieldText").toString().split(",");
                            if (excludeFieldTextList.length > 0 && excludeFieldTextList != null) {
                                for (int n = 0; n < excludeFieldTextList.length; n++) {
                                    jsonObject.remove(excludeFieldTextList[n].trim());
                                }
                            }
                        }
                        StringBuilder stringBuilder = new StringBuilder();
                        String contentText = jsonObject.toJSONStringWithDateFormat(jsonObject, "yyyy-MM-dd HH:mm:ss:SSS", SerializerFeature.WriteDateUseDateFormat);
                        List<String> primKeyList = new ArrayList<>();
                        List<Field> fields = BeanUtils.getAllFields(object.getClass());
                        for (Field field : fields) {
                            if (field.isAnnotationPresent(Id.class)) {
                                primKeyList.add(field.getName());
                            }
                        }
                        for (int k = 0; k < primKeyList.size(); k++) {
                            String primKey = primKeyList.get(k);
                            Object keyValue = jsonObject.get(primKey);
                            stringBuilder.append(primKey + "_" + keyValue);
                            if (k != primKeyList.size() - 1) {
                                stringBuilder.append(",");
                            }
                        }
                        log.info("表名:" + tableName + "," + "主键及其值:" + stringBuilder.toString() + "," + "traceId:" + tracer.currentSpan().context().traceIdString());
                        historyResultDTO.setOldTableName(tableName);
                        historyResultDTO.setOldPrimaryKeyAndValue(stringBuilder.toString());
                        historyResultDTO.setOldContentText(contentText);
                        historyResultDTO.setHistoryDataSetDefinitionIdentifier(Long.valueOf(hsDfObject.get("identifier").toString()));
                        historyResultDTO.setDataSetUUID(hsDfObject.get("dataSetUUID").toString());
                        historyResultDTO.setDataSetName(hsDfObject.get("dataSetName").toString());
                        historyResultDTO.setLevelNumber(Long.valueOf(hsDfObject.get("levelNumber").toString()));
                        historyResultDTOList.add(historyResultDTO);
                    }
                }
                BaseRedisCacheUtil.set(tracer.currentSpan().context().traceIdString() + "_" + TransactionSynchronizationManager.getCurrentTransactionName() + "_" + BizParamUtils.getLastEventTime(), (Serializable) historyResultDTOList);
            }
        }

        return resultList;
    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {

    }
}
