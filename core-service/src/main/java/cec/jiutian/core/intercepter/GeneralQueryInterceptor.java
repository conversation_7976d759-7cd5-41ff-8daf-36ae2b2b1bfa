package cec.jiutian.core.intercepter;

import cec.jiutian.security.base.dto.BaseQueryDTO;
import cec.jiutian.security.base.dto.BaseQueryDTO.GeneralQueryParam;
import cec.jiutian.core.comn.util.StringUtils;
import cec.jiutian.core.util.SpringContextUtils;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.BoundSqlInterceptor;
import com.github.pagehelper.util.ExecutorUtil;
import com.github.pagehelper.util.MetaObjectUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.ibatis.cache.CacheKey;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.ResultMap;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.session.Configuration;
import org.apache.ibatis.session.SqlSessionFactory;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;

/*
 * <AUTHOR>
 * 通用条件查询，基于pageHelper的{@link BoundSqlInterceptor}，在原始SQL的最外层增加前端输入的查询条件，根据Mybatis的Result拼接SQL
 * @Date 15:02 2022-4-20
 **/
@SuppressWarnings("unchecked")
public class GeneralQueryInterceptor implements BoundSqlInterceptor {
    private static final Configuration CONFIGURATION = SpringContextUtils.getBean(SqlSessionFactory.class).getConfiguration();

    private static final GeneralQueryInterceptor GENERAL_QUERY_INTERCEPTOR = new GeneralQueryInterceptor();

    public static GeneralQueryInterceptor getInstance() {
        return GENERAL_QUERY_INTERCEPTOR;
    }

    @Override
    public BoundSql boundSql(Type type, BoundSql boundSql, CacheKey cacheKey, Chain chain) {
        Object paramObj = boundSql.getParameterObject();
        if (type == Type.ORIGINAL && paramObj instanceof BaseQueryDTO) {
            checkParam(paramObj);
            String generalQueryCon = ((BaseQueryDTO) paramObj).getGeneralQueryCondition();
            if (StringUtils.isNotEmpty(generalQueryCon)) {
                generalQueryCon = generalQueryCon.trim();
                // 需要从cacheKey中取出这条SQL的ID
                MetaObject cache = MetaObjectUtil.forObject(cacheKey);
                List<Object> updateList = (List<Object>) cache.getValue("updateList");
                String sqlId = StringUtils.substringAfterLast(String.valueOf(updateList.get(0)), ".");
                // 此处只应对一个SQL对应一个ResultMap的情况，若出现有多个ResultMap再进行完善
                ResultMap resultMap = ExecutorUtil.getExistedMappedStatement(CONFIGURATION, sqlId).getResultMaps().get(0);
                // 根据resultMap和查询参数中的sort筛选和匹配数据库真实的字段名，可避免SQL注入
                List<GeneralQueryParam> queryParams = analyzeQueryParam(generalQueryCon, resultMap);
                if (CollectionUtils.isNotEmpty(queryParams)) {
                    MetaObject metaObject = MetaObjectUtil.forObject(boundSql);
                    metaObject.setValue("sql", getGeneralQuerySql(queryParams, boundSql.getSql()));
                }
            }
        }
        return boundSql;
    }

    /*
     * <AUTHOR>
     * @Description //检查基本查询条件和通用查询条件中的 是否有相同的属性，有则抛错
     * @Date 16:06 2022-4-22
     **/
    private void checkParam(Object obj) {
        BaseQueryDTO queryDTO = (BaseQueryDTO) obj;
        String generalQueryCon = queryDTO.getGeneralQueryCondition();
        if (null == generalQueryCon) {
            return;
        }
        JSONObject job = JSONObject.parseObject(JSONObject.toJSON(obj).toString());  //所有null的属性都不要
        Set<String> paramFieldNames = job.keySet();
        List<String> params = Arrays.asList(generalQueryCon.split(","));
        params.forEach(x -> {
            String paramName = x.split("=")[0];
            if (paramFieldNames.contains(paramName)) {
                throw new RuntimeException("param: {" + paramName + "} has select in drop-down box, can not set in general condition");
            }
        });
    }

    private List<GeneralQueryParam> analyzeQueryParam(String generalQueryCon, ResultMap resultMap) {
        List<GeneralQueryParam> generalQueryParams = new ArrayList<>();
        List<String> params = Arrays.asList(generalQueryCon.split(","));
        if (CollectionUtils.isEmpty(params)) {
            return generalQueryParams;
        }
        List<GeneralQueryParam> actualParams = new ArrayList<>();
        params.forEach(x -> {
            if (x.split("=").length > 1) {
                generalQueryParams.add(new GeneralQueryParam(x.split("=")[0], "", x.split("=")[1]));
            }
        });
        generalQueryParams.stream()
                .forEach(param -> resultMap.getResultMappings()
                        .stream()
                        .filter(it -> StringUtils.equals(it.getProperty(), param.getField()))
                        .findFirst()
                        .ifPresent(resultMapping -> actualParams.add(new GeneralQueryParam(resultMapping.getColumn(), resultMapping.getJdbcType().name(), param.getValue()))));
        return actualParams;

    }

    private String getGeneralQuerySql(List<GeneralQueryParam> params, String sql) {
        StringBuilder sqlBuilder = new StringBuilder(sql.length() + 400);
        sqlBuilder.append("SELECT * FROM ( ");
        sqlBuilder.append(sql);
        sqlBuilder.append(" \n ) TMP_FEN WHERE 1=1 ");
        params.forEach(queryParam -> sqlBuilder.append(generateConditionSql(queryParam)));
        return sqlBuilder.toString();
    }

    private String generateConditionSql(GeneralQueryParam queryParam) {
        StringBuilder sqlBuilder = new StringBuilder(100);
        if (Arrays.asList(JdbcTypes.charTypes).contains(queryParam.getJdbcType())) {
            sqlBuilder.append(" AND  ").append(queryParam.getField())
                    .append(" LIKE ")
                    .append("'%")
                    .append(queryParam.getValue()).append("%'")
                    .append(" \n");
        } else if (Arrays.asList(JdbcTypes.numberTypes).contains(queryParam.getJdbcType())) {
            sqlBuilder.append(" AND  ").append(queryParam.getField())
                    .append(" = ")
                    .append(queryParam.getValue())
                    .append(" \n");
        } else if (Arrays.asList(JdbcTypes.timeTypes).contains(queryParam.getJdbcType())) {
            sqlBuilder.append(" AND  ")
                    .append(" DATE_FORMAT(")
                    .append(queryParam.getField())
                    .append(",'%Y-%m-%d' ")
                    .append(")")
                    .append("='")
                    .append(formatDate(queryParam.getValue(), "yyyy-MM-dd"))
                    .append("'")
                    .append(" \n");
        }

        return sqlBuilder.toString();
    }

    public String formatDate(String strDate, String format) {
        Boolean formatFlag = false;
        if (!StringUtils.isNoneBlank(strDate, strDate)) {
            formatFlag = true;
        }
        if (strDate.length() < format.length()) {
            formatFlag = true;
        }
        DateTimeFormatter dateTimeFormat = DateTimeFormat.forPattern(format);
        strDate = strDate.substring(0, format.length());
        try {
            DateTime.parse(strDate, dateTimeFormat);
        } catch (Exception e) {
            formatFlag = true;
        }
        if (formatFlag) {
            throw new RuntimeException("DateFormat need to be " + format);
        }
        return strDate;
    }

    static class JdbcTypes {
        public static String[] numberTypes = {
                "NUMERIC"
                , "DECIMAL"
                , "BIT"
                , "TINYINT"
                , "SMALLINT"
                , "INTEGER"
                , "BIGINT"
                , "REAL"
                , "FLOAT"
                , "DOUBLE"
        };
        public static String[] charTypes = {
                "CHAR"
                , "VARCHAR"
                , "LONGVARCHAR"
        };

        public static String[] timeTypes = {
                "DATE"
                , "TIME"
                , "TIMESTAMP"
        };
    }

}
