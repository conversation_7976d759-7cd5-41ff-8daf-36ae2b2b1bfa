package cec.jiutian.core.service;

import cec.jiutian.core.comn.constant.ErrorCodeConstant;
import cec.jiutian.core.errorCode.CoreErrorCode;
import cec.jiutian.core.util.BaseRedisCacheUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Component
public class CoreErrorCodeService {

    /**
     *  * @description: 直接从Redis中获取ErrorCode
     *  
     *  * @version: 1.0
     *  * @date: 2019 2019年9月2日 下午7:14:23
     *  * @param errorCode
     *  * @return
     */
    public CoreErrorCode getCoreErrorCodeByCode(String errorCode) {
        if (StringUtils.isEmpty(errorCode)) {
            return null;
        }
        try {
            CoreErrorCode coreErrorCode = (CoreErrorCode) BaseRedisCacheUtil.get(ErrorCodeConstant.REDIS_PREFIX + errorCode);
            return coreErrorCode;

        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
}
