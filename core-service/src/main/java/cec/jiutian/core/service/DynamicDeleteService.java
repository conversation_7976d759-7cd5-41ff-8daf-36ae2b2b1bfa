package cec.jiutian.core.service;

import cec.jiutian.core.mapper.DynamicDeleteMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
@Service
public class DynamicDeleteService implements IDynamicDeleteService {

    @Autowired
    private DynamicDeleteMapper dynamicDeleteMapper;

    @PostMapping("/dynamicDelete/deleteHistTblData")
    @Override
    public int deleteHistTblData(@RequestParam int type, @RequestParam String tblName, @RequestParam int purgeRuleDayNumber, @RequestParam String deleteFieldName) {
        StringBuilder sb = new StringBuilder();
        sb.append("DELETE FROM ").append(tblName).append(" WHERE ");
        if (type == 1) {
            sb.append("DATE_SUB(CURDATE(), INTERVAL ").append(purgeRuleDayNumber).append(" DAY) > date(");
            sb.append(deleteFieldName).append(")");
        } else if (type == 2) {
            sb.append("to_char(").append(deleteFieldName).append(", 'yy-mm-dd') < to_char(sysdate+1-");
            sb.append(purgeRuleDayNumber).append(",'yy-mm-dd')");
        }
        return dynamicDeleteMapper.dynamicDelete(sb.toString());
    }

}
