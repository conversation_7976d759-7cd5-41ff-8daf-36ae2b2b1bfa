package cec.jiutian.core.service;

import cec.jiutian.core.base.BaseService;
import cec.jiutian.core.comn.util.StringUtils;
import cec.jiutian.core.entity.MasterDataStage;
import cec.jiutian.core.util.JsonMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/11/12
 */
@Slf4j
@Service
@Transactional
public class MasterDataStageService extends BaseService<MasterDataStage, String> {
    private static final JsonMapper JSON_MAPPER = new JsonMapper();

    /**
     * 主数据实际修改时调用
     *
     * @param o    更新DTO，代表修改后的数据
     * @param uuid 签出时生成的uuid
     * @return 临时数据的创建结果
     */
    public Boolean create(Object o, String uuid) {
        if (o != null && StringUtils.isNotBlank(uuid)) {
            deleteById(uuid);
            MasterDataStage masterDataStage = new MasterDataStage();
            masterDataStage.setUuid(uuid);
            masterDataStage.setContentText(JSON_MAPPER.toJson(o));
            return save(masterDataStage) == 1;
        }
        return true;
    }

    /**
     * 主数据签入时调用，为更新DTO赋值
     *
     * @param uuid    签出时生成的uuid
     * @param dtoType 更新DTO的类型
     * @param <T>     更新DTO的类型
     * @return 更新DTO，与原接口所有的DTO一致
     */
    public <T> T setValue(String uuid, Class<T> dtoType) {
        if (StringUtils.isNotBlank(uuid)) {
            MasterDataStage masterDataStage = getById(uuid);
            if (masterDataStage != null) {
                deleteById(uuid);
                return JSON_MAPPER.fromJson(masterDataStage.getContentText(), dtoType);
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * 主数据的查询方法，增加返回临时表中的数据，在传入的list中增加临时表中的数据
     *
     * @param objects 普通查询后得到的list
     * @param <T>     结果集类型
     */
    // TODO: huay 2020/12/7 重写逻辑
    public <T> void query(List<T> objects) {

    }
}
