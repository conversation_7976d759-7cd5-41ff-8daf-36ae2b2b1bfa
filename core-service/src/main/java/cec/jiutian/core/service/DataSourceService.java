package cec.jiutian.core.service;

import cec.jiutian.core.comn.constant.AuthenticationConstant;
import cec.jiutian.core.config.DataDomainEnv;
import cec.jiutian.core.util.BaseRedisCacheUtil;
import cec.jiutian.core.util.DomainParamUtils;
import cec.jiutian.core.util.SpringContextUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class DataSourceService {
    private final DataDomainEnv env;

    public DataSourceService(DataDomainEnv env) {
        this.env = env;
    }

    public String getDataBaseName() {
        String dataBaseName = DomainParamUtils.getDataBaseName();
        if(null != dataBaseName){
            return dataBaseName;
        }
        return env.dataBaseName;
    }

    /*
     * <AUTHOR>
     * @Description //获取有指定字段的所有表名
     * @Param []
     **/
    public List<String> getAllTableNameContainColumn(String domainColumn) {
        if (null == domainColumn) {
            return null;
        }
        List<String> tables = new ArrayList<>();
        String dataBaseName = getDataBaseName();
        Map<Object, Object> tableSchemaMap = null;
        try{
            tableSchemaMap = BaseRedisCacheUtil.hGetAll(dataBaseName);
        }catch (Exception e){
            e.printStackTrace();
        }
        if (null == tableSchemaMap) {
            tableSchemaMap = getTableSchema();
        }
        if (null == tableSchemaMap) {
            return null;
        }
        tableSchemaMap.forEach((k, v) -> {
            String tableName = (String) k;
            List<String> columns = (List<String>) v;
            if (CollectionUtils.isNotEmpty(columns) && columns.contains(domainColumn)) {
                tables.add(tableName);
            }
        });
        return tables;
    }

    /*
     * <AUTHOR>
     * @Description //获取所有含有组织ID的表名
     **/
    public List<String> getTableContainOrganization() {
        return getAllTableNameContainColumn(AuthenticationConstant.ORG_ID_COLUMN_NAME);
    }

    public Map<Object, Object> getTableSchema() {
        SqlSession sqlSession = null;
        Map<Object, Object> schemaMap = null;

        try {
            SqlSessionFactory sqlSessionFactory = SpringContextUtils.getBean(SqlSessionFactory.class);
            sqlSession = sqlSessionFactory.openSession();
            Connection con = sqlSession.getConnection();
            DatabaseMetaData metaData = con.getMetaData();
            ResultSet tables = metaData.getTables(null, null, "%", new String[]{"TABLE"});
            schemaMap = new HashMap<>();
            while (tables.next()) {
                List<String> colNamesList;
                String TABLE_NAME = tables.getString("TABLE_NAME");
                colNamesList = getTableStructure(TABLE_NAME, metaData);
                schemaMap.put(TABLE_NAME, colNamesList);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            if (null != sqlSession) {
                sqlSession.close();
            }
        }
        return schemaMap;
    }

    public static List<String> getTableStructure(String tableName, DatabaseMetaData metaData) {
        List<String> columnModelList = new ArrayList<>();
        try {
            ResultSet columnSet = metaData.getColumns(null, "%", tableName, "%");
            while (columnSet.next()) {
                String column = columnSet.getString("COLUMN_NAME");
                columnModelList.add(column);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return columnModelList;
    }
}
