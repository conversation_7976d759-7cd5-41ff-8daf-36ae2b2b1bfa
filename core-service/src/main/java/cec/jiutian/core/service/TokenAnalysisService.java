package cec.jiutian.core.service;

import cec.jiutian.core.comn.constant.AuthenticationConstant;
import cec.jiutian.core.comn.service.AuthorizedSecretService;
import cec.jiutian.core.entity.TokenAnalysisEntity;
import cec.jiutian.core.factory.JsonMapperFactory;
import cec.jiutian.core.util.DomainParamUtils;
import cec.jiutian.core.util.JsonMapper;
import com.auth0.jwt.exceptions.JWTDecodeException;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

/**
 * @title: TokenAnalysisService.java
 * @package cec.jiutian.core.service
 * @description: 功能说明（jira task id）
 * @author: <EMAIL>
 * @date: 2022-3-16 15:01
 * @version: 2.5.3
 */
@Service
@Slf4j
public class TokenAnalysisService {

    private final AuthorizedSecretService authorizedSecretService;

    public TokenAnalysisService(AuthorizedSecretService authorizedSecretService) {
        this.authorizedSecretService = authorizedSecretService;
    }

    public String getToken(TokenAnalysisEntity entity) {
        return getToken(entity, false);
    }

    /**
     * 获取一个授权密钥
     *
     * @param isRememberMe
     * @return
     */
    public String getToken(TokenAnalysisEntity entity, boolean isRememberMe) {
        String result = null;
        try {
            String jsonStr = JsonMapperFactory.getObjectMapper().writeValueAsString(entity);
            result = authorizedSecretService.getAuthorizedSecret(jsonStr, isRememberMe);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
            throw new RuntimeException("generate token fail! " + e.getMessage());
        }
        return result;
    }

    /*
     * <AUTHOR>
     * @Description //将token 解析成 DTO
     * @Param [token]
     **/
    public TokenAnalysisEntity decodeToken(String token) {
        TokenAnalysisEntity tokenAnalysisEntity;
        try {
            String decodeTokenStr = authorizedSecretService.decodeAuthorizedSecret(token);
            tokenAnalysisEntity = JsonMapper.nonDefaultMapper().fromJson(decodeTokenStr, TokenAnalysisEntity.class);
        } catch (JWTDecodeException e) {
            log.error(e.getMessage());
            throw new RuntimeException("Decode token fail! " + e.getMessage());
        }
        return tokenAnalysisEntity;
    }

    public TokenAnalysisEntity getTokenAnalysisEntity() {
        String tokenStr = DomainParamUtils.getToken();
        if (null == tokenStr) {
            return null;
        }
        TokenAnalysisEntity tokenAnalysisEntity = decodeToken(tokenStr);
        return tokenAnalysisEntity;
    }

    public String getTokenOrgId() {
        TokenAnalysisEntity tokenAnalysisEntity = getTokenAnalysisEntity();
        return null == tokenAnalysisEntity ? null : tokenAnalysisEntity.getOrganizationIdentifier();
    }

    public String getTokenUserId() {
        TokenAnalysisEntity tokenAnalysisEntity = getTokenAnalysisEntity();
        return null == tokenAnalysisEntity ? null : tokenAnalysisEntity.getUserId();
    }

    public String getTokenSystemCode() {
        TokenAnalysisEntity tokenAnalysisEntity = getTokenAnalysisEntity();
        return null == tokenAnalysisEntity ? null : tokenAnalysisEntity.getSystemCode();
    }

    public void checkToken() {
        if (null == DomainParamUtils.getUri()) {
            return;
        }
        String tokenStr = DomainParamUtils.getToken();
        if (null == tokenStr) {
            throw new RuntimeException("Token is null! ");
        }
    }

    //生成token到本地线程
    public void genToken() {
        genToken(StringUtils.EMPTY, AuthenticationConstant.CONTROL_MODE_ORG);
    }

    public void genToken(String orgId) {
        genToken(orgId, AuthenticationConstant.CONTROL_MODE_ORG);
    }

    public void genToken(String orgId, String controlMode) {
        TokenAnalysisEntity tokenAnalysisEntity = new TokenAnalysisEntity();
        tokenAnalysisEntity.setUserId(AuthenticationConstant.ROOT_NAME);
        tokenAnalysisEntity.setOrganizationIdentifier(orgId);
        tokenAnalysisEntity.setControlMode(controlMode);
        DomainParamUtils.setToken(this.getToken(tokenAnalysisEntity));
        DomainParamUtils.putParam("tokenAnalysisEntity", tokenAnalysisEntity);
    }
}
