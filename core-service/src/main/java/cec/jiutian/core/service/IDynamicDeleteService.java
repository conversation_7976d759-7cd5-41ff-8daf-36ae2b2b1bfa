package cec.jiutian.core.service;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

public interface IDynamicDeleteService {

    @PostMapping("/dynamicDelete/deleteHistTblData")
    int deleteHistTblData(@RequestParam("type") int type, @RequestParam("tblName") String tblName, @RequestParam("purgeRuleDayNumber") int purgeRuleDayNumber, @RequestParam("deleteFieldName") String deleteFieldName);

}
