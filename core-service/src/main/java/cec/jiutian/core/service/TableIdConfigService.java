package cec.jiutian.core.service;

import cec.jiutian.security.base.annotation.AutoId;
import cec.jiutian.security.base.po.TableIdConfigPO;
import cec.jiutian.security.base.po.TableServiceIdConfigPO;
import cec.jiutian.core.base.BaseService;
import cec.jiutian.core.comn.util.StringUtils;
import cec.jiutian.core.mapper.TableIdConfigMapper;
import cec.jiutian.core.mapper.TableServiceIdConfigMapper;
import cec.jiutian.core.util.NetUtils;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider;
import org.springframework.core.type.filter.AnnotationTypeFilter;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ReflectionUtils;
import tk.mybatis.mapper.entity.Example;
import javax.persistence.Table;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TableIdConfigService extends BaseService<TableIdConfigPO, String> implements InitializingBean {

    @Value("${server.port}")
    public String port;

    @Value("${app.id}")
    public String appId;

    private final TableIdConfigMapper tableIdConfigMapper;

    private final TableServiceIdConfigMapper tableServiceIdConfigMapper;

    public TableIdConfigService(TableIdConfigMapper tableIdConfigMapper, TableServiceIdConfigMapper tableServiceIdConfigMapper) {
        this.tableIdConfigMapper = tableIdConfigMapper;
        this.tableServiceIdConfigMapper = tableServiceIdConfigMapper;
    }

    /**
     * key table name
     * value table id config
     */
    public static Map<String, TableIdConfigPO> tableIdConfigList = Maps.newConcurrentMap();

    public static Long serviceId;

    @Override
    public void afterPropertiesSet() {
        String macAddress= NetUtils.getLocalMacAddress();
        TableIdConfigService.serviceId=Long.parseLong(getServiceId(macAddress,port).toString());
        addTableIdConfig();
        initConfigMap();
    }

    public Long getServiceId(String macAddress,String port){
       List<TableServiceIdConfigPO> serviceList= getServiceList(macAddress,port);
       if(CollectionUtils.isEmpty(serviceList)){
          return saveServiceConfig(macAddress,port);
       }
       return serviceList.get(0).getIdentifier();
    }

    public List<TableServiceIdConfigPO> getServiceList(String macAddress, String port){
        Example example = new Example(TableServiceIdConfigPO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("macAddress", macAddress);
        criteria.andEqualTo("port", port);
       return tableServiceIdConfigMapper.selectByExample(example);
    }

    public Long saveServiceConfig(String macAddress, String port){
        TableServiceIdConfigPO config=new TableServiceIdConfigPO();
        config.setMacAddress(macAddress);
        config.setPort(port);
        config.setServiceName(appId);
        config.setCreateTime(new Date());
        tableServiceIdConfigMapper.insertSelective(config);
        return config.getIdentifier();
    }

    /**
     * @param tableName table name
     * @return return table id config
     */
   public TableIdConfigPO getTableIdConfig(String tableName){
       return TableIdConfigService.tableIdConfigList.get(tableName.toLowerCase());
   }

    /**
     * create table id config
     * Only add table id configurations that exist in entity classes but do not exist in configuration tables
     */
   public void addTableIdConfig(){
       Map<String,String> allTableMap= getAutoIdTableList();
       List<String> notConfigTableList= tableIdConfigMapper.getTableIdNotConfigList(ArrayToString(allTableMap.keySet()));
       createTableIdConfigList(notConfigTableList,allTableMap);
   }

   public void initConfigMap(){
       List<TableIdConfigPO> configList= tableIdConfigMapper.getAllTableIdConfigList();
       for (TableIdConfigPO po: configList) {
           TableIdConfigService.tableIdConfigList.put(po.getTableName(),po);
       }
   }

    /**
     * Get all table annotated by AutoId
     */
    private Map<String,String> getAutoIdTableList() {
        ClassPathScanningCandidateComponentProvider scanner = new ClassPathScanningCandidateComponentProvider(false);
        scanner.addIncludeFilter(new AnnotationTypeFilter(Table.class));
        Set<Class<?>> annotatedClasses = scanner.findCandidateComponents("cec.jiutian").stream()
                .map(beanDefinition -> {
                    try {
                        return Class.forName(beanDefinition.getBeanClassName());
                    } catch (ClassNotFoundException e) {
                        throw new RuntimeException(e);
                    }
                }).collect(Collectors.toSet());
        Map<String,String> tableIdMap=Maps.newHashMap();
        List<Field> fieldList;
        for (final Class<?> classObject : annotatedClasses) {
            Table table = classObject.getAnnotation(Table.class);
            fieldList = scanAnnotatedFields(classObject, AutoId.class);
            if(!CollectionUtils.isEmpty(fieldList)){
                AutoId autoId= fieldList.get(0).getAnnotation(AutoId.class);
                tableIdMap.put(table.name().toLowerCase(),autoId.type().name());
            }
        }
        return tableIdMap;
    }

    /**
     * Query all annotated fields
     * @param clazz Class
     * @param annotationClass annotation Class
     * @return
     */
    public static List<Field> scanAnnotatedFields(Class<?> clazz, Class annotationClass) {
        List<Field> annotatedFields = new ArrayList<>();
        ReflectionUtils.doWithFields(clazz, field -> {
            if (field.isAnnotationPresent(annotationClass)) {
                annotatedFields.add(field);
            }
        });
        return annotatedFields;
    }

    public String ArrayToString(Set<String> array){
        if(array==null){
            return null;
        }
        StringBuilder sb=new StringBuilder();
        int i=0;
        for (String str:array) {
            sb.append(str);
            if(i<array.size()-1){
                sb.append(",");
            }
            i++;
        }
        return sb.toString();
    }

    public void createTableIdConfigList(List<String> notConfigTableList,Map<String,String> map){
        if(CollectionUtils.isEmpty(notConfigTableList)){
            return;
        }
        String idType = null;
        for (String tableName:notConfigTableList) {
            idType = map.get(tableName);
            if(StringUtils.isNotBlank(idType)){
                createTableIdConfig(tableName,idType);
            }
        }
    }

    public void createTableIdConfig(String tableName,String idType){
        TableIdConfigPO po=new TableIdConfigPO();
        po.setTableName(tableName);
        po.setIdentifierType(idType);
        switch (idType){
            case "SEQUENCE":
                po.setSequenceName(tableName+"_seq");
                tableIdConfigMapper.createSequence(po.getSequenceName());
                break;
            case "GENERAL":
                po.setNowSequence(0L);
                break;
        }
       if(tableIdConfigMapper.getTableIdConfigByName(tableName)==null) {
           save(po);
       }
    }

    /**
     * 获取序列
     * @param sequenceName
     * @return
     */
    public Long getSequence(String sequenceName){
       return tableIdConfigMapper.getSequence(sequenceName);
    }

    /**
     * 获取虚拟序列
     * @param tableId
     * @return
     */
    public Long getSimulateSequence(Long tableId){
        TableIdConfigPO configPO= tableIdConfigMapper.selectByPrimaryKeyForUpdate(tableId);
        configPO.setNowSequence(add(configPO.getNowSequence(),1));
        tableIdConfigMapper.updateByPrimaryKey(configPO);
        return configPO.getNowSequence();
    }

    /**
     * 加法使用位移
     * @param a
     * @param b
     * @return
     */
    public static long add(long a,long b) {
        long sum = 0;
        while(b != 0) {
            sum = a ^ b;
            b = (a & b) << 1;
            a = sum;
        }
        return sum;
    }
}
