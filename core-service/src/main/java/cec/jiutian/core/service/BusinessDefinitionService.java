package cec.jiutian.core.service;

import cec.jiutian.core.base.BaseResponse;
import cec.jiutian.core.entity.OperationCodeDTO;
import cec.jiutian.core.result.ErrorCodeResult;
import cec.jiutian.core.util.FillerUtil;
import org.springframework.web.bind.annotation.PostMapping;

import javax.ws.rs.core.Response;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.ArrayList;
import java.util.List;

public abstract class BusinessDefinitionService {

    public Object getAllOperationCode(Class<?> clazz) {
        List<OperationCodeDTO> result = new ArrayList<>();
        try {
            String className = clazz.getName();
            Class innerClazz[] = clazz.getDeclaredClasses();
            for (Class class1 : innerClazz) {
                if (class1.getSimpleName().contains("Operation")) {
                    String simpleName = class1.getSimpleName();
                    Class operationClazz = Class.forName(className + "$" + simpleName);
                    Field[] declaredFields = operationClazz.getDeclaredFields();
                    for (Field field : declaredFields) {
                        field.setAccessible(true);
                        if (Modifier.isStatic(field.getModifiers())) {
                            OperationCodeDTO operationCodeDTO = new OperationCodeDTO();
                            operationCodeDTO.setOperationCode(field.getName());
                            result.add(operationCodeDTO);
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        ErrorCodeResult errorCodeResult = new ErrorCodeResult();
        errorCodeResult.setCode("000");
        errorCodeResult.setMessage("查询所有操作码成功");
        FillerUtil.fillErrorCodeResult(errorCodeResult);
        BaseResponse baseResponse = new BaseResponse();
        baseResponse.setStatus(Response.Status.OK.getStatusCode());
        baseResponse.setEntity(errorCodeResult);
        FillerUtil.setOutput(baseResponse, result);
        return baseResponse;

    }

    @PostMapping("/getAllOperationCode")
    public abstract Object getAllOperationCode();

}
