package cec.jiutian.core.util;

import cec.jiutian.core.exception.MesErrorCodeException;
import cec.jiutian.core.feign.ModeServiceFeign;
import cec.jiutian.security.base.dto.BaseConvertModeDTO;
import cec.jiutian.core.comn.errorCode.BaseErrorCode;
import cec.jiutian.core.comn.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date ${DATE}
 */
@Slf4j
@Component
public class ModeConvertUtils {
    private final ModeServiceFeign modeServiceFeign;

    public ModeConvertUtils(ModeServiceFeign modeServiceFeign) {
        this.modeServiceFeign = modeServiceFeign;
    }

    public void checkInitialMode(String currentState, String modeModelName) {
        String initialMode = getInitialMode(modeModelName);
        if (!StringUtils.equals(currentState, initialMode)) {
            MesErrorCodeException exception = new MesErrorCodeException(BaseErrorCode.STATE_CONVERT_ERROR.getCode())
                    .addMsgItem(initialMode)
                    .addMsgItem(currentState);
            log.error("{}", exception.getMessage(), exception);
            throw exception;
        }
    }

    public String getInitialMode(String modeModelName) {
        String initialMode = modeServiceFeign.getInitialMode(modeModelName);
        if (StringUtils.isBlank(initialMode)) {
            MesErrorCodeException exception = new MesErrorCodeException(BaseErrorCode.STATE_NOT_EXISTED.getCode())
                    .addMsgItem(initialMode);
            log.error("{}", exception.getMessage(), exception);
            throw exception;
        }
        return initialMode;
    }

    public <T extends BaseConvertModeDTO> String getNextMode(String modeModelName, String currentState, T t) {
        String mode = modeServiceFeign.getNextModeByCondition(modeModelName, currentState, t.getConditionFieldName(), t.getConditionValueText());
        if (StringUtils.isBlank(mode)) {
            MesErrorCodeException exception = new MesErrorCodeException(BaseErrorCode.STATE_CONVERT_ERROR.getCode())
                    .addMsgItem(modeModelName)
                    .addMsgItem(currentState)
                    .addMsgItem("");
            log.error("{}", exception.getMessage(), exception);
            throw exception;
        }
        return mode;
    }

    public String getNextMode(String modeModelName, String currentState) {
        String mode = modeServiceFeign.getNextModeByCondition(modeModelName, currentState, null, null);
        if (StringUtils.isBlank(mode)) {
            MesErrorCodeException exception = new MesErrorCodeException(BaseErrorCode.STATE_CONVERT_ERROR.getCode())
                    .addMsgItem(modeModelName)
                    .addMsgItem(currentState)
                    .addMsgItem("");
            log.error("{}", exception.getMessage(), exception);
            throw exception;
        }
        return mode;
    }

    public <T extends BaseConvertModeDTO> String getNextMode(String modeModelName, String currentState, String conditionFieldName, String conditionValueText) {
        String mode = modeServiceFeign.getNextModeByCondition(modeModelName, currentState, conditionFieldName, conditionValueText);
        if (StringUtils.isBlank(mode)) {
            MesErrorCodeException exception = new MesErrorCodeException(BaseErrorCode.STATE_CONVERT_ERROR.getCode())
                    .addMsgItem(modeModelName)
                    .addMsgItem(currentState)
                    .addMsgItem(conditionValueText);
            log.error("{}", exception.getMessage(), exception);
            throw exception;
        }
        return mode;
    }
}
