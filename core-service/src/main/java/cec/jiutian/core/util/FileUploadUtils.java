package cec.jiutian.core.util;

import cec.jiutian.core.entity.FileOperation;
import cec.jiutian.core.exception.MesErrorCodeException;
import cec.jiutian.core.feign.FileUploadServiceFeign;
import cec.jiutian.security.base.entity.FeignString;
import cec.jiutian.core.comn.errorCode.BaseErrorCode;
import cec.jiutian.core.comn.util.StringUtils;
import com.fasterxml.jackson.databind.JavaType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date ${DATE}
 */
@Component
@Slf4j
public class FileUploadUtils {
    private static final JsonMapper JSON_MAPPER = new JsonMapper();
    private final FileUploadServiceFeign fileUploadServiceFeign;
    @Value("${web.file-path:fabos}")
    private String FILE_PATH;

    public FileUploadUtils(FileUploadServiceFeign fileUploadServiceFeign) {
        this.fileUploadServiceFeign = fileUploadServiceFeign;
    }

    /**
     * 根据提供的文件列表批量上传文件
     *
     * @return 返回逗号分割的UUID列表
     */
    public String uploadFile(MultipartFile[] files, String functionFolder) {
        return uploadFile(files, null, functionFolder);
    }

    public String uploadFile(MultipartFile[] files, String sourceCode,  String functionFolder) {
        if (files != null) {
            MultipartFile[] multipartFiles = Arrays.stream(files).filter(file -> file.getSize() > 0).toArray(MultipartFile[]::new);
            if (multipartFiles.length > 0) {
                String lastEventUser = BizParamUtils.getLastEventUser();
                FeignString feignString = fileUploadServiceFeign.multiFileUploadAndSaveData(multipartFiles, FILE_PATH, sourceCode, lastEventUser, functionFolder);
                if (feignString == null || StringUtils.isBlank(feignString.getFeignString())) {
                    MesErrorCodeException exception = new MesErrorCodeException(BaseErrorCode.FILE_UPLOAD_ERROR.getCode());
                    log.error("{}", exception.getMessage(), exception);
                    throw exception;
                }
                List<String> fileUUIDList = (List<String>) BizParamUtils.getParam("fileUUIDList");
                String[] fileList = feignString.getFeignString().split(",");
                if (CollectionUtils.isNotEmpty(fileUUIDList)) {
                    fileUUIDList.addAll(Arrays.asList(fileList));
                    BizParamUtils.putParam("fileUUIDList", fileUUIDList);
                } else {
                    BizParamUtils.putParam("fileUUIDList", Arrays.asList(fileList));
                }
                return feignString.getFeignString();
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * 根据提供的单个文件上传接口
     *
     * @return 返回逗号分割的UUID列表
     */
    public String uploadFile(MultipartFile file, String functionFolder) {
        return uploadFile(file, null, functionFolder);
    }

    public String uploadFile(MultipartFile file, String sourceCode, String functionFolder) {
        if (file != null) {
            String lastEventUser = BizParamUtils.getLastEventUser();
            FeignString feignString = fileUploadServiceFeign.fileUpload(file, FILE_PATH, sourceCode, lastEventUser, functionFolder);
            if (feignString == null || StringUtils.isBlank(feignString.getFeignString())) {
                MesErrorCodeException exception = new MesErrorCodeException(BaseErrorCode.FILE_UPLOAD_ERROR.getCode());
                log.error("{}", exception.getMessage(), exception);
                throw exception;
            }
            return feignString.getFeignString();
        } else {
            return null;
        }
    }

    /**
     * 根据前端传递的fileOperation数据对文件进行操作
     *
     * @param url 当前的文件字段,一般为逗号隔开的UUID列表
     * @return 返回整理后的文件字段
     */
    public String fileOperation(String fileOperation, String url) {
        if (StringUtils.isNotBlank(fileOperation)) {
            FeignString string = new FeignString(url);
            JavaType javaType = JSON_MAPPER.constructCollectionType(List.class, FileOperation.class);
            List<FileOperation> fileOperations = JSON_MAPPER.fromJson(fileOperation, javaType);
            if(CollectionUtils.isEmpty(fileOperations)){
                return url;
            }
            fileOperations.forEach(it -> {
                switch (it.getOperation()) {
                    case RENAME:
                        fileUploadServiceFeign.changeFilename(it);
                        break;
                    case DELETE:
                        if (StringUtils.contains(string.getFeignString(), it.getId())) {
                            string.setFeignString(subStringUrl(string.getFeignString(), it.getId()));
                        }
                        fileUploadServiceFeign.deleteFileById(it.getId());
                        break;
                }
            });
            return string.getFeignString();
        } else {
            return url;
        }
    }

    /**
     * 合并上传文件并处理fileOperation的操作，此处只处理多文件情况，单文件不会出现既有文件又有fileOperation的情况，需要各自功能额外处理
     */
    public String uploadFile(MultipartFile[] files,  String functionFolder, String fileOperation, String url) {
        return uploadFile(files, null,  functionFolder, fileOperation, url);
    }

    public String uploadFile(MultipartFile[] files, String sourceCode,  String functionFolder, String fileOperation, String url) {
        String uploadFile = uploadFile(files, sourceCode,  functionFolder);
        if (StringUtils.isNotBlank(url)) {
            if (StringUtils.isNotBlank(uploadFile)) {
                url = url + "," + uploadFile;
            }
        } else {
            url = uploadFile;
        }
        return fileOperation(fileOperation, url);
    }

    private String subStringUrl(String url, String id) {
        return StringUtils.removeEnd(url.replaceAll(id + ",?", ""), ",");
    }

    /**
     * 文件删除操作，直接处理url字段中的数据，如果为单个UUID则删除单个文件，如果为UUID列表则进行批量删除
     */
    public void deleteFile(String url) {
        if (StringUtils.isNotBlank(url)) {
            if (url.contains(",")) {
                fileUploadServiceFeign.deleteBatchFile(url);
            } else {
                fileUploadServiceFeign.deleteFileById(url);
            }
        }
    }

    public String uploadFileForFront(MultipartFile[] files, String filePath, String functionFolder, String fileOperation) {
        return fileUploadServiceFeign.multiFileUploadAndSaveDataForFront(files, filePath, functionFolder, fileOperation);
    }
}
