package cec.jiutian.core.util;

import cec.jiutian.core.result.ErrorCodeResult;
import cec.jiutian.core.result.Pager;
import cec.jiutian.security.base.dto.BaseQueryDTO;
import cec.jiutian.core.base.BaseResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.github.pagehelper.PageInfo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.util.ContentCachingRequestWrapper;

import jakarta.servlet.http.HttpServletRequest;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 *  * @description: 自定义规则，msg中的可填充部分进行填充  * @copyright: Copyright (c) 2022
 *  * @company: 中电九天    * @version: 1.0  * @date: 2022
 */
public class FillerUtil {

    // 定义填充规则
    private static String FILLER_RULE = "\\{\\{(.*?)\\}\\}";

    public static boolean hasMatches(String regix, String str) {
        Pattern pattern = Pattern.compile(regix);
        Matcher m = pattern.matcher(str);
        return m.find();
    }

    /**
     *  * @description: 遍历list中的数据进行填充    * @version: 1.0
     *  * @date: 2022 2022年3月22日 下午4:53:25  * @param str  * @param fillList
     *  * @return
     */
    public static String fill(String str, List<String> fillList) {
        if (null == str) {
            str = "";
        }
        Pattern pattern = Pattern.compile(FILLER_RULE);

        Matcher matcher = pattern.matcher(str);
        int index = 0;
        while (matcher.find()) {
            str = str.replaceFirst(FILLER_RULE, getItem(fillList, index++));
        }
        return str;
    }

    /**
     *  * @description: 获取List中的符合index的数据，否则返回空字符串  
     *  * @version: 1.0  * @date: 2022 2022年3月22日 下午4:47:41  * @param list
     *  * @param index  * @return
     */
    private static String getItem(List<String> list, int index) {
        if (null == list || list.isEmpty() || index < 0 || index >= list.size()) {
            return "";
        } else {
            return getObject(list.get(index));
        }
    }

    /**
     * 经前端协定： Object 为null时，则返回 js的空实体即："{}" String 为null时，则返回js的空字符串：""
     */
    private static Object getObject(Object obj) {
        if (null == obj) {
            return "{}";
        } else {
            return obj;
        }
    }

    private static String getObject(String obj) {
        if (null == obj) {
            return "";
        } else {
            return obj;
        }
    }

    /**
     *  * @description: 根据前端协定，重新填充未填字段    * @version: 1.0
     *  * @date: 2022 2022年3月23日 上午11:02:46  * @param errorCodeResult
     */
    public static void fillErrorCodeResult(ErrorCodeResult errorCodeResult) {
        if (null == errorCodeResult) {
            return;
        } else {
            errorCodeResult.setCode(getObject(errorCodeResult.getCode()));
//            errorCodeResult.setOutput(getObject(errorCodeResult.getOutput()));
            errorCodeResult.setMessage(getObject(errorCodeResult.getMessage()));
            errorCodeResult.setDetail(getObject(errorCodeResult.getDetail()));
        }
    }

    /**
     *  * @description: 异常填充几个基础数据    * @version: 1.0  * @date:
     * 2022 2022年3月24日 上午9:11:14  * @param errorCodeResult  * @param req
     */
    public static void fillErrorCodeResult(ErrorCodeResult errorCodeResult, HttpServletRequest req) {
        if (null == req || null == errorCodeResult) {
            return;
        } else {
            // 获取路径
//            errorCodeResult.setPath(req.getServletPath());
            // 获取输入
            if (req != null && req instanceof ContentCachingRequestWrapper) {
                ContentCachingRequestWrapper wrapper = (ContentCachingRequestWrapper) req;
                String input = StringUtils.toEncodedString(wrapper.getContentAsByteArray(),
                        Charset.forName(wrapper.getCharacterEncoding()));
//                errorCodeResult.setInput(input);
            }
        }
    }

    public static void setOutput(BaseResponse baseResponse, Object output) {
        if (null == baseResponse || null == output) {
            return;
        }
        if (output instanceof PageInfo) {
            PageInfo page = (PageInfo) output;
            baseResponse.setOutput(page.getList());
            Pager pager = new Pager();
            pager.setPageNum(page.getPageNum());
            pager.setPageSize(page.getPageSize());
            pager.setTotal(page.getTotal());
            baseResponse.setPager(pager);
        } else {
            baseResponse.setOutput(output);
            baseResponse.setPager(null);
        }
    }

    /*
     * <AUTHOR>
     * @Description //根据QueryDTO中的参数对行列进行过滤
     * @Date 15:22 2022-5-27
     * @Param [baseResponse, output]
     * @return void
     **/
    public static Object filtrationRowColumn(BaseQueryDTO queryDTO, Object output) {
        if (!(output instanceof List)) {
            return output;
        }
        JSONObject filter = queryDTO.getFilter();
        List<String> columns = queryDTO.getColumns();
        if(null == filter && null== columns){
            return output;
        }
        List<Object> list;
        if ((output instanceof PageInfo)) {
            PageInfo page = (PageInfo) output;
            list = page.getList();
        }else {
            list = (List<Object>) output;
        }
        if (CollectionUtils.isEmpty(list)) {
            return output;
        }
        List<JSONObject> jobList = list.stream().map(X ->
                JSONObject.parseObject(JSON.toJSONString(X, SerializerFeature.WriteMapNullValue)) //包含null的属性
        ).collect(Collectors.toList());
        jobList = filtrationRow(filter, jobList);
        jobList = filtrationColumn(columns, jobList);
        if ((output instanceof PageInfo)) {
            ((PageInfo) output).setList(jobList);
        }else {
            output = jobList;
        }
        return output;
    }

    public static List<JSONObject> filtrationRow(JSONObject filter, List<JSONObject> jobList) {
        if (null == filter) {
            return jobList;
        }
        Set<String> keys = filter.keySet();
        //遍历filter元素
        for (String key : keys) {
            Object valueObj = filter.get(key);
            // 1: value为null这过滤为null的
            if (null == valueObj) {
                jobList.removeIf(job -> null != job.get(key));
            }else if(valueObj instanceof ArrayList){//2:value为array则批量过滤
                List<Object> values = (List) filter.get(key);
                List<String> valuesStr = values.stream().map(x->String.valueOf(x)).collect(Collectors.toList());
                //遍历行 匹配行中是当前 filter元素的key  的值，在value中，不是则删除
                jobList.removeIf(job -> !valuesStr.contains(String.valueOf(job.get(key))));
            }else {//3:value为string 或 number则普通过滤
                jobList.removeIf(job -> !String.valueOf(valueObj).equals(String.valueOf(job.get(key))));
            }


        }
        return jobList;
    }

    public static List<JSONObject> filtrationColumn(List<String> columns, List<JSONObject> jobList) {
        if (CollectionUtils.isEmpty(columns)) {
            return jobList;
        }
        List<JSONObject> result = new ArrayList<>();
        for (JSONObject job : jobList) {
            JSONObject resJOb = new JSONObject();
            for (String column : columns) {
                if(null != job.get(column)){
                    resJOb.put(column, job.get(column));
                }
            }
            result.add(resJOb);
        }
        return result;
    }
}
