package cec.jiutian.core.util;

import cec.jiutian.core.aspect.BizParamAspect;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <p>业务参数工具类，在服务端接收到一个请求时，结合{@link BizParamAspect}使用本地线程存储常用的三个业务参数（lastEventUser，lastEventComment，lastEventTime）</p>
 * <p>该工具类中的本地线程变量不允许随意修改，仅在{@link BizParamAspect}中进行初始化和销毁</p>
 * <p>若服务端未接收任何请求，而是后台逻辑中使用了{@link BaseEntityUtils}对数据进行持久化操作，则本地线程中会初始化一个lastEventTime有值的对象，其他参数则为null</p>
 *
 * <AUTHOR>
 * @date 2021/5/26
 */
public class BizParamUtils {
    private static final ThreadLocal<BizParam> BIZ_PARAM_THREAD_LOCAL = new ThreadLocal<>();

    private static BizParam getBizParam() {
        return BIZ_PARAM_THREAD_LOCAL.get();
    }

    public static void setBizParam(BizParam bizParam) {
        if (getBizParam() == null) {
            BIZ_PARAM_THREAD_LOCAL.set(bizParam);
        }
    }

    public static void clearBizParam() {
        BIZ_PARAM_THREAD_LOCAL.remove();
    }

    public static String getLastEventUser() {
        if (getBizParam() != null) {
            return BIZ_PARAM_THREAD_LOCAL.get().getLastEventUser();
        } else {
            return null;
        }
    }

    public static String getUserId() {
        if (getBizParam() != null) {
            return BIZ_PARAM_THREAD_LOCAL.get().getUserId();
        } else {
            return null;
        }
    }

    public static String getLastEventComment() {
        if (getBizParam() != null) {
            return getBizParam().getLastEventComment();
        } else {
            return null;
        }
    }

    public static void putParam(Object key, Object value) {
        if (getBizParam() != null) {
            if (getBizParam().getMap() == null) {
                Map<Object, Object> map = new HashMap<>();
                map.put(key, value);
                getBizParam().setMap(map);
            } else {
                getBizParam().getMap().put(key, value);
            }
        }
    }

    public static String getOperationName() {
        if (getBizParam() != null) {
            return getBizParam().getLastEventName();
        } else {
            return null;
        }
    }

    public static void setOperationName(String operationName) {
        if (getBizParam() != null) {
            getBizParam().setLastEventName(operationName);
        }
    }

    public static Object getParam(Object key) {
        if (getBizParam() != null && getBizParam().getMap() != null) {
            return getBizParam().getMap().get(key);
        } else {
            return null;
        }
    }

    /**
     * 特殊方法，用于非http请求逻辑中获取统一时间时使用
     */
    public static Date getLastEventTime() {
        if (getBizParam() == null) {
            setBizParam(new BizParam());
        }
        return getBizParam().getLastEventTime();
    }

    /**
     * 业务参数类，主要用于业务接口处理中存储lastEventUser，lastEventComment，lastEventTime三个参数
     */
    public static class BizParam {
        private String lastEventUser;
        private String lastEventComment;
        @JsonFormat(pattern = "yyyy-MM-dd hh:mm:ss.SSS")
        private Date lastEventTime = new Date();
        private String lastEventName;
        private String userId;
        private Map<Object, Object> map;

        public String getLastEventUser() {
            return lastEventUser;
        }

        public void setLastEventUser(String lastEventUser) {
            this.lastEventUser = lastEventUser;
        }

        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        public String getLastEventComment() {
            return lastEventComment;
        }

        public void setLastEventComment(String lastEventComment) {
            this.lastEventComment = lastEventComment;
        }

        public Date getLastEventTime() {
            return lastEventTime;
        }

        public void setLastEventTime(Date lastEventTime) {
            this.lastEventTime = lastEventTime;
        }

        public Map<Object, Object> getMap() {
            return map;
        }

        public void setMap(Map<Object, Object> map) {
            this.map = map;
        }

        public String getLastEventName() {
            return lastEventName;
        }

        public void setLastEventName(String lastEventName) {
            this.lastEventName = lastEventName;
        }
    }
}
