package cec.jiutian.core.util;

import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;

@Slf4j
public class DateUtil extends org.apache.commons.lang3.time.DateUtils {
    /**
     * 获取两个日期之间的所有日期
     *
     * @param startTime 开始日期
     * @param endTime   结束日期
     * @return
     */
    public static List<Date> getDays(Date startTime, Date endTime) {

        // 返回的日期集合
        List<Date> days = new ArrayList<Date>();

        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

        Calendar tempStart = Calendar.getInstance();
        tempStart.setTime(startTime);

        Calendar tempEnd = Calendar.getInstance();
        tempEnd.setTime(endTime);
        tempEnd.add(Calendar.DATE, 0); // 日期加1(包含结束)
        while (tempStart.before(tempEnd) || DateUtil.isSameDay(tempStart, tempEnd)) {
            days.add(tempStart.getTime());
            tempStart.add(Calendar.DAY_OF_YEAR, 1);
        }

        return days;
    }

    /**
     * 把毫秒数转换成时分秒  3小时10分钟13秒
     *
     * @param millis
     * @return
     */
    public static String millisToStringShort(long millis) {
        StringBuilder strBuilder = new StringBuilder();
        long temp = millis;
        long hper = 60 * 60 * 1000;
        long mper = 60 * 1000;
        long sper = 1000;
        if (temp / hper > 0) {
            strBuilder.append(temp / hper).append("小时");
        }
        temp = temp % hper;

        if (temp / mper > 0) {
            strBuilder.append(temp / mper).append("分钟");
        }
        temp = temp % mper;
        if (temp / sper > 0) {
            strBuilder.append(temp / sper).append("秒");
        }
        return strBuilder.toString();
    }

    /*@param dateformat 时间格式
     *@param date 基准日期
     *@param intnum 日期偏移,正数向前,负数向后!
     * */
    public static String dateshit(String dateformat, String date, int intnum) throws ParseException {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(dateformat); //格式工具
        Date da = simpleDateFormat.parse(date);
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(da);
        calendar.add(Calendar.DAY_OF_MONTH, intnum); //日期偏移,正数向前,负数向后!
        return simpleDateFormat.format(calendar.getTime());
    }

    public static Date dateshit(Date date, int intnum) {
//    	SimpleDateFormat simpleDateFormat = new SimpleDateFormat(dateformat);//格式工具
//    	Date da = simpleDateFormat.parse(date);
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_MONTH, intnum); //日期偏移,正数向前,负数向后!
        return calendar.getTime();
    }

    public static List<String> getMonthBetween(String minDate, String maxDate) {
        ArrayList<String> result = new ArrayList<String>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM"); //格式化为年月

        Calendar min = Calendar.getInstance();
        Calendar max = Calendar.getInstance();

        try {
            min.setTime(sdf.parse(minDate));
        } catch (ParseException e1) {
            // TODO Auto-generated catch block
            e1.printStackTrace();
        }
        min.set(min.get(Calendar.YEAR), min.get(Calendar.MONTH), 1);

        try {
            max.setTime(sdf.parse(maxDate));
        } catch (ParseException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        max.set(max.get(Calendar.YEAR), max.get(Calendar.MONTH), 2);

        Calendar curr = min;
        while (curr.before(max)) {
            result.add(sdf.format(curr.getTime()));
            curr.add(Calendar.MONTH, 1);
        }
        min = null;
        max = null;
        curr = null;
        return result;
    }

    public static String parseStringToDate(String strDate) {
        DateTimeFormatter dateTimeFormat = DateTimeFormat.forPattern("yyyyMMddHHmmssSSS");
        DateTime dateTime = DateTime.parse(strDate, dateTimeFormat);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(dateTime.toDate());
        return calendar.get(Calendar.YEAR) + "年" + (calendar.get(Calendar.MONTH) + 1) + "月" + calendar.get(Calendar.DATE) + "日" + calendar.get(Calendar.HOUR_OF_DAY) + ":"
                + calendar.get(Calendar.MINUTE) + ":" + calendar.get(Calendar.SECOND);

    }

    public static Date parseToDate(String strDate) {
        DateTimeFormatter dateTimeFormat = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ssSSS");
        DateTimeFormatter dateTimeFormatEnd = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss.SSS");
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
        DateTime dateTime = DateTime.parse(strDate, dateTimeFormat);
        String newDatesStr = dateTime.toString("yyyy-MM-dd HH:mm:ss.SSS");
        DateTime dateTimeEnd = DateTime.parse(newDatesStr, dateTimeFormatEnd);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(dateTimeEnd.toDate());
        return calendar.getTime();

    }

    public static void checkDateFormat(String strDate, String format) {
        DateTimeFormatter dateTimeFormat = DateTimeFormat.forPattern(format);
        try {
            DateTime dateTime = DateTime.parse(strDate, dateTimeFormat);
        }catch (Exception e){

        }

    }

    public static int getNowDateNum(String strDate) {
        String dateStr = strDate.replace("-", "");
        String dataNumStr = dateStr.substring(0, 8);
        int result = Integer.valueOf(dataNumStr);
        return result;
    }

    public static int getNowDateNum() {
        String nowDateStr = getNowDateTimeStr();
        String dateStr = nowDateStr.replace("-", "");
        String dataNumStr = dateStr.substring(0, 8);
        return Integer.getInteger(dataNumStr);

    }

    public static String getNowDateTimeStr() {
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd :hh:mm:ss");
        return dateFormat.format(calendar.getTime());

    }

    /**
     * 将日期的时分秒设置为0
     * @param date 不为空
     */
    public static Date clearTime(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }
}
