package cec.jiutian.core.util;

import cec.jiutian.core.exception.MesErrorCodeException;
import cec.jiutian.security.base.entity.MasterEntity;
import cec.jiutian.core.base.BaseService;
import cec.jiutian.core.comn.errorCode.BaseErrorCode;
import cec.jiutian.core.comn.util.StringUtils;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2020/11/13
 */
@Slf4j
public class MasterEntityUtils {
    /**
     * 签出方法
     *
     * @param t       主表原数据。该数据应该为通过业务检查的数据。
     * @param dto     更新主表数据的dto，如果dto的数据需要处理，则该数据应该为逻辑处理后的数据。
     * @param service 主表数据对应的service的实例化对象
     * @param <T>     主表数据类型
     * @param <PK>    主表数据主键类型
     * @param <S>     主表数据对应的service类型
     * @return 签出操作结果
     */
    public static <T extends MasterEntity, PK extends Serializable, S extends BaseService<T, PK>> Boolean checkOut(T t, Object dto, S service) {
        if (StringUtils.equals(t.getModifyFlag(), "Y")) {
            MesErrorCodeException exception = new MesErrorCodeException(BaseErrorCode.CHECK_OUT_ERROR.getCode());
            log.error("{}", exception.getMessage(), exception);
            throw exception;
        }
        t.setModifyFlag("Y");
        t.setUuid(UUID.randomUUID().toString());
        return BaseEntityUtils.persistentOperation(t, dto, "CheckOut", service, BaseEntityUtils.Operation.UPDATE);
    }

    /**
     * 签入方法
     *
     * @param t       主表原数据。该数据应该为通过业务检查的数据。
     * @param dto     更新主表数据的dto，如果dto的数据需要处理，则该数据应该为逻辑处理后的数据。
     * @param service 主表数据对应的service的实例化对象
     * @param <T>     主表数据类型
     * @param <PK>    主表数据主键类型
     * @param <S>     主表数据对应的service类型
     * @return 签入操作结果
     */
    public static <T extends MasterEntity, PK extends Serializable, S extends BaseService<T, PK>> Boolean checkIn(T t, Object dto, S service) {
        if (!StringUtils.equals(t.getModifyFlag(), "Y")) {
            MesErrorCodeException exception = new MesErrorCodeException(BaseErrorCode.NO_CHECK_OUT.getCode());
            log.error("{}", exception.getMessage(), exception);
            throw exception;
        }
        t.setModifyFlag("N");
        t.setUuid(null);
        return BaseEntityUtils.persistentOperation(t, dto, "CheckIn", service, BaseEntityUtils.Operation.UPDATE);
    }
}
