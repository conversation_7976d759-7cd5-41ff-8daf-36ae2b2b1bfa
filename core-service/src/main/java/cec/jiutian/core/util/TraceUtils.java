package cec.jiutian.core.util;

import brave.Tracer;
import org.springframework.transaction.support.TransactionSynchronizationManager;

/**
 * <AUTHOR>
 * @date 2023/1/10
 */
public class TraceUtils {

    public static Tracer getTracer() {
        return SpringContextUtils.getBean(Tracer.class);
    }

    public static String getTraceIdString() {
        Tracer tracer = getTracer();
        if (tracer.currentSpan() != null) {
            return tracer.currentSpan().context().traceIdString();
        } else {
            return null;
        }
    }

    public static String getHistoryDataKey() {
        String s = getTraceIdString();
        if (s != null) {
            return s + "_" + TransactionSynchronizationManager.getCurrentTransactionName() + "_" + BizParamUtils.getLastEventTime();
        } else {
            return null;
        }
    }
}
