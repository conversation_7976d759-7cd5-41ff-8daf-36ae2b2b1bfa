package cec.jiutian.core.util;

import cec.jiutian.core.entity.AbstractDomain;
import cec.jiutian.core.exception.MesErrorCodeException;
import cec.jiutian.security.base.entity.BaseEntity;
import cec.jiutian.security.base.entity.ObsoleteEntity;
import cec.jiutian.core.base.BaseDomainService;
import cec.jiutian.core.comn.errorCode.BaseErrorCode;
import cec.jiutian.core.comn.util.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.Serializable;

/*
 * <AUTHOR>
 * @Date 15:14 2022-4-28
 **/
@Slf4j
@Component
public class ObsoleteCheckUtils {

    /**
     * Domain中Entity的 启用校验
     */
    public static <V extends BaseEntity, D extends AbstractDomain<V>> void checkObsoleteFlagN(D d) {
        V entity = d.getEntity();
        if (null == entity) {
            return;
        }
        ObsoleteEntity validEntity = new ObsoleteEntity();
        if (!BeanUtils.ClassFieldContain(entity.getClass(), ObsoleteEntity.class)) {
            return;
        }
        BeanUtils.copyProperties(entity, validEntity);
        if (!"N".equals(validEntity.getObsoleteFlag())) {
            MesErrorCodeException exception = new MesErrorCodeException(BaseErrorCode.DATA_HAS_OBSOLETE.getCode());
            log.error("{}", exception.getMessage(), exception);
            throw exception;
        }
    }

    /**
     * Domain中Entity的 废弃校验
     */
    public static <V extends BaseEntity, D extends AbstractDomain<V>> void checkObsoleteFlagY(D d) {
        V entity = d.getEntity();
        if (null == entity) {
            return;
        }
        ObsoleteEntity validEntity = new ObsoleteEntity();
        if (!BeanUtils.ClassFieldContain(entity.getClass(), ObsoleteEntity.class)) {
            return;
        }
        BeanUtils.copyProperties(entity, validEntity);
        if (!"Y".equals(validEntity.getObsoleteFlag())) {
            MesErrorCodeException exception = new MesErrorCodeException(BaseErrorCode.DATA_HAS_NOT_OBSOLETE.getCode());
            log.error("{}", exception.getMessage(), exception);
            throw exception;
        }
    }
    
    /**
     * Domain中Entity的 废弃信息填写
     */
    public static <V extends BaseEntity, D extends AbstractDomain<V>> void setObsoleteInfo(D d) {
        V entity = d.getEntity();
        if (null == entity) {
            return;
        }
        ObsoleteEntity obsoleteEntity = new ObsoleteEntity();
        if (!BeanUtils.ClassFieldContain(entity.getClass(), ObsoleteEntity.class)) {
            return;
        }
        BeanUtils.copyProperties(entity, obsoleteEntity);
        obsoleteEntity.setObsoleteFlag("Y");
        obsoleteEntity.setObsoleteAccountIdentifier(BizParamUtils.getUserId());
        obsoleteEntity.setObsoleteAccountName(BizParamUtils.getLastEventUser());
        obsoleteEntity.setObsoleteTime(BizParamUtils.getLastEventTime());
        obsoleteEntity.setObsoleteReasonText((String) BizParamUtils.getParam("obsoleteReasonText"));
        BeanUtils.copyProperties(obsoleteEntity, entity);
        d.setAuditField();
    }
    /**
     * 对Domain做废弃操作
     *
     * @param pk      实体的PK。
     * @param service 主表数据对应的service的实例化对象
     * @param <V>     主表数据类型
     * @param <PK>    主表数据主键类型
     * @param <S>     主表数据对应的service类型
     */
    public static <V extends BaseEntity, D extends AbstractDomain<V>, PK extends Serializable, S extends BaseDomainService<V, D, PK>> D obsolete(PK pk, S service) {
        D d = service.checkExistById(pk);
        setObsoleteInfo(d);
        service.update(d);
        return d;
    }
}