package cec.jiutian.core.util;

import cec.jiutian.core.base.BaseService;
import cec.jiutian.core.entity.AbstractDomain;
import cec.jiutian.core.exception.MesErrorCodeException;
import cec.jiutian.security.base.entity.BaseEntity;
import cec.jiutian.core.comn.errorCode.BaseErrorCode;
import cec.jiutian.core.comn.util.BeanUtils;
import lombok.extern.slf4j.Slf4j;

import javax.persistence.GeneratedValue;
import java.io.Serializable;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 实体基类的工具类。目前主要用于简单逻辑、单表单数据的审计字段录入和历史记录录入。
 *
 * <AUTHOR>
 * @date 2020/11/13
 */
@Slf4j
public class BaseEntityUtils {
    private static final ThreadLocal<String> SERVICE_METHOD_THREAD_LOCAL = new ThreadLocal<>();
    private static final BatchThreadLocal IS_BATCH = new BatchThreadLocal();
    private static final ThreadLocal<Map<String, List<Object>>> SAVE_LIST = new ThreadLocal<>();
    private static final ThreadLocal<Map<String, List<Object>>> UPDATE_LIST = new ThreadLocal<>();
    private static final ThreadLocal<Map<String, List<Object>>> DELETE_LIST = new ThreadLocal<>();
    private static final ThreadLocal<List<String>> AUTO_INCREMENT_ENTITY = new ThreadLocal<>();

    public static String getServiceMethodName() {
        return SERVICE_METHOD_THREAD_LOCAL.get();
    }

    public static void setServiceMethodName(String name) {
        SERVICE_METHOD_THREAD_LOCAL.set(name);
    }

    public static void setIsBatch(boolean isBatch) {
        IS_BATCH.set(isBatch);
    }

    public static void clearThreadVariable() {
        SAVE_LIST.remove();
        UPDATE_LIST.remove();
        DELETE_LIST.remove();
        IS_BATCH.remove();
        SERVICE_METHOD_THREAD_LOCAL.remove();
        AUTO_INCREMENT_ENTITY.remove();
    }

    public static Boolean isBatch() {
        return IS_BATCH.get();
    }

    private static void collectPersistentData(Object t, ThreadLocal<Map<String, List<Object>>> list) {
        Class<?> aClass = t.getClass();
        String simpleName = aClass.getSimpleName();
        Map<String, List<Object>> objectListMap = list.get();
        if (objectListMap == null) {
            objectListMap = new HashMap<>();
            List<Object> objects = new ArrayList<>();
            objects.add(t);
            objectListMap.put(simpleName, objects);
            list.set(objectListMap);
        } else {
            List<Object> entities = objectListMap.get(simpleName);
            if (entities == null) {
                List<Object> objects = new ArrayList<>();
                objects.add(t);
                objectListMap.put(simpleName, objects);
            } else {
                entities.add(t);
            }
        }
    }

    public static Boolean batchPersistent() throws Exception {
        boolean result = batchPersistent(SAVE_LIST, "insertBatch") && batchPersistent(UPDATE_LIST, "updateBatch")
                && batchPersistent(DELETE_LIST, "deleteBatchByPrimaryKey");
        clearThreadVariable();
        return result;
    }

    @SuppressWarnings({"rawtypes", "unchecked"})
    public static Boolean batchPersistent(ThreadLocal<Map<String, List<Object>>> list, String methodName) throws Exception {
        Map<String, List<Object>> stringListMap = list.get();
        if (stringListMap != null) {
            for (Map.Entry<String, List<Object>> entry : stringListMap.entrySet()) {
                String k = entry.getKey();
                List<Object> v = entry.getValue().stream().distinct().collect(Collectors.toList());
                int result;
                try {
                    result = batchOperationByEntityNameAndMethodName(k, methodName, v);
                    Class<?> aClass = v.get(0).getClass();
                    if (AUTO_INCREMENT_ENTITY.get() != null && AUTO_INCREMENT_ENTITY.get().contains(aClass.getSimpleName()) && "insertBatch".equals(methodName)) {
                        Class<?> history = Class.forName(aClass.getName() + "History");
                        List historyList = new ArrayList();
                        for (Object o : v) {
                            Object tHistory = history.newInstance();
                            BeanUtils.copyProperties(o, tHistory);
                            historyList.add(tHistory);
                        }
                        String simpleName = history.getSimpleName();
                        batchOperationByEntityNameAndMethodName(simpleName, "insertBatch", historyList);
                        AUTO_INCREMENT_ENTITY.get().remove(aClass.getSimpleName());
                    }
                } catch (IllegalAccessException | InvocationTargetException e) {
                    if (e instanceof InvocationTargetException) {
                        Throwable throwable = ((InvocationTargetException) e).getTargetException();
                        Exception exception = new Exception(throwable);
                        log.error("{}", exception.getMessage(), exception);
                        throw exception;
                    } else {
                        log.error("{}", e.getMessage(), e);
                        throw e;
                    }
                }
                if (result != v.size()) {
                    return false;
                }
            }
        }
        return true;
    }

    private static int batchOperationByEntityNameAndMethodName(String entityName, String methodName, List<Object> objects) throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        String first = entityName.replaceFirst(String.valueOf(entityName.charAt(0)), String.valueOf(Character.toLowerCase(entityName.charAt(0))));
        Object mapper = SpringContextUtils.getBean(first + "Mapper");
        Method method;
        method = mapper.getClass().getMethod(methodName, List.class);
        return (int) method.invoke(mapper, objects);
    }

    /**
     * 默认的数据库插入持久化方法，自动填充审计字段和历史记录。该方法默认主表有相关历史记录，并执行历史记录的插入操作，默认的最后操作事件名为Create
     *
     * @param t       主表原数据。该数据应该为通过业务检查的数据。
     * @param dto     更新主表数据的dto，如果dto的数据需要处理，则该数据应该为逻辑处理后的数据。
     * @param service 主表数据对应的service的实例化对象
     * @param <T>     主表数据类型
     * @param <PK>    主表数据主键类型
     * @param <S>     主表数据对应的service类型
     * @return 数据库持久化操作结果。
     */
    public static <T extends BaseEntity, PK extends Serializable, S extends BaseService<T, PK>> Boolean save(T t, Object dto, S service) {
        return save(t, dto, "Create", service);
    }

    /**
     * 默认的数据库插入持久化方法，自动填充审计字段和历史记录。该方法默认主表有相关历史记录，并执行历史记录的插入操作，默认的最后操作事件名为Create，且无dto赋值操作
     *
     * @param t       主表原数据。该数据应该为通过业务检查的数据。
     * @param service 主表数据对应的service的实例化对象
     * @param <T>     主表数据类型
     * @param <PK>    主表数据主键类型
     * @param <S>     主表数据对应的service类型
     * @return 数据库持久化操作结果。
     */
    public static <T extends BaseEntity, PK extends Serializable, S extends BaseService<T, PK>> Boolean save(T t, S service) {
        return save(t, null, "Create", service);
    }

    /**
     * 默认的数据库更新持久化方法，自动填充审计字段和历史记录。该方法默认主表有相关历史记录，并执行历史记录的插入操作，默认的最后事件名为Update
     *
     * @param t       主表原数据。该数据应该为通过业务检查的数据。
     * @param dto     更新主表数据的dto，如果dto的数据需要处理，则该数据应该为逻辑处理后的数据。
     * @param service 主表数据对应的service的实例化对象
     * @param <T>     主表数据类型
     * @param <PK>    主表数据主键类型
     * @param <S>     主表数据对应的service类型
     * @return 数据库持久化操作结果。
     */
    public static <T extends BaseEntity, PK extends Serializable, S extends BaseService<T, PK>> Boolean update(T t, Object dto, S service) {
        return update(t, dto, "Update", service);
    }

    /**
     * 默认的数据库更新持久化方法，自动填充审计字段和历史记录。该方法默认主表有相关历史记录，并执行历史记录的插入操作，默认的最后事件名为Update，且无dto赋值操作
     *
     * @param t       主表原数据。该数据应该为通过业务检查的数据。
     * @param service 主表数据对应的service的实例化对象
     * @param <T>     主表数据类型
     * @param <PK>    主表数据主键类型
     * @param <S>     主表数据对应的service类型
     * @return 数据库持久化操作结果。
     */
    public static <T extends BaseEntity, PK extends Serializable, S extends BaseService<T, PK>> Boolean update(T t, S service) {
        return update(t, null, "Update", service);
    }

    /**
     * 默认的数据库删除持久化方法，自动填充审计字段和历史记录。该方法默认主表有相关历史记录，并执行历史记录的插入操作，默认的最后事件名为Delete
     *
     * @param t       主表原数据。该数据应该为通过业务检查的数据。
     * @param dto     更新主表数据的dto，如果dto的数据需要处理，则该数据应该为逻辑处理后的数据。
     * @param service 主表数据对应的service的实例化对象
     * @param <T>     主表数据类型
     * @param <PK>    主表数据主键类型
     * @param <S>     主表数据对应的service类型
     * @return 数据库持久化操作结果。
     */
    public static <T extends BaseEntity, PK extends Serializable, S extends BaseService<T, PK>> Boolean delete(T t, Object dto, S service) {
        return delete(t, dto, "Delete", service);
    }

    /**
     * 默认的数据库删除持久化方法，自动填充审计字段和历史记录。该方法默认主表有相关历史记录，并执行历史记录的插入操作，默认的最后事件名为Delete，且无dto赋值操作
     *
     * @param t       主表原数据。该数据应该为通过业务检查的数据。
     * @param service 主表数据对应的service的实例化对象
     * @param <T>     主表数据类型
     * @param <PK>    主表数据主键类型
     * @param <S>     主表数据对应的service类型
     * @return 数据库持久化操作结果。
     */
    public static <T extends BaseEntity, PK extends Serializable, S extends BaseService<T, PK>> Boolean delete(T t, S service) {
        return delete(t, null, "Delete", service);
    }

    /**
     * 默认的数据库插入持久化方法，自动填充审计字段和历史记录。该方法默认主表有相关历史记录，并执行历史记录的插入操作
     *
     * @param tList         主表原数据集合。该数据应该为通过业务检查的数据。
     * @param dto           更新主表数据的dto，如果dto的数据需要处理，则该数据应该为逻辑处理后的数据。
     * @param lastEventName 最后事件名称
     * @param service       主表数据对应的service的实例化对象
     * @param <T>           主表数据类型
     * @param <PK>          主表数据主键类型
     * @param <S>           主表数据对应的service类型
     * @return 数据库持久化操作结果。
     */
    public static <T extends BaseEntity, PK extends Serializable, S extends BaseService<T, PK>> Boolean saveBatch(List<T> tList, Object dto, String lastEventName, S service) {
        return batchPersistentOperation(tList, dto, lastEventName, service, Operation.SAVE, true);
    }

    /*
     * 默认的数据库插入持久化方法，自动填充审计字段和历史记录。可选择是否保存历史表
     **/
    public static <T extends BaseEntity, PK extends Serializable, S extends BaseService<T, PK>> Boolean saveBatch(List<T> tList, Object dto, String lastEventName, S service, Boolean isHistory) {
        return batchPersistentOperation(tList, dto, lastEventName, service, Operation.SAVE, isHistory);
    }

    /**
     * 默认的数据库更新持久化方法，自动填充审计字段和历史记录。该方法默认主表有相关历史记录，并执行历史记录的插入操作
     *
     * @param tList         主表原数据集合。该数据应该为通过业务检查的数据。
     * @param dto           更新主表数据的dto，如果dto的数据需要处理，则该数据应该为逻辑处理后的数据。
     * @param lastEventName 最后事件名称
     * @param service       主表数据对应的service的实例化对象
     * @param <T>           主表数据类型
     * @param <PK>          主表数据主键类型
     * @param <S>           主表数据对应的service类型
     * @return 数据库持久化操作结果。
     */
    public static <T extends BaseEntity, PK extends Serializable, S extends BaseService<T, PK>> Boolean updateBatch(List<T> tList, Object dto, String lastEventName, S service) {
        return batchPersistentOperation(tList, dto, lastEventName, service, Operation.UPDATE, true);
    }

    /**
     * 默认的数据库更新持久化方法，自动填充审计字段和历史记录。可选择是否保存历史表
     */
    public static <T extends BaseEntity, PK extends Serializable, S extends BaseService<T, PK>> Boolean updateBatch(List<T> tList, Object dto, String lastEventName, S service, Boolean isHistory) {
        return batchPersistentOperation(tList, dto, lastEventName, service, Operation.UPDATE, isHistory);
    }

    /**
     * 默认的数据库删除持久化方法，自动填充审计字段和历史记录。该方法默认主表有相关历史记录，并执行历史记录的插入操作
     *
     * @param tList         主表原数据集合。该数据应该为通过业务检查的数据。
     * @param dto           更新主表数据的dto，如果dto的数据需要处理，则该数据应该为逻辑处理后的数据。
     * @param lastEventName 最后事件名称
     * @param service       主表数据对应的service的实例化对象
     * @param <T>           主表数据类型
     * @param <PK>          主表数据主键类型
     * @param <S>           主表数据对应的service类型
     * @return 数据库持久化操作结果。
     */
    public static <T extends BaseEntity, PK extends Serializable, S extends BaseService<T, PK>> Boolean deleteBatch(List<T> tList, Object dto, String lastEventName, S service) {
        return batchPersistentOperation(tList, dto, lastEventName, service, Operation.DELETE, true);
    }

    /**
     * 默认的数据库删除持久化方法，自动填充审计字段和历史记录。可选择是否保存历史表
     */
    public static <T extends BaseEntity, PK extends Serializable, S extends BaseService<T, PK>> Boolean deleteBatch(List<T> tList, Object dto, String lastEventName, S service, Boolean isHistory) {
        return batchPersistentOperation(tList, dto, lastEventName, service, Operation.DELETE, isHistory);
    }

    /**
     * 默认的数据库插入持久化方法，自动填充审计字段和历史记录。自定义是否保留履历
     *
     * @param t             主表原数据。该数据应该为通过业务检查的数据。
     * @param dto           更新主表数据的dto，如果dto的数据需要处理，则该数据应该为逻辑处理后的数据。
     * @param lastEventName 最后事件名称
     * @param service       主表数据对应的service的实例化对象
     * @param <T>           主表数据类型
     * @param <PK>          主表数据主键类型
     * @param <S>           主表数据对应的service类型
     * @param isHistory     是否保留履历
     * @return 数据库持久化操作结果。
     */
    public static <T extends BaseEntity, PK extends Serializable, S extends BaseService<T, PK>> Boolean save(T t, Object dto, String lastEventName, S service, Boolean isHistory) {
        return persistentOperation(t, dto, lastEventName, service, Operation.SAVE, isHistory);
    }

    /**
     * 默认的数据库插入持久化方法，自动填充审计字段和历史记录。该方法默认主表有相关历史记录，并执行历史记录的插入操作
     *
     * @param t             主表原数据。该数据应该为通过业务检查的数据。
     * @param dto           更新主表数据的dto，如果dto的数据需要处理，则该数据应该为逻辑处理后的数据。
     * @param lastEventName 最后事件名称
     * @param service       主表数据对应的service的实例化对象
     * @param <T>           主表数据类型
     * @param <PK>          主表数据主键类型
     * @param <S>           主表数据对应的service类型
     * @return 数据库持久化操作结果。
     */
    public static <T extends BaseEntity, PK extends Serializable, S extends BaseService<T, PK>> Boolean save(T t, Object dto, String lastEventName, S service) {
        return persistentOperation(t, dto, lastEventName, service, Operation.SAVE, true);
    }

    /**
     * 默认的数据库更新持久化方法，自动填充审计字段和历史记录。可选择是否插入历史表
     *
     * @param t             主表原数据。该数据应该为通过业务检查的数据。
     * @param dto           更新主表数据的dto，如果dto的数据需要处理，则该数据应该为逻辑处理后的数据。
     * @param lastEventName 最后事件名称
     * @param service       主表数据对应的service的实例化对象
     * @param <T>           主表数据类型
     * @param <PK>          主表数据主键类型
     * @param <S>           主表数据对应的service类型
     * @return 数据库持久化操作结果。
     */
    public static <T extends BaseEntity, PK extends Serializable, S extends BaseService<T, PK>> Boolean update(T t, Object dto, String lastEventName, S service, Boolean isHisory) {
        return persistentOperation(t, dto, lastEventName, service, Operation.UPDATE, isHisory);
    }

    /**
     * 默认的数据库更新持久化方法，自动填充审计字段和历史记录。该方法默认主表有相关历史记录，并执行历史记录的插入操作
     *
     * @param t             主表原数据。该数据应该为通过业务检查的数据。
     * @param dto           更新主表数据的dto，如果dto的数据需要处理，则该数据应该为逻辑处理后的数据。
     * @param lastEventName 最后事件名称
     * @param service       主表数据对应的service的实例化对象
     * @param <T>           主表数据类型
     * @param <PK>          主表数据主键类型
     * @param <S>           主表数据对应的service类型
     * @return 数据库持久化操作结果。
     */
    public static <T extends BaseEntity, PK extends Serializable, S extends BaseService<T, PK>> Boolean update(T t, Object dto, String lastEventName, S service) {
        return persistentOperation(t, dto, lastEventName, service, Operation.UPDATE, true);
    }

    /**
     * 默认的数据库删除持久化方法，自动填充审计字段和历史记录。该方法默认主表有相关历史记录，并执行历史记录的插入操作
     *
     * @param t             主表原数据。该数据应该为通过业务检查的数据。
     * @param dto           更新主表数据的dto，如果dto的数据需要处理，则该数据应该为逻辑处理后的数据。
     * @param lastEventName 最后事件名称
     * @param service       主表数据对应的service的实例化对象
     * @param <T>           主表数据类型
     * @param <PK>          主表数据主键类型
     * @param <S>           主表数据对应的service类型
     * @return 数据库持久化操作结果。
     */
    public static <T extends BaseEntity, PK extends Serializable, S extends BaseService<T, PK>> Boolean delete(T t, Object dto, String lastEventName, S service) {
        return persistentOperation(t, dto, lastEventName, service, Operation.DELETE, true);
    }

    /**
     * 默认的数据库删除持久化方法，自动填充审计字段和历史记录。该方法默认主表有相关历史记录，并执行历史记录的插入操作
     *
     * @param t             主表原数据。该数据应该为通过业务检查的数据。
     * @param dto           更新主表数据的dto，如果dto的数据需要处理，则该数据应该为逻辑处理后的数据。
     * @param lastEventName 最后事件名称
     * @param service       主表数据对应的service的实例化对象
     * @param <T>           主表数据类型
     * @param <PK>          主表数据主键类型
     * @param <S>           主表数据对应的service类型
     * @return 数据库持久化操作结果。
     */
    public static <T extends BaseEntity, PK extends Serializable, S extends BaseService<T, PK>> Boolean delete(T t, Object dto, String lastEventName, S service, Boolean isHistory) {
        return persistentOperation(t, dto, lastEventName, service, Operation.DELETE, isHistory);
    }

    /**
     * 默认的持久化方法，自动填充审计字段和历史记录。该方法默认主表有相关历史记录，并执行历史记录的插入操作
     *
     * @param t             主表原数据。该数据应该为通过业务检查的数据。
     * @param dto           更新主表数据的dto，如果dto的数据需要处理，则该数据应该为逻辑处理后的数据。
     * @param lastEventName 最后事件名称
     * @param service       主表数据对应的service的实例化对象
     * @param operation     持久化操作
     * @param <T>           主表数据类型
     * @param <PK>          主表数据主键类型
     * @param <S>           主表数据对应的service类型
     * @return 数据库持久化操作结果。
     */
    public static <T extends BaseEntity, PK extends Serializable, S extends BaseService<T, PK>> Boolean persistentOperation(T t, Object dto, String lastEventName, S service, Operation operation) {
        return persistentOperation(t, dto, lastEventName, service, operation, true);
    }

    /**
     * 数据库持久化方法，自动填充审计字段和历史记录。该方法根据isHistory判断主表是否有历史表
     *
     * @param isHistory     判断主表是否有历史表
     * @param t             主表原数据。该数据应该为通过业务检查的数据。
     * @param dto           更新主表数据的dto，如果dto的数据需要处理，则该数据应该为逻辑处理后的数据。
     * @param lastEventName 最后事件名称
     * @param service       主表数据对应的service的实例化对象
     * @param operation     持久化操作
     * @param <T>           主表数据类型
     * @param <PK>          主表数据主键类型
     * @param <S>           主表数据对应的service类型
     * @return 数据库持久化操作结果。
     */
    public static <T extends BaseEntity, PK extends Serializable, S extends BaseService<T, PK>> Boolean persistentOperation(T t, Object dto, String lastEventName, S service, Operation operation, boolean isHistory) {
        try {
            setFields(t, dto, lastEventName);
            int result = 1;
            switch (operation) {
                case SAVE:
                    if (IS_BATCH.get()) {
                        collectPersistentData(t, SAVE_LIST);
                    } else {
                        result = service.save(t);
                    }
                    break;
                case UPDATE:
                    if (IS_BATCH.get()) {
                        collectPersistentData(t, UPDATE_LIST);
                    } else {
                        result = service.update(t);
                    }
                    break;
                case DELETE:
                    if (IS_BATCH.get()) {
                        collectPersistentData(t, DELETE_LIST);
                    } else {
                        result = service.deleteBatchByPrimaryKey(Collections.singletonList(t));
                    }
                    break;
                default:
                    return false;
            }
            int saveHistory = 1;
            if (isHistory) {
                Class<? extends BaseEntity> aClass = t.getClass();
                Class<?> history = Class.forName(aClass.getName() + "History");
                Object tHistory = history.newInstance();
                BeanUtils.copyProperties(t, tHistory);
                if (IS_BATCH.get()) {
                    // 如果主表中是自增主键，则此处不构建历史表数据，后面进行批量插入后再构建数据。
                    Field[] declaredFields = aClass.getDeclaredFields();
                    boolean collectFlag = true;
                    for (Field declaredField : declaredFields) {
                        if (declaredField.isAnnotationPresent(GeneratedValue.class)) {
                            collectFlag = false;
                            break;
                        }
                    }
                    if (collectFlag) {
                        collectPersistentData(tHistory, SAVE_LIST);
                    } else {
                        List<String> autoIncrementEntities = AUTO_INCREMENT_ENTITY.get();
                        if (autoIncrementEntities == null) {
                            autoIncrementEntities = new ArrayList<>();
                            autoIncrementEntities.add(aClass.getSimpleName());
                            AUTO_INCREMENT_ENTITY.set(autoIncrementEntities);
                        } else {
                            autoIncrementEntities.add(aClass.getSimpleName());
                        }
                    }
                } else {
                    // 使用默认bean的名称查找bean
                    String simpleName = history.getSimpleName();
                    String first = simpleName.replaceFirst(String.valueOf(simpleName.charAt(0)), String.valueOf(Character.toLowerCase(simpleName.charAt(0))));
                    Object historyMapper = SpringContextUtils.getBean(first + "Mapper");
                    Method historySave = historyMapper.getClass().getMethod("insertSelective", Object.class);
                    saveHistory = (int) historySave.invoke(historyMapper, tHistory);
                }
            }
            return IS_BATCH.get() || (saveHistory == 1 && result == 1);
        } catch (ClassNotFoundException | NoSuchMethodException | IllegalAccessException | InstantiationException | InvocationTargetException e) {
            MesErrorCodeException exception = new MesErrorCodeException(e, BaseErrorCode.EXCEPTION.getCode());
            log.error("{}", exception.getMessage(), exception);
            throw exception;
        }
    }

    /**
     * 自动填充审计字段和需要更新的值。
     *
     * @param t             需要填充的对象
     * @param dto           包含了最后操作人和备注的DTO
     * @param lastEventName 最后事件名称
     * @param <T>           需要填充的对象类型，继承自{@link BaseEntity}
     */
    public static <T extends BaseEntity> void setFields(T t, Object dto, String lastEventName, String... ignoreProperties) {
        if (dto != null) {
            BeanUtils.copyProperties(dto, t, ignoreProperties);
        }
        if (BizParamUtils.getLastEventUser() != null) {
            t.setLastEventUser(BizParamUtils.getLastEventUser());
        }
        if (BizParamUtils.getLastEventComment() != null) {
            t.setLastEventComment(BizParamUtils.getLastEventComment());
        }
        if (BizParamUtils.getOperationName() != null) {
            t.setLastEventName(BizParamUtils.getOperationName());
        }
        // TODO: huay 2020/11/17 时间可以采用数据库时间，通过接口请求数据库
        if (t.getCreateTime() == null) {
            t.setCreateTime(BizParamUtils.getLastEventTime());
            t.setCreateUser(t.getLastEventUser());
        }
        t.setLastEventTime(BizParamUtils.getLastEventTime());
        // operationName的优先级大于该参数
        // t.setLastEventName(lastEventName);
    }

    /**
     * 数据库批量持久化方法，自动填充审计字段和历史记录。该方法根据isHistory判断主表是否有历史表
     *
     * @param isHistory     判断主表是否有历史表
     * @param tList         主表原数据。该数据应该为通过业务检查的数据。并已完成赋值
     * @param dto           需要统一赋值的内容主要为审计字段，为空不赋值
     * @param lastEventName 最后事件名称
     * @param service       主表数据对应的service的实例化对象
     * @param operation     持久化操作
     * @param <T>           主表数据类型
     * @param <PK>          主表数据主键类型
     * @param <S>           主表数据对应的service类型
     * @return 数据库持久化操作结果。
     */
    public static <T extends BaseEntity, PK extends Serializable, S extends BaseService<T, PK>> Boolean batchPersistentOperation(List<T> tList, Object dto, String lastEventName, S service, Operation operation, boolean isHistory) {
        if (null == tList || tList.size() == 0) {
            return false;
        }
        try {
            tList.forEach(
                    t -> setNotNullFields(t, dto, lastEventName)
            );
            int result;
            switch (operation) {
                case SAVE:
                    result = service.insertBatch(tList);
                    break;
                case UPDATE:
                    result = service.updateBatch(tList);
                    break;
                case DELETE:
                    result = service.deleteBatchByPrimaryKey(tList);
                    break;
                default:
                    return false;
            }
            int saveHistory = tList.size();
            if (isHistory) {
                Class<? extends BaseEntity> aClass = tList.get(0).getClass();
                Class<?> history = Class.forName(aClass.getName() + "History");
                List<Object> tHistoryList = new ArrayList<>();
                for (T t : tList) {
                    Object tHistory = history.newInstance();
                    BeanUtils.copyProperties(t, tHistory);
                    tHistoryList.add(tHistory);
                }
                // 使用默认bean的名称查找bean
                String simpleName = history.getSimpleName();
                String first = simpleName.replaceFirst(String.valueOf(simpleName.charAt(0)), String.valueOf(Character.toLowerCase(simpleName.charAt(0))));
                Object historyMapper = SpringContextUtils.getBean(first + "Mapper");
                Method historySave = historyMapper.getClass().getMethod("insertBatch", List.class);
                saveHistory = (int) historySave.invoke(historyMapper, tHistoryList);
            }
            return saveHistory == tList.size() && result == tList.size();
        } catch (ClassNotFoundException | NoSuchMethodException | IllegalAccessException | InstantiationException | InvocationTargetException e) {
            MesErrorCodeException exception = new MesErrorCodeException(e, BaseErrorCode.EXCEPTION.getCode());
            log.error("{}", exception.getMessage(), exception);
            throw exception;
        }
    }

    /**
     * 自动填充审计字段和需要更新的非空的值。
     *
     * @param t             需要填充的对象
     * @param dto           包含了最后操作人和备注的DTO
     * @param lastEventName 最后事件名称
     * @param <T>           需要填充的对象类型，继承自{@link BaseEntity}
     */
    public static <T extends BaseEntity> void setNotNullFields(T t, Object dto, String lastEventName) {
        if (dto != null) {
            BeanUtils.copyNotNullProperties(dto, t);
        }
        if (BizParamUtils.getLastEventUser() != null) {
            t.setLastEventUser(BizParamUtils.getLastEventUser());
        }
        if (BizParamUtils.getLastEventComment() != null) {
            t.setLastEventComment(BizParamUtils.getLastEventComment());
        }
        if (BizParamUtils.getOperationName() != null) {
            t.setLastEventName(BizParamUtils.getOperationName());
        }
        if (t.getCreateTime() == null) {
            t.setCreateTime(BizParamUtils.getLastEventTime());
            t.setCreateUser(t.getLastEventUser());
        }
        t.setLastEventTime(BizParamUtils.getLastEventTime());
        // operationName的优先级大于该参数
        // t.setLastEventName(lastEventName);
    }

    public static <E extends BaseEntity, T extends AbstractDomain<E>> void save() {

    }

    /**
     * 数据库持久化操作枚举，表示创建修改和删除操作
     */
    public enum Operation {
        SAVE,
        UPDATE,
        DELETE
    }

    public static class BatchThreadLocal extends ThreadLocal<Boolean> {
        @Override
        protected Boolean initialValue() {
            return false;
        }
    }
}
