package cec.jiutian.core.util;

import cec.jiutian.core.comn.util.StringUtils;

import java.text.NumberFormat;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @description: 返回非空Number 为空是返回0
 * @author: <EMAIL>
 */
public class NumberUtil {
    public static <T extends Number> T getNotNullNumber(T t, Class<T> tClass) {
        Object result = new Object();
        Double dblValue;
        if (t == null) {
            dblValue = new Double(0D);
        } else {
            return t;
        }
        switch (tClass.getTypeName()) {
            case "java.lang.Byte":
                result = dblValue.byteValue();
                break;
            case "java.lang.Short":
                result = dblValue.shortValue();
                break;
            case "java.lang.Integer":
                result = dblValue.intValue();
                break;
            case "java.lang.Long":
                result = dblValue.longValue();
                break;
            case "java.lang.Float":
                result = dblValue.floatValue();
                break;
            case "java.lang.Double":
                result = dblValue;
                break;
            default:
        }
        return (T) result;
    }

    public static <T extends Number> T getNumberByString(String str, Class<T> tClass) {
        Object result = new Object();
        Double dblValue;
        if (str == null) {
            dblValue = new Double(0D);
        } else {
            Pattern p = Pattern.compile("[^0-9]");
            Matcher m = p.matcher(str);
            String numStr = m.replaceAll("").trim();
            dblValue = Double.parseDouble(numStr);
        }
        switch (tClass.getTypeName()) {
            case "java.lang.Byte":
                result = dblValue.byteValue();
                break;
            case "java.lang.Short":
                result = dblValue.shortValue();
                break;
            case "java.lang.Integer":
                result = dblValue.intValue();
                break;
            case "java.lang.Long":
                result = dblValue.longValue();
                break;
            case "java.lang.Float":
                result = dblValue.floatValue();
                break;
            case "java.lang.Double":
                result = dblValue;
                break;
            default:
        }
        return (T) result;
    }

    /*
     * @Description 对版本做升级处理，适用于0001等中间无分隔符的版本
     * @param version 原版本
     * @return 升级后的版本
     **/
    public static String versionUpgrade(String version) {
        if (null == version) {
            return null;
        }
        return versionUpgrade(version, version.length());
    }

    public static String versionUpgrade(String version, int formatLength) {
        if (null == version) {
            return null;
        }
        int intValue = 0;
        String regularizeVersion = removeLeftZero(version);
        if (StringUtils.isNotEmpty(regularizeVersion)) {
            intValue = Integer.parseInt(regularizeVersion);
        }
        ++intValue;
        NumberFormat nf = NumberFormat.getInstance();
        nf.setGroupingUsed(false);
        nf.setMaximumIntegerDigits(formatLength);
        nf.setMinimumIntegerDigits(formatLength);
        return nf.format(intValue);
    }

    public static String removeLeftZero(String str) {
        if (null == str) {
            return null;
        } else {
            return str.replaceAll("^(0+)", "");
        }
    }

    public static String removeRightZero(String str) {
        if (null == str) {
            return null;
        } else {
            return str.replaceAll("0*$", "");
        }
    }

    /*
     * @Description //获取字符中的所有数字
     **/
    public static String getStringNum(String str) {
        String regEx = "[^0-9]";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(str);
        return m.replaceAll("").trim();
    }
}
