package cec.jiutian.core.util;

import cec.jiutian.core.exception.MesErrorCodeException;
import cec.jiutian.core.base.BaseService;
import cec.jiutian.core.comn.errorCode.BaseErrorCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.Serializable;

/**
 * @author: <EMAIL>
 * @date: 2021-6-21 18:18
 */
@Slf4j
@Component
public class EntityCheckUtils {
    /**
     * 实体存在检查，若存在则返回已存在的实体，不存在则抛错
     *
     * @param pk      实体的PK。
     * @param service 主表数据对应的service的实例化对象
     * @param <T>     主表数据类型
     * @param <PK>    主表数据主键类型
     * @param <S>     主表数据对应的service类型
     */
    public static <T extends Serializable, PK extends Serializable, S extends BaseService<T, PK>> T checkExist(PK pk, S service) {
        T t = service.getById(pk);
        if (t == null) {
            String className = service.getClass().getName();
            String serviceName = className.substring(className.indexOf(".service.") + 9, className.indexOf("Service"));
            MesErrorCodeException exception = new MesErrorCodeException(BaseErrorCode.DATA_NOT_EXIST.getCode())
                    .addMsgItem(serviceName + ": " + pk.toString());
            log.error("{}", exception.getMessage(), exception);
            throw exception;
        }
        return t;
    }

    /**
     * 实体唯一性检查，若存在则抛错
     *
     * @param pk      实体的PK。
     * @param service 主表数据对应的service的实例化对象
     * @param <T>     主表数据类型
     * @param <PK>    主表数据主键类型
     * @param <S>     主表数据对应的service类型
     */
    public static <T extends Serializable, PK extends Serializable, S extends BaseService<T, PK>> void checkUnique(PK pk, S service) {
        T t = service.getById(pk);
        if (t != null) {
            String className = service.getClass().getName();
            String serviceName = className.substring(className.indexOf(".service.") + 9, className.indexOf("Service"));
            MesErrorCodeException exception = new MesErrorCodeException(BaseErrorCode.DATA_EXISTED.getCode())
                    .addMsgItem(serviceName + ": " + pk.toString());
            log.error("{}", exception.getMessage(), exception);
            throw exception;
        }
    }
}
