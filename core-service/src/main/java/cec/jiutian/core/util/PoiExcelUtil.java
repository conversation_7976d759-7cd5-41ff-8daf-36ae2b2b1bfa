package cec.jiutian.core.util;

import cec.jiutian.core.comn.errorCode.BaseErrorCode;
import cec.jiutian.core.comn.util.ExcelUtil;
import cec.jiutian.core.exception.MesErrorCodeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.util.DigestUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class PoiExcelUtil {

    public static void inspectExcelRowRepeatData(Sheet sheet) {
        List<String> md5List = new ArrayList<>();
        for (int i = sheet.getFirstRowNum() + 1; i < sheet.getLastRowNum(); i++) {
            StringBuilder sb = new StringBuilder();
            for (Cell cell : sheet.getRow(i)) {
                cell.setCellType(Cell.CELL_TYPE_STRING);
                sb.append(cell.getStringCellValue());
            }
            if (StringUtils.isNotBlank(sb.toString())) {
                md5List.add(DigestUtils.md5DigestAsHex(sb.toString().getBytes()));
            }
        }

        List<String> streamList = md5List.stream().collect(Collectors.toList());
        if (streamList.size() != streamList.stream().distinct().count()) {
            MesErrorCodeException exception = new MesErrorCodeException(BaseErrorCode.IMPORTED_EXCEL_DUPLICATE_DATA.getCode());
            log.error("{}", exception.getMessage(), exception);
            throw exception;
        }
    }

    public static Workbook getExcelWorkbook(MultipartFile file) {
        if (null == file || file.isEmpty()) {
            MesErrorCodeException exception = new MesErrorCodeException(BaseErrorCode.UPLOADING_NO_FILES.getCode());
            log.error("{}", exception.getMessage(), exception);
            throw exception;
        }
        Workbook workbook = null;
        try {
            workbook = ExcelUtil.createCommonWorkbook(file.getInputStream());
        } catch (IOException | InvalidFormatException e) {
            MesErrorCodeException exception = new MesErrorCodeException(BaseErrorCode.NO_DATA.getCode());
            log.error("{}", exception.getMessage(), exception);
            throw exception;
        }

        if (null == workbook) {
            MesErrorCodeException exception = new MesErrorCodeException(BaseErrorCode.NO_DATA.getCode());
            log.error("{}", exception.getMessage(), exception);
            throw exception;
        }
        return workbook;
    }

}
