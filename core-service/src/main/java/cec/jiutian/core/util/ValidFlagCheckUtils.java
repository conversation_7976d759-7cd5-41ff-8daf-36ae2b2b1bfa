package cec.jiutian.core.util;

import cec.jiutian.core.entity.AbstractDomain;
import cec.jiutian.core.exception.MesErrorCodeException;
import cec.jiutian.security.base.entity.BaseEntity;
import cec.jiutian.security.base.entity.ValidEntity;
import cec.jiutian.core.base.BaseDomainService;
import cec.jiutian.core.comn.errorCode.BaseErrorCode;
import cec.jiutian.core.comn.util.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.Serializable;

/*
 * <AUTHOR>
 * @Date 15:14 2022-4-28
 **/
@Slf4j
@Component
public class ValidFlagCheckUtils {
    /**
     * Domain中Entity的 禁用标志校验
     */
    public static <V extends BaseEntity, D extends AbstractDomain<V>> void checkValidFlagN(D d) {
        V entity = d.getEntity();
        if (null == entity) {
            return;
        }
        ValidEntity validEntity = new ValidEntity();
        if (!BeanUtils.ClassFieldContain(entity.getClass(), ValidEntity.class)) {
            return;
        }
        BeanUtils.copyProperties(entity, validEntity);
        if (!"N".equals(validEntity.getValidFlag())) {
            MesErrorCodeException exception = new MesErrorCodeException(BaseErrorCode.DATA_HAS_EFFECTIVE.getCode());
            log.error("{}", exception.getMessage(), exception);
            throw exception;
        }
    }

    /**
     * Domain中Entity的 启用标志校验
     */
    public static <V extends BaseEntity, D extends AbstractDomain<V>> void checkValidFlagY(D d) {
        V entity = d.getEntity();
        if (null == entity) {
            return;
        }
        ValidEntity validEntity = new ValidEntity();
        if (!BeanUtils.ClassFieldContain(entity.getClass(), ValidEntity.class)) {
            return;
        }
        BeanUtils.copyProperties(entity, validEntity);
        if (!"Y".equals(validEntity.getValidFlag())) {
            MesErrorCodeException exception = new MesErrorCodeException(BaseErrorCode.DATA_HAS_DISABLE.getCode());
            log.error("{}", exception.getMessage(), exception);
            throw exception;
        }
    }

    /**
     * Domain中Entity的 禁用信息填写
     */
    public static <V extends BaseEntity, D extends AbstractDomain<V>> void setDisableInfo(D d) {
        V entity = d.getEntity();
        if (null == entity) {
            return;
        }
        ValidEntity validEntity = new ValidEntity();
        if (!BeanUtils.ClassFieldContain(entity.getClass(), ValidEntity.class)) {
            return;
        }
        BeanUtils.copyProperties(entity, validEntity);
        validEntity.setValidFlag("N");
        validEntity.setDisableDate(BizParamUtils.getLastEventTime());
        validEntity.setDisableAccountName(BizParamUtils.getLastEventUser());
        validEntity.setDisableAccountIdentifier(BizParamUtils.getUserId());
        validEntity.setDisableReasonText((String) BizParamUtils.getParam("disableReasonText"));
/*        validEntity.setEffectiveDate(null);
        validEntity.setEffectiveAccountName(null);
        validEntity.setEffectiveAccountIdentifier(null);*/
        BeanUtils.copyProperties(validEntity, entity);
        d.setAuditField();
    }

    /**
     * Domain中Entity的 启用信息填写
     */
    public static <V extends BaseEntity, D extends AbstractDomain<V>> void setEffectiveInfo(D d) {
        V entity = d.getEntity();
        if (null == entity) {
            return;
        }
        ValidEntity validEntity = new ValidEntity();
        if (!BeanUtils.ClassFieldContain(entity.getClass(), ValidEntity.class)) {
            return;
        }
        BeanUtils.copyProperties(entity, validEntity);
        validEntity.setValidFlag("Y");
        validEntity.setEffectiveDate(BizParamUtils.getLastEventTime());
        validEntity.setEffectiveAccountName(BizParamUtils.getLastEventUser());
        validEntity.setEffectiveAccountIdentifier(BizParamUtils.getUserId());
        validEntity.setDisableDate(null);
        validEntity.setDisableAccountName(null);
        validEntity.setDisableAccountIdentifier(null);
        validEntity.setDisableReasonText(null);
        BeanUtils.copyProperties(validEntity, entity);
        d.setAuditField();
    }

    /**
     * 对Domain做启用操作
     *
     * @param pk      实体的PK。
     * @param service 主表数据对应的service的实例化对象
     * @param <V>     主表数据类型
     * @param <PK>    主表数据主键类型
     * @param <S>     主表数据对应的service类型
     */
    public static <V extends BaseEntity, D extends AbstractDomain<V>, PK extends Serializable, S extends BaseDomainService<V, D, PK>> D effective(PK pk, S service) {
        D d = service.checkExistById(pk);
        checkValidFlagN(d);
        setEffectiveInfo(d);
        service.update(d);
        return d;
    }

    /**
     * 对Domain做禁用操作
     *
     * @param pk      实体的PK。
     * @param service 主表数据对应的service的实例化对象
     * @param <V>     主表数据类型
     * @param <PK>    主表数据主键类型
     * @param <S>     主表数据对应的service类型
     */
    public static <V extends BaseEntity, D extends AbstractDomain<V>, PK extends Serializable, S extends BaseDomainService<V, D, PK>> D disable(PK pk, S service) {
        D d = service.checkExistById(pk);
        checkValidFlagY(d);
        setDisableInfo(d);
        service.update(d);
        return d;
    }

}