package cec.jiutian.core.util;

import cec.jiutian.core.exception.MesErrorCodeException;
import cec.jiutian.security.base.entity.BaseEntity;
import cec.jiutian.security.base.entity.PCKey;
import cec.jiutian.core.comn.errorCode.BaseErrorCode;
import cec.jiutian.core.comn.util.BeanUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.TreeSet;
import java.util.stream.Collectors;

/**
 * @title: TreeEntityUtil.java
 * @package cec.jiutian.core.util
 * @description: 树查询工具，向上查询和向下查询
 * @author: <EMAIL>
 * @date: 2022-4-15 11:38
 * @version: 2.5.3
 */
@Slf4j
public class TreeEntityUtil<T extends PCKey> {
    private int thisTreeHierarchy = 0;
    private Boolean showNullFlag = true;

    @Data
    class TreeDTO {
        private JSONObject property;
        @ApiModelProperty(value = "是否有子节点标志")
        private Boolean childFlag = false;
        @ApiModelProperty(value = "子节点集")
        private List<TreeDTO> children;

        public TreeDTO(T t) {
            JSONObject job;
            if (showNullFlag) {
                job = JSONObject.parseObject(JSON.toJSONString(t, SerializerFeature.WriteMapNullValue)); //包含null的属性
            } else {
                job = JSONObject.parseObject(JSONObject.toJSON(t).toString());  //所有null的属性都不要
            }
            if (t.getShowAuditFlag()) {
                List<String> fieldNameList = BeanUtils.getFieldNameList(BaseEntity.class);
                fieldNameList.forEach(x -> job.remove(x));
            }
            this.property = job;
        }
    }

    interface TreeType {
        String CHILD = "selectTreeChild";
        String PARENT = "selectTreeParent";
        String ROOT = "selectTreeRoot";
    }

    private List<T> getTreeNodes(T t, String treeType) {
        try {
            showNullFlag = t.getShowNullFlag();
            Class tClass = t.getClass();
            String simpleName = tClass.getSimpleName();
            String first = simpleName.replaceFirst(String.valueOf(simpleName.charAt(0)), String.valueOf(Character.toLowerCase(simpleName.charAt(0))));
            Object tMapper = SpringContextUtils.getBean(first.replace("PO", "Mapper"));
            Method tSelectTree = tMapper.getClass().getMethod(treeType, Object.class);
            List<T> allNotes = (List<T>) tSelectTree.invoke(tMapper, t);
            return allNotes;
        } catch (NoSuchMethodException e) {
            log.error(e.getMessage());
            MesErrorCodeException exception = new MesErrorCodeException(e, BaseErrorCode.EXCEPTION.getCode());
            log.error("{}", exception.getMessage(), exception);
            throw exception;
        } catch (IllegalAccessException | InvocationTargetException e) {
            e.printStackTrace();
        }
        return null;
    }

    private List<T> getTreeNodesChild(T t) {
        if (null == t.getIdentifier() && null == t.getParentIdentifier()) {
            return getTreeNodes(t, TreeType.ROOT);
        } else {
            return getTreeNodes(t, TreeType.CHILD);
        }
    }

    private List<T> getTreeNodesParent(T t) {
        return getTreeNodes(t, TreeType.PARENT);
    }

    public Object getTreeWithChild(T t, int treeHierarchy) {
        List<T> allNotes = getTreeNodesChild(t);
        return getTreeWithChild(allNotes, treeHierarchy);
    }

    public Object getTreeWithParent(T t) {
        List<T> allNotes = getTreeNodesParent(t);
        return getTreeWithParent(allNotes);
    }

    private Object getTreeWithChild(List<T> allNotes, int treeHierarchy) {
        if (CollectionUtils.isEmpty(allNotes)) {
            return new Object[0];
        }
        List<T> parentNodes = allNotes.stream().filter(x -> "p".equals(x.getNodeType())).collect(Collectors.toList());
        List<T> childNodes = allNotes.stream().filter(x -> "c".equals(x.getNodeType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(parentNodes)) {
            return new Object[0];
        }
        List<TreeDTO> trees = parentNodes.stream().map(TreeDTO::new).collect(Collectors.toList());
        trees.forEach(x -> {
            if (x.property.getInteger("childCount") > 0) {
                x.childFlag = true;
            }
        });
        trees.forEach(x -> {
            thisTreeHierarchy = 0;
            setTreeChildren(x, childNodes, treeHierarchy);
        });
        thisTreeHierarchy = 0;
        return renormalizeTree(trees);
    }

    private Object getTreeWithParent(List<T> allNotes) {
        if (CollectionUtils.isEmpty(allNotes)) {
            return new Object[0];
        }
        // 有相同的根节点时，则合并根节点，去掉去除有子节点但type为c的情况
        allNotes = allNotes.stream().collect(
                Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(T::getIdentifier))), ArrayList::new));
        // 从根节点开始组树
        List<T> parentNodes = allNotes.stream().filter(x -> null == x.getParentIdentifier()).collect(Collectors.toList());
        List<T> childNodes = allNotes.stream().filter(x -> null != x.getParentIdentifier()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(parentNodes)) {
            return new Object[0];
        }
        List<TreeDTO> trees = parentNodes.stream().map(TreeDTO::new).collect(Collectors.toList());
        trees.forEach(x -> {
            if (x.property.getInteger("childCount") > 0) {
                x.childFlag = true;
            }
        });
        trees.forEach(x -> {
            setTreeChildren(x, childNodes, null);
        });
        return renormalizeTree(trees);

/*        List<T> parentNodes = allNotes.stream().filter(x -> "p".equals(x.getNodeType())).collect(Collectors.toList());
        List<T> childNodes = allNotes.stream().filter(x -> "c".equals(x.getNodeType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(childNodes)) {
            return new Object[0];
        }
        List<TreeDTO> result = new ArrayList<>();
        List<TreeDTO> childTrees = childNodes.stream().map(TreeDTO::new).collect(Collectors.toList());
        childTrees.forEach(x -> setTreeParent(x, parentNodes, result));
        return renormalizeTree(result);*/
    }

    private void setTreeChildren(TreeDTO tree, List<T> childNodes, Integer loopTime) {
        setTreeChildrenFlag(tree);
        if (null != loopTime && ++thisTreeHierarchy > loopTime) {
            return;
        }
        List<T> treeChildren = childNodes.stream().filter(x -> x.getParentIdentifier().equals(tree.getProperty().getLong("identifier"))).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(treeChildren)) {
            return;
        }
        tree.setChildren(treeChildren.stream().map(TreeDTO::new).distinct().collect(Collectors.toList()));
        tree.getChildren().forEach(x -> setTreeChildren(x, childNodes, loopTime));
    }

    private void setTreeParent(TreeDTO tree, List<T> parentNodes, List<TreeDTO> result) {
        setTreeChildrenFlag(tree);
        List<T> treeParentList = parentNodes.stream().filter(x -> x.getIdentifier().equals(tree.getProperty().getLong("parentIdentifier"))).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(treeParentList)) {
            result.add(tree);
            return;
        }
        TreeDTO treeParent = new TreeDTO(treeParentList.get(0));
        treeParent.setChildren(Collections.singletonList(tree));
        setTreeParent(treeParent, parentNodes, result);
    }

    private void setTreeChildrenFlag(TreeDTO tree) {
        if (null != tree && null != tree.property && tree.property.getInteger("childCount") > 0) {
            tree.setChildFlag(true);
        }
    }

    /*
     * <AUTHOR>
     * @Description //重整化树结构，使其适用于前端结构
     * @Date 11:41 2022-5-9
     **/
    private Object renormalizeTree(List<TreeDTO> treeDTOList) {
        if (CollectionUtils.isEmpty(treeDTOList)) {
            return new Object[0];
        }
        List<Object> resultList = new ArrayList<>();
        for (TreeDTO treeDTO : treeDTOList) {
            JSONObject treeJob = new JSONObject();
            renormalizeTreeJob(treeDTO, treeJob);
            resultList.add(treeJob);
        }
        return resultList;
    }

    private void renormalizeTreeJob(TreeDTO tree, JSONObject resultTree) {
        JSONObject treeJob = JSONObject.parseObject(JSON.toJSONString(tree, SerializerFeature.WriteMapNullValue)); //包含null的属性
        JSONObject propertyJob = (JSONObject) treeJob.get("property");
        Map<String, Object> properties = propertyJob.getInnerMap();
        treeJob.putAll(properties);
        treeJob.remove("property");
        resultTree.putAll(treeJob);
        List<TreeDTO> treeChildren = tree.getChildren();
        if (CollectionUtils.isEmpty(treeChildren)) {
            return;
        }
        List<JSONObject> jobChildren = new ArrayList<>();
        resultTree.remove("children");
        treeChildren.forEach(x -> {
            JSONObject job = new JSONObject();
            jobChildren.add(job);
            renormalizeTreeJob(x, job);
        });
        resultTree.put("children", jobChildren);
    }
}