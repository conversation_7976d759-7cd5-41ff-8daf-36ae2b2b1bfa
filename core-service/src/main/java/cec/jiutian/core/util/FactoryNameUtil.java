package cec.jiutian.core.util;

import cec.jiutian.core.exception.MesErrorCodeException;
import cec.jiutian.core.feign.FactoryAndAreaServiceFeign;
import cec.jiutian.security.base.entity.IFactotyAreaEntity;
import cec.jiutian.core.comn.errorCode.BaseErrorCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * @author: <EMAIL>
 * @date: 2021-6-29 10:27
 */
@Slf4j
@Component
public class FactoryNameUtil {
    private final FactoryAndAreaServiceFeign factoryAreaServiceFeign;

    public FactoryNameUtil(FactoryAndAreaServiceFeign factoryAreaServiceFeign) {
        this.factoryAreaServiceFeign = factoryAreaServiceFeign;
    }

    /**
     * 检查实体的仓别和区域是否在配置的表中
     *
     * @param tlist 需要检查的实体列表。
     * @param <T>   主表数据类型
     */
    public <T extends IFactotyAreaEntity> Boolean checkFactoryAndAreaWithList(List<T> tlist) {
        if (tlist == null) {
            MesErrorCodeException exception = new MesErrorCodeException(BaseErrorCode.NO_DATA.getCode());
            log.error("{}", exception.getMessage(), exception);
            throw exception;
        }
        List<String> factoryNames = this.getFactory();
        Map<String, List<String>> areaNameListMap = this.getAreaByFactory();

        for (int i = 1; i <= tlist.size(); i++) {
            String factoryName = tlist.get(i - 1).getFactoryName();
            if (null == factoryNames || !factoryNames.contains(factoryName)) {
                MesErrorCodeException exception = new MesErrorCodeException(BaseErrorCode.FCTRY_NOT_CONFIG.getCode())
                        .addMsgItem("NO" + i + ", " + factoryName);
                throw exception;
            }
            String areaName = tlist.get(i - 1).getAreaName();
            List<String> areaNameList = areaNameListMap.get(factoryName);
            if (null == areaNameList || !areaNameList.contains(areaName)) {
                MesErrorCodeException exception = new MesErrorCodeException(BaseErrorCode.AREA_NOT_CONFIG.getCode())
                        .addMsgItem("NO" + i + ", " + "Factory: " + factoryName + "AreaName: " + areaName);
                throw exception;
            }

        }
        return true;
    }

    public <T extends IFactotyAreaEntity> Boolean checkFactoryAndArea(T t) {
        if (t == null) {
            MesErrorCodeException exception = new MesErrorCodeException(BaseErrorCode.NO_DATA.getCode());
            log.error("{}", exception.getMessage(), exception);
            throw exception;
        }
        String factoryName = t.getFactoryName();
        List<String> factoryNames = this.getFactory();
        if (null == factoryNames || !factoryNames.contains(factoryName)) {
            MesErrorCodeException exception = new MesErrorCodeException(BaseErrorCode.FCTRY_NOT_CONFIG.getCode())
                    .addMsgItem(factoryName);
            throw exception;
        }
        String areaName = t.getAreaName();
        Map<String, List<String>> areaNameListMap = this.getAreaByFactory();
        List<String> areaNameList = areaNameListMap.get(factoryName);
        if (null == areaNameList || !areaNameList.contains(areaName)) {
            MesErrorCodeException exception = new MesErrorCodeException(BaseErrorCode.AREA_NOT_CONFIG.getCode())
                    .addMsgItem("Factory: " + factoryName + "AreaName: " + areaName);
            throw exception;
        }
        return true;
    }

    public List<String> getFactory() {
        List<String> factoryNameList = factoryAreaServiceFeign.getFactory();
        if (CollectionUtils.isEmpty(factoryNameList)) {
            MesErrorCodeException exception = new MesErrorCodeException(BaseErrorCode.FEIGN_GET_FAIL.getCode());
            log.error("{}", exception.getMessage(), exception);
            throw exception;
        }
        return factoryNameList;
    }

    public Map<String, List<String>> getAreaByFactory() {
        Map<String, List<String>> areaNameList = factoryAreaServiceFeign.getAreaByFactory();
        if (null == (areaNameList)) {
            MesErrorCodeException exception = new MesErrorCodeException(BaseErrorCode.FEIGN_GET_FAIL.getCode());
            log.error("{}", exception.getMessage(), exception);
            throw exception;
        }
        return areaNameList;
    }

}
