package cec.jiutian.core.util;

import cec.jiutian.core.comn.constant.FileTypeConstant;
import cec.jiutian.core.comn.errorCode.BaseErrorCode;
import cec.jiutian.core.comn.util.StringUtils;
import cec.jiutian.core.exception.MesErrorCodeException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletResponse;
import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Arrays;
import java.util.Objects;

@Slf4j
public class FileUtils {

    /**
     * 根据文件名获取文件类型后缀
     *
     * @param filename 文件名称，不包含路径
     * @return 文件类型后缀，如doc，xls等，不包含小数点
     */
    public static String getFileType(String filename) {
        // 截取文件后缀
        String[] suffix = filename.split("\\.");
        if (suffix.length == 0) {
            MesErrorCodeException exception = new MesErrorCodeException(BaseErrorCode.FILE_TYPE_ERROR.getCode())
                    .addMsgItem(getFileTypeConstant());
            log.error("{}", exception.getMessage(), exception);
            throw exception;
        }
        //获取文件类型
        return suffix[suffix.length - 1];
    }

    /**
     * 校验文件类型是否在系统规定的文件类型集合中
     */
    public static String checkFileType(MultipartFile file) {
        //获取文件类型
        String fileType = getFileType(Objects.requireNonNull(file.getOriginalFilename()));
        if (!contains(fileType)) {
            MesErrorCodeException exception = new MesErrorCodeException(BaseErrorCode.FILE_TYPE_ERROR.getCode())
                    .addMsgItem(getFileTypeConstant());
            log.error("{}", exception.getMessage(), exception);
            throw exception;
        }
        return fileType;
    }

    /**
     * 校验文件后缀，并返回后缀
     * (反射形式的校验已废弃)
     *
     * @param file
     * @return
     */
    @Deprecated
    public static String checkFileType(MultipartFile file, Class constantClass) {
        // 截取文件后缀
        String[] suffix = file.getOriginalFilename().split("\\.");
        if (suffix.length == 0) {
            MesErrorCodeException exception = new MesErrorCodeException(BaseErrorCode.FILE_TYPE_ERROR.getCode()).addMsgItem(ConstantUtils.getFileTypes(constantClass));
            throw exception;
        }
        //获取文件类型
        String fileType = suffix[suffix.length - 1];
        if (!ConstantUtils.contains(constantClass, fileType)) {
            MesErrorCodeException exception = new MesErrorCodeException(BaseErrorCode.FILE_TYPE_ERROR.getCode()).addMsgItem(ConstantUtils.getFileTypes(constantClass));
            throw exception;
        }
        return suffix[suffix.length - 1];
    }

    /**
     * 校验指定类型的文件，并返回后缀
     *
     * @param file      spring形式的文件数据
     * @param fileTypes 文件类型字符串数组，形如：[doc, xls, ppt]
     * @return 返回文件类型后缀，不包含小数点
     */
    public static String checkFileType(MultipartFile file, String... fileTypes) {
        String fileType = getFileType(Objects.requireNonNull(file.getOriginalFilename()));
        if (checkFileType(fileType, fileTypes)) {
            return fileType;
        } else {
            // 理论上此判断分支永远不会进入
            log.error("impossible!!!!");
            return null;
        }
    }

    /**
     * 校验指定类型的文件，并返回后缀
     *
     * @param fileType  文件类型
     * @param fileTypes 文件类型字符串数组，形如：[doc, xls, ppt]
     * @return 返回文件类型后缀，不包含小数点
     */
    public static Boolean checkFileType(String fileType, String... fileTypes) {
        if (StringUtils.containsAny(fileType, fileTypes)) {
            return true;
        } else {
            MesErrorCodeException exception = new MesErrorCodeException(BaseErrorCode.FILE_TYPE_ERROR.getCode()).addMsgItem(Arrays.toString(fileTypes));
            log.error("{}", exception.getMessage(), exception);
            throw exception;
        }
    }

    /**
     * 直接下载文件的接口（目前workflow在使用该方法）
     *
     * @param inputStream 文件输入流,不为空
     * @param filename    文件名，不为空
     * @param filetype    文件类型，不为空
     * @param response    不为空
     */
    public static void downloadSingleFile(InputStream inputStream, String filename, String filetype, HttpServletResponse response) {
        if (StringUtils.isNotBlank(filetype)) {
            response.setContentType(filetype);
        } else {
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
        }
        response.setHeader("Content-Disposition", "attachment;filename=" + filename);
        byte[] buff = new byte[1024];
        BufferedInputStream bis = null;
        OutputStream os;
        try {
            os = response.getOutputStream();
            bis = new BufferedInputStream(inputStream);
            int i;
            while ((i = bis.read(buff)) != -1) {
                os.write(buff, 0, i);
                os.flush();
            }
        } catch (IOException e) {
            e.printStackTrace();
            MesErrorCodeException exception = new MesErrorCodeException(e, BaseErrorCode.EXCEPTION.getCode());
            log.error("{}", exception.getMessage(), exception);
            throw exception;
        } finally {
            try {
                if (bis != null) {
                    bis.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
                MesErrorCodeException exception = new MesErrorCodeException(e, BaseErrorCode.EXCEPTION.getCode());
                log.error("{}", exception.getMessage(), exception);
            }
        }
    }

    public static Boolean contains(String fileType) {
        return checkMediaType(fileType) || StringUtils.containsAny(fileType, FileTypeConstant.OFFICE_TYPE);
    }

    public static String getFileTypeConstant() {
        return Arrays.toString(FileTypeConstant.AUDIO_TYPE) +
                Arrays.toString(FileTypeConstant.IMAGE_TYPE) +
                Arrays.toString(FileTypeConstant.VIDEO_TYPE) +
                Arrays.toString(FileTypeConstant.OFFICE_TYPE);
    }

    public static Boolean checkMediaType(String fileType) {
        return StringUtils.containsAny(fileType, FileTypeConstant.AUDIO_TYPE)
                || StringUtils.containsAny(fileType, FileTypeConstant.IMAGE_TYPE)
                || StringUtils.containsAny(fileType, FileTypeConstant.VIDEO_TYPE);
    }
}
