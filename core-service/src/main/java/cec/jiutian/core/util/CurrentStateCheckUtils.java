package cec.jiutian.core.util;

import cec.jiutian.core.entity.AbstractDomain;
import cec.jiutian.core.exception.MesErrorCodeException;
import cec.jiutian.security.base.entity.BaseEntity;
import cec.jiutian.security.base.entity.CurrentStateEntity;
import cec.jiutian.core.base.BaseDomainService;
import cec.jiutian.core.comn.errorCode.BaseErrorCode;
import cec.jiutian.core.comn.util.BeanUtils;
import cec.jiutian.core.comn.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.List;

/*
 * <AUTHOR>
 * @Date 15:14 2022-7-26
 **/
@Slf4j
@Component
public class CurrentStateCheckUtils {
    /**
     * Domain中Entity的 当前状态校验
     *
     * @param d            domain。
     * @param targetStates 目标状态集合，当前状态不在目标集合就会抛出
     * @param messageSign  消息标记，抛出后显示， XXX的当前状态错误，应为{[],[]}
     */
    public static <V extends BaseEntity, D extends AbstractDomain<V>> void checkCurrentState(D d, List<String> targetStates, String messageSign) {
        if (CollectionUtils.isEmpty(targetStates)) {
            return;
        }
        V entity = d.getEntity();
        if (null == entity) {
            return;
        }
        CurrentStateEntity currentStateEntity = new CurrentStateEntity();
        if (!BeanUtils.ClassFieldContain(entity.getClass(), CurrentStateEntity.class)) {
            return;
        }
        BeanUtils.copyProperties(entity, currentStateEntity);
        if (!targetStates.contains(currentStateEntity.getCurrentState())) {
            StringBuilder stateString = new StringBuilder(StringUtils.EMPTY);
            for (String state : targetStates) {
                stateString.append(state).append(",");
            }
            stateString.delete(stateString.length() - 1, stateString.length());
            MesErrorCodeException exception = new MesErrorCodeException(BaseErrorCode.CURRENT_STATE_ERROR.getCode())
                    .addMsgItem(messageSign)
                    .addMsgItem(currentStateEntity.getCurrentState())
                    .addMsgItem(stateString.toString());
            log.error("{}", exception.getMessage(), exception);
            throw exception;
        }
    }

    /**
     * Domain中Entity的 当前状态校验
     *
     * @param pk           实体的PK。
     * @param service      主表数据对应的service的实例化对象
     * @param targetStates 目标状态集合，当前状态不在目标集合就会抛出
     * @param messageSign  消息标记，抛出后显示， XXX的当前状态错误，应为{[],[]}
     */
    public static <V extends BaseEntity, D extends AbstractDomain<V>, PK extends Serializable, S extends BaseDomainService<V, D, PK>> D checkCurrentStateById(PK pk, S service, List<String> targetStates, String messageSign) {
        D d = service.checkExistById(pk);
        checkCurrentState(d, targetStates, messageSign);
        return d;
    }

}