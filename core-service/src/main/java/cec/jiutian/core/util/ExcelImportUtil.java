package cec.jiutian.core.util;

import cec.jiutian.core.exception.MesErrorCodeException;
import cec.jiutian.security.base.entity.BaseEntity;
import cec.jiutian.security.base.annotation.ExcelImport;
import cec.jiutian.core.comn.constant.ContentTypeConstant;
import cec.jiutian.core.comn.errorCode.BaseErrorCode;
import cec.jiutian.core.comn.util.BeanUtils;
import cec.jiutian.core.comn.util.StringUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.http.HttpStatus;
import org.apache.poi.POIXMLDocument;
import org.apache.poi.hssf.usermodel.HSSFDateUtil;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.openxml4j.opc.OPCPackage;
import org.apache.poi.poifs.filesystem.POIFSFileSystem;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Comment;
import org.apache.poi.ss.usermodel.Drawing;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFClientAnchor;
import org.apache.poi.xssf.usermodel.XSSFRichTextString;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.web.multipart.MultipartFile;

import javax.persistence.GeneratedValue;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.PushbackInputStream;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Slf4j
public class ExcelImportUtil {
    private static int subTableTier;
    private static int nowTier;

    public static void exportExcelResponse(HttpServletResponse response, Workbook wb, String etag, String filename) {
        ServletOutputStream out = null;
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        try {
            response.addHeader("Content-Type", ContentTypeUtils.getContentType(ContentTypeConstant.class, "XLSX"));
            // 此处对下载文件的中文名称做处理，若出现浏览器不兼容（例如IE和firefox），可能需要前端提供浏览器的编码作为参数，此处默认使用ISO_8859_1编码
            response.addHeader("Content-Disposition", "attachment; filename=" + "\"" + new String(filename.getBytes(), StandardCharsets.ISO_8859_1) + "\"" + "." + "XLSX");
            // 浏览器下载时进度提示
            if (StringUtils.isNotBlank(etag)) {
                response.addHeader("Cache-Control", "no-cache");
                response.addHeader("Etag", etag);
            }
            response.setStatus(HttpStatus.SC_OK);
            wb.write(baos);
            // 浏览器下载时进度提示
            response.setHeader("Content-Length", String.valueOf(baos.size()));
            out = response.getOutputStream();
            wb.write(out);
            out.flush();
        } catch (IOException e) {
            throwException(e, BaseErrorCode.FILE_IO_EXCEPTION.getCode());
        } finally {
            try {
                if (null != out) {
                    out.close();
                    baos.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    protected static void throwException(Exception exp, String errorCode, String... msgs) {
        MesErrorCodeException exception = new MesErrorCodeException(exp, errorCode);
        for (String msg : msgs) {
            exception.addMsgItem(msg);
        }
        throw exception;
    }

    public static <T> void exportExcelModule(HttpServletResponse response, Class<T> tClass) throws Exception {
        exportExcelModule(response, tClass, false, 1);
    }

    public static <T> void exportExcelModule(HttpServletResponse response, Class<T> tClass, Boolean subTableFlag) throws Exception {
        exportExcelModule(response, tClass, subTableFlag, 1);
    }

    public static <T> void exportExcelModule(HttpServletResponse response, Class<T> tClass, Boolean subTableFlag, int tier) {
        try {
            subTableTier = tier;
            nowTier = 0;
            Workbook wb = new XSSFWorkbook();
            createSheetByEntity(wb, tClass, subTableFlag, nowTier);
            exportExcelResponse(response, wb, "etag", tClass.getSimpleName());
        } catch (Exception e) {
            MesErrorCodeException exception = new MesErrorCodeException(e, BaseErrorCode.EXCEPTION.getCode());
            log.error("{}", exception.getMessage(), exception);
            throw exception;
        }
    }

    public static <T> void createSheetByEntity(Workbook wb, Class<T> tClass, Boolean subTableFlag, int tier) throws Exception {
        if (null == tClass) {
            return;
        }
        int rowSize = 0;
        Sheet sheet = wb.createSheet(tClass.getSimpleName().replaceFirst("PO", ""));
        Row row = sheet.createRow(rowSize);
        List<Field> fields = getAllFields(tClass);
        List<String> baseEntityFieldNames = getFieldNameList(BaseEntity.class);
        int columnNum = 0;
        for (int i = 0; i < fields.size(); i++) {
            Field field = fields.get(i);
            //若为自增主键则不作为导入栏
            if (field.isAnnotationPresent(GeneratedValue.class) || baseEntityFieldNames.contains(field.getName())
                    || "serialVersionUID".equals(field.getName())) {
                continue;
            }
            Cell cell = row.createCell(columnNum++);
            cell.setCellValue(field.getName());
            Drawing drawing = sheet.createDrawingPatriarch();
            // 前四个参数是坐标点,后四个参数是编辑和显示批注时的大小.
            //dx1 dy1 起始单元格中的x,y坐标.
            //dx2 dy2 结束单元格中的x,y坐标
            //col1,row1 指定起始的单元格，下标从0开始
            //col2,row2 指定结束的单元格 ，下标从0开始
            Comment comment = drawing.createCellComment(new XSSFClientAnchor(0, 0, 0, 0, (short) 5, 2, (short) 5, 6));
            // 输入批注信息
            comment.setString(new XSSFRichTextString(field.getName()));
            cell.setCellComment(comment);
            Comment cellComment = cell.getCellComment();
            cellComment.setVisible(false);//设置批注默认不显示
        }
        while (subTableFlag && nowTier < subTableTier) {
            Class<?> subTable = getSubTableClass(tClass);
            createSheetByEntity(wb, subTable, subTableFlag, ++nowTier);
        }
    }

    public static <T> void importFromExcel(MultipartFile file, Class<T> tClass) throws Exception {
        importFromExcel(file, tClass, false, 1);
    }

    public static <T> void importFromExcel(MultipartFile file, Class<T> tClass, Boolean subTableFlag) throws
            Exception {
        importFromExcel(file, tClass, subTableFlag, 1);
    }

    public static <T> void importFromExcel(MultipartFile file, Class<T> tClass, Boolean subTableFlag, int tier) throws
            Exception {
        subTableTier = tier;
        nowTier = 0;
        int thisTier = 0;
        // 1:获取List
        List<T> tList = readExcelToListByFieldType(file, tClass, nowTier);
        doSaveAndCheck(tList);
        // 6: 获取子表 继续
        if (subTableFlag) {
            importSubTable(file, tClass, ++nowTier);
        }
    }

    public static void importSubTable(MultipartFile file, Class<?> clazz, int tier) throws Exception {
        Class<?> subTable = getSubTableClass(clazz);
        if (null == subTable) {
            return;
        }
        List<Object> subTableList = readExcelToListByFieldType(file, (Class<Object>) subTable, nowTier);
        doSaveAndCheck(subTableList);
        while (nowTier < subTableTier) {
            importSubTable(file, subTable, ++nowTier);
        }
    }

    public static Class<?> getSubTableClass(Class<?> clazz) throws Exception {
        if (!clazz.isAnnotationPresent(ExcelImport.class)) {
            return null;
        }
        ExcelImport ano = clazz.getAnnotation(ExcelImport.class);
        String subTableName = ano.subTableName();
        if (StringUtils.isEmpty(subTableName)) {
            return null;
        }
        String first = clazz.getName().replaceFirst(clazz.getSimpleName(), subTableName);
        Class<?> subTable = clazz.forName(first + "PO");
        return subTable;
    }

    private static class AuditField extends BaseEntity {
        public AuditField() {
            this.setCreateTime(BizParamUtils.getLastEventTime());
            this.setCreateUser(BizParamUtils.getLastEventUser());
            this.setLastEventComment(BizParamUtils.getLastEventComment());
            this.setLastEventName(BizParamUtils.getOperationName());
            this.setLastEventTime(BizParamUtils.getLastEventTime());
            this.setLastEventUser(BizParamUtils.getLastEventUser());
        }
    }

    public static List<?> initAuditFields(List<?> tList) throws Exception {
        if (CollectionUtils.isEmpty(tList)) {
            return null;
        }
        AuditField auditField = new AuditField();
        for (Object obj : tList) {
            BeanUtils.copyNotNullProperties(auditField, obj);
        }
        return tList;
    }

    public static void doSaveAndCheck(List<?> tList) throws Exception {
        if (CollectionUtils.isEmpty(tList)) {
            return;
        }
        // 2:非空校验
        checkColumnNotNull(tList);
        // 3: 唯一校验
        checkColumnUnique(tList);
        // 4：保存主表
        tList = initAuditFields(tList);
        saveEntities(tList);
        // 5: 保存历史表
        Class<?> clazz = tList.get(0).getClass();
        if (!(clazz.isAnnotationPresent(ExcelImport.class) && !clazz.getAnnotation(ExcelImport.class).historyTableFlag())) {
            saveHistories(tList);
        }
    }

    /**
     * <p>检查需要保存的列组合的必填列是否都有值</p>
     *
     * @param
     * @param
     * @return
     */
    public static <T> void checkColumnNotNull(List<T> tList) throws Exception {
        Class<?> clazz = tList.get(0).getClass();
        if (!clazz.isAnnotationPresent(ExcelImport.class)) {
            return;
        }
        ExcelImport ano = clazz.getAnnotation(ExcelImport.class);
        String[] notNullColumns = ano.notNullColumns();
        if (null == notNullColumns || notNullColumns.length == 0) {
            return;
        }
        List<Field> fields = getAllFields(clazz);
        List<String> notNullColumnList = Arrays.asList(notNullColumns);
        for (int i = 0; i < tList.size() - 1; i++) {
            T temp = tList.get(i);
            for (Field field : fields) {
                field.setAccessible(true);
                String fieldName = field.getName();
                if (!notNullColumnList.contains(field.getName())) {
                    continue;
                }

                if (null == field.get(temp)) {
                    throw new Exception("第" + (i + 1) + "行中属性" + fieldName + " 为空");
                }
            }
        }
    }

    /**
     * <p>检查需要保存的列组合唯一性</p>
     *
     * @param
     * @param
     * @return
     */
    public static <T> void checkColumnUnique(List<T> tList) throws Exception {
        Class<?> clazz = tList.get(0).getClass();
        if (!clazz.isAnnotationPresent(ExcelImport.class)) {
            return;
        }
        ExcelImport ano = clazz.getAnnotation(ExcelImport.class);
        String[] uniqueColumns = ano.uniqueColumns();
        if (uniqueColumns.length == 0) {
            return;
        }
        for (int i = 0; i < tList.size() - 1; i++) {
            T temp = tList.get(i);
            for (int j = i + 1; j < tList.size(); j++) {
                if (compareWithAno(temp, tList.get(j), uniqueColumns) < 1) {
                    throw new Exception("第" + (i + 1) + "行跟第" + (j + 1) + "行唯一键重复，值是：" + temp);
                }
            }
        }

    }

    /*
     * <AUTHOR>
     * @Description //判断指定字段的值是否相同
     * @return int  0表示相同，1表示不同
     **/
    public static <T> int compareWithAno(T t1, T t2, String[] uniqueColumns) {
        Class<?> clazz = t1.getClass();
        List<Field> fields = getAllFields(clazz);
        List<String> uniqueColumnList = Arrays.asList(uniqueColumns);
        try {
            for (Field field : fields) {
                field.setAccessible(true);
                String fieldName = field.getName();
                if (!uniqueColumnList.contains(field.getName())) {
                    continue;
                }
                String str1 = String.valueOf(field.get(t1));
                String str2 = String.valueOf(field.get(t2));
                if (!StringUtils.equals(String.valueOf(field.get(t1)), String.valueOf(field.get(t2)))) {
                    return 1;
                }
            }
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
        return 0;
    }

    private static <T> void saveEntities(List<T> tList) {
        Class<?> aClass = tList.get(0).getClass();
        try {
            String simpleName = aClass.getSimpleName();
            String first = simpleName.replaceFirst(String.valueOf(simpleName.charAt(0)), String.valueOf(Character.toLowerCase(simpleName.charAt(0))));
            Object mapper = SpringContextUtils.getBean(first.replace("PO", "Mapper"));
            Method save = mapper.getClass().getMethod("insertBatch", List.class);
            save.invoke(mapper, tList);
        } catch (IllegalAccessException | NoSuchMethodException | InvocationTargetException e) {
            MesErrorCodeException exception = new MesErrorCodeException(e, BaseErrorCode.EXCEPTION.getCode());
            log.error("{}", exception.getMessage(), exception);
            throw exception;
        }
    }

    private static <T> void saveHistories(List<T> tList) {
        if (null == tList || tList.size() == 0) {
            return;
        }
        Class<?> aClass = tList.get(0).getClass();
        try {
            Class<?> history = Class.forName(aClass.getName().replace("PO", "HistoryPO"));
            List<Object> tHistoryList = new ArrayList<>();
            for (T t : tList) {
                Object tHistory = history.newInstance();
                BeanUtils.copyProperties(t, tHistory);
                tHistoryList.add(tHistory);
            }
            String simpleName = history.getSimpleName();
            String first = simpleName.replaceFirst(String.valueOf(simpleName.charAt(0)), String.valueOf(Character.toLowerCase(simpleName.charAt(0))));
            Object historyMapper = SpringContextUtils.getBean(first.replace("PO", "Mapper"));
            Method historySave = historyMapper.getClass().getMethod("insertBatch", List.class);
            historySave.invoke(historyMapper, tHistoryList);
        } catch (ClassNotFoundException | InstantiationException | IllegalAccessException | NoSuchMethodException | InvocationTargetException e) {
            MesErrorCodeException exception = new MesErrorCodeException(e, BaseErrorCode.EXCEPTION.getCode());
            log.error("{}", exception.getMessage(), exception);
            throw exception;
        }
    }

    public static Workbook createCommonWorkbook(InputStream inp) throws IOException, InvalidFormatException {

        // 首先判断流是否支持mark和reset方法，最后两个if分支中的方法才能支持
        if (!inp.markSupported()) {
            // 还原流信息
            inp = new PushbackInputStream(inp, 8);
        }
        // EXCEL2003使用的是微软的文件系统
        if (POIFSFileSystem.hasPOIFSHeader(inp)) {
            return new HSSFWorkbook(inp);
        }
        // EXCEL2007使用的是OOM文件格式
        if (POIXMLDocument.hasOOXMLHeader(inp)) {
            // 可以直接传流参数，但是推荐使用OPCPackage容器打开
            return new XSSFWorkbook(OPCPackage.open(inp));
        }

        return null;
    }

    public static String getCellValue(Row row, int cellNum) {
        Cell cell = row.getCell(cellNum - 1);
        if (null == cell) {
            return null;
        }
        cell.setCellType(Cell.CELL_TYPE_STRING);
        String cellValue = cell.getStringCellValue();
        return StringUtils.isNotBlank(cellValue) ? cellValue.trim() : null;
    }

    public static Float getFloatValue(Row row, int cellNum) {
        Cell c = row.getCell(cellNum - 1);
        if (c != null) {
            c.setCellType(Cell.CELL_TYPE_STRING);
            return Float.parseFloat(c.getStringCellValue());
        }
        return null;
    }

    public static Integer getIntegerValue(Row row, int cellNum) {
        Cell c = row.getCell(cellNum - 1);
        if (c != null) {
            c.setCellType(Cell.CELL_TYPE_STRING);
            return Integer.parseInt(c.getStringCellValue());
        }
        return null;
    }

    /**
     * <p>从excel中读取数据并转化为一个对象数组，主要用于各个功能的excel导入接口，该方法只适用于单sheet的情况，一次只对一种对象进行操作</p>
     * <p>该方法要求excel的表头以备注的形式将对象的属性信息填入对应单元格，即表格第一行的每个单元格需要添加批注，内容为对象的Java字段名（小写开头驼峰式）</p>
     *
     * @param file   excel文件
     * @param tClass 对象类型
     * @return 对象的数组，如果表格无数据则返回null
     */
    public static <T> List<T> readExcelToList(MultipartFile file, Class<T> tClass) throws
            InvalidFormatException, InstantiationException, IllegalAccessException, IOException {
        try {
            Workbook workbook = ExcelImportUtil.createCommonWorkbook(file.getInputStream());
            Sheet sheet = Objects.requireNonNull(workbook).getSheetAt(0);
            if (sheet != null) {
                Map<Integer, String> fieldIndex = getFieldIndex(sheet);
                List<Field> fields = getAllFields(tClass);
                List<T> list = new ArrayList<>();
                for (int i = 1; i < sheet.getLastRowNum() + 1; i++) {
                    T t = Objects.requireNonNull(tClass).newInstance();
                    int count = setFieldValueByRow(fieldIndex, fields, sheet.getRow(i), t);
                    if (count != 0) {
                        list.add(t);
                    }
                }
                return list;
            } else {
                return null;
            }
        } catch (IOException | InvalidFormatException | IllegalAccessException | InstantiationException e) {
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * <p>从excel中读取数据并转化为一个对象数组，主要用于各个功能的excel导入接口，该方法只适用于单sheet的情况，一次只对一种对象进行操作</p>
     * <p>该方法要求excel的表头以备注的形式将对象的属性信息填入对应单元格，即表格第一行的每个单元格需要添加批注，内容为对象的Java字段名（小写开头驼峰式）</p>
     * <p>根据对象字段的类型解析</p>
     *
     * @param file   excel文件
     * @param tClass 对象类型
     * @return 对象的数组，如果表格无数据则返回null
     */
    public static <T> List<T> readExcelToListByFieldType(MultipartFile file, Class<T> tClass) throws
            InvalidFormatException, InstantiationException, IllegalAccessException, IOException {
        return readExcelToListByFieldType(file, tClass, 0);
    }

    public static <T> List<T> readExcelToListByFieldType(MultipartFile file, Class<T> tClass, int tier) throws
            InvalidFormatException, InstantiationException, IllegalAccessException, IOException {
        try {
            Workbook workbook = ExcelImportUtil.createCommonWorkbook(file.getInputStream());
            Sheet sheet = Objects.requireNonNull(workbook).getSheetAt(tier);
            if (sheet != null) {
                Map<Integer, String> fieldIndex = getFieldIndex(sheet);
                List<Field> fields = getAllFields(tClass);
                List<T> list = new ArrayList<>();
                for (int i = 1; i < sheet.getLastRowNum() + 1; i++) {
                    T t = Objects.requireNonNull(tClass).newInstance();
                    int count = setFieldValueByFieldType(fieldIndex, fields, sheet.getRow(i), t);
                    if (count != 0) {
                        list.add(t);
                    }
                }
                return list;
            } else {
                return null;
            }
        } catch (IOException | InvalidFormatException | IllegalAccessException | InstantiationException e) {
            e.printStackTrace();
            throw e;
        }
    }

    public static Map<Integer, String> getFieldIndex(Sheet sheet) {
        Row row = sheet.getRow(0);
        Map<Integer, String> fieldIndex = new HashMap<>();
        for (Cell cell : row) {
            Comment comment = cell.getCellComment();
            if (comment != null) {
                fieldIndex.put(comment.getColumn(), comment.getString().getString());
            }
        }
        return fieldIndex;
    }

    @SuppressWarnings("rawtypes")
    public static <T> List<Field> getAllFields(Class<T> tClass) {
        List<Field> fields = new ArrayList<>();
        Class aClass = tClass;
        while (aClass != null) {
            fields.addAll(Arrays.asList(aClass.getDeclaredFields()));
            aClass = aClass.getSuperclass();
        }
        return fields;
    }

    public static <T> List<String> getFieldNameList(Class<T> tClass) {
        List<String> fieldNameLists = new ArrayList<>();
        List<Field> fields = new ArrayList<>();
        Class aClass = tClass;
        while (aClass != null) {
            fields.addAll(Arrays.asList(aClass.getDeclaredFields()));
            aClass = aClass.getSuperclass();
            fieldNameLists = fields.stream().map(Field::getName).collect(Collectors.toList());
        }
        return fieldNameLists;
    }

    public static <T> void setFieldValueByCell(Field field, Cell cell, T t, AtomicInteger count) {
        field.setAccessible(true);
        try {
            int cellType = cell.getCellType();
            switch (cellType) {
                case Cell.CELL_TYPE_NUMERIC:
                    if (HSSFDateUtil.isCellDateFormatted(cell)) {
                        field.set(t, cell.getDateCellValue());
                    } else {
                        field.set(t, parseNumeric(field.getType(), cell.getNumericCellValue()));
                    }
                    break;
                case Cell.CELL_TYPE_STRING:
                    field.set(t, cell.getStringCellValue());
                    break;
                case Cell.CELL_TYPE_BOOLEAN:
                    field.set(t, cell.getBooleanCellValue());
                    break;
                default:
            }
            if (field.get(t) != null) {
                count.addAndGet(1);
            }
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
    }

    /**
     * 增加字段赋值计数器，初始为0，进行有效赋值操作会累加1，若最终为0，则表示对象为空对象，将被丢弃
     */
    public static <T> int setFieldValueByRow(Map<Integer, String> fieldIndex, List<Field> fields, Row row, T
            t) {
        AtomicInteger count = new AtomicInteger(0);
        for (Cell cell : row) {
            fields.stream()
                    .filter(it -> it.getName().equals(fieldIndex.get(cell.getColumnIndex())))
                    .findFirst().ifPresent(field -> setFieldValueByCell(field, cell, t, count));
        }
        return count.get();
    }

    public static <T> int setFieldValueByFieldType(Map<Integer, String> fieldIndex, List<Field> fields, Row
            row, T t) {
        AtomicInteger count = new AtomicInteger(0);
        for (Cell cell : row) {
            fields.stream()
                    .filter(it -> it.getName().equals(fieldIndex.get(cell.getColumnIndex())))
                    .findFirst().ifPresent(field -> setFieldValueByType(field, cell, t, count));
        }
        return count.get();
    }

    public static <T> T parseNumeric(Class<T> t, Object obj) {
        Object result = new Object();
        try {
            String var = String.valueOf(obj);
            Double dblValue = Double.parseDouble(var);
            switch (t.getTypeName()) {
                case "java.lang.Byte":
                    result = dblValue.byteValue();
                    break;
                case "java.lang.Short":
                    result = dblValue.shortValue();
                    break;
                case "java.lang.Integer":
                    result = dblValue.intValue();
                    break;
                case "java.lang.Long":
                    result = dblValue.longValue();
                    break;
                case "java.lang.Float":
                    result = dblValue.floatValue();
                    break;
                case "java.lang.Double":
                    result = dblValue;
                    break;
                default:
            }
        } catch (NumberFormatException e) {
            e.printStackTrace();
        }
        return (T) result;
    }

    public static <T> void setFieldValueByType(Field field, Cell cell, T t, AtomicInteger count) {
        field.setAccessible(true);
        try {
            cell.setCellType(Cell.CELL_TYPE_STRING);
            String cellValue = cell.getStringCellValue();
            field.set(t, parseValue(field.getType(), cellValue, field));
            if (field.get(t) != null) {
                count.addAndGet(1);
            }
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
    }

    public static <T> T parseValue(Class<T> t, Object obj, Field field) {
        Object result = new Object();
        try {
            String var = String.valueOf(obj);
            switch (t.getTypeName()) {
                case "java.lang.Byte":
                    result = ((Double) Double.parseDouble(var)).byteValue();
                    break;
                case "java.lang.Short":
                    result = ((Double) Double.parseDouble(var)).shortValue();
                    break;
                case "java.lang.Integer":
                    result = ((Double) Double.parseDouble(var)).intValue();
                    break;
                case "java.lang.Long":
                    result = ((Double) Double.parseDouble(var)).longValue();
                    break;
                case "java.lang.Float":
                    result = ((Double) Double.parseDouble(var)).floatValue();
                    break;
                case "java.util.Date":
                    SimpleDateFormat sdf;
                    if (field.isAnnotationPresent(JsonFormat.class)) {
                        JsonFormat ano = field.getAnnotation(JsonFormat.class);
                        sdf = new SimpleDateFormat(ano.pattern());
                    } else {
                        sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    }
                    result = sdf.parse(var);
                    break;
                case "java.lang.String":
                    result = var;
                    break;
                default:
                    result = var;
                    break;
            }
        } catch (NumberFormatException | ParseException e) {
            e.printStackTrace();
        }
        return (T) result;
    }
}
