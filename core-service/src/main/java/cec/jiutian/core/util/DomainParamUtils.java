package cec.jiutian.core.util;

import java.util.HashMap;
import java.util.Map;

/*
 * <p>数据域参数工具类，用于当前线程的数据域参数的临时存取</p>
 * <AUTHOR>
 * @Date 14:34 2022-3-28
 **/
public class DomainParamUtils {
    //可继承父线程数据的线程变量
    private static final InheritableThreadLocal<DomainParam> DOMAIN_PARAM_THREAD_LOCAL = new InheritableThreadLocal<>();

    private static DomainParam getDomainParam() {
        return DOMAIN_PARAM_THREAD_LOCAL.get();
    }

    public static void setDomainParam(DomainParam domainParam) {
/*        if (getDomainParam() == null) {
            DOMAIN_PARAM_THREAD_LOCAL.set(domainParam);
        }*/
        DOMAIN_PARAM_THREAD_LOCAL.set(domainParam);
    }

    public static void clearDomainParam() {
        DOMAIN_PARAM_THREAD_LOCAL.remove();
    }

    public static String getToken() {
        if (getDomainParam() != null) {
            return DOMAIN_PARAM_THREAD_LOCAL.get().getToken();
        } else {
            return null;
        }
    }

    public static void setToken(String token) {
        if (getDomainParam() != null) {
            getDomainParam().setToken(token);
        } else {
            DomainParam threadParam = new DomainParam();
            // 保存token
            threadParam.setToken(token);
            setDomainParam(threadParam);
        }
    }

    public static String getUri() {
        if (getDomainParam() != null) {
            return getDomainParam().getUri();
        } else {
            return null;
        }
    }

    public static void putParam(Object key, Object value) {
        if (getDomainParam() != null) {
            if (getDomainParam().getMap() == null) {
                Map<Object, Object> map = new HashMap<>();
                map.put(key, value);
                getDomainParam().setMap(map);
            } else {
                getDomainParam().getMap().put(key, value);
            }
        }
    }

    public static Object getParam(Object key) {
        if (getDomainParam() != null && getDomainParam().getMap() != null) {
            return getDomainParam().getMap().get(key);
        } else {
            return null;
        }
    }

    public static String getDataBaseName() {
        if (getDomainParam() != null) {
            return getDomainParam().getDataBaseName();
        } else {
            return null;
        }
    }

    /**
     * 数据域参数，存储当前请求的 token, uri,dataBaseName三个参数
     */
    public static class DomainParam {
        private String token;
        private String uri;
        private String dataBaseName;
        private Map<Object, Object> map;

        public String getToken() {
            return token;
        }

        public void setToken(String token) {
            this.token = token;
        }

        public String getUri() {
            return uri;
        }

        public void setUri(String uri) {
            this.uri = uri;
        }

        public String getDataBaseName() {
            return dataBaseName;
        }

        public void setDataBaseName(String dataBaseName) {
            this.dataBaseName = dataBaseName;
        }

        public Map<Object, Object> getMap() {
            return map;
        }

        public void setMap(Map<Object, Object> map) {
            this.map = map;
        }

    }
}
