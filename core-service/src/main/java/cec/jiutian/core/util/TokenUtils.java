package cec.jiutian.core.util;

import cec.jiutian.core.entity.TokenAnalysisEntity;
import cec.jiutian.core.service.TokenAnalysisService;
import lombok.extern.slf4j.Slf4j;

/**
 * @title: TokenUtils.java
 * @package cec.jiutian.core.util
 * @description: 功能说明（jira task id）
 * @author: <EMAIL>
 * @date: 2022-3-16 22:32
 * @version: 2.5.3
 */
@Slf4j
public class TokenUtils {
    private final TokenAnalysisService tokenAnalysisService;

    public TokenUtils(TokenAnalysisService tokenAnalysisService) {
        this.tokenAnalysisService = tokenAnalysisService;
    }
    public TokenAnalysisEntity getTokenAnalysisEntity() {
        String tokenStr = (String) BizParamUtils.getParam("token");
        TokenAnalysisEntity tokenAnalysisEntity = tokenAnalysisService.decodeToken(tokenStr);
        return tokenAnalysisEntity;
    }
    public String getToken(TokenAnalysisEntity entity) {
        return tokenAnalysisService.getToken(entity, false);
    }
}
