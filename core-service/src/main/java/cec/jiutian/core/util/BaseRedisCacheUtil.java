package cec.jiutian.core.util;

import cec.jiutian.core.comn.util.StringUtils;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import jakarta.annotation.PostConstruct;
import java.io.Serializable;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component("baseRedisCacheUtil")
public class BaseRedisCacheUtil {
    // 维护一个本类的静态变量
    private static BaseRedisCacheUtil baseRedisCacheUtil;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private RedisTemplate<String, Serializable> baseRedisTemplate;
    @Autowired
    private RedisTemplate<String, String> stringRedisTemplate;
    @Autowired
    private RedisTemplate<String, Long> longRedisTemplate;
    @Autowired
    private RedisTemplate<String, Integer> intRedisTemplate;

    /**
     * 将参数中的字符串值设置为键的值，不设置过期时间
     *
     * @param key
     * @param value 必须要实现 Serializable 接口
     */
    public static void set(String key, Serializable value) {
        try {
            baseRedisCacheUtil.baseRedisTemplate.opsForValue().set(key, value);
        } catch (Exception e) {
            log.error(e.getMessage());
        }

    }

    /**
     * 将java以json字符串方式对象存入redis中，不设置过期时间
     *
     * @param key
     * @param value 存入java对象
     */
    public static void set(String key, Object value) {
        try {
            baseRedisCacheUtil.stringRedisTemplate.opsForValue().set(key, JSONObject.toJSONString(value));
        } catch (Exception e) {
            log.error(e.getMessage());
        }

    }

    /**
     * 将参数中的字符串值设置为键的值，设置过期时间
     *
     * @param key
     * @param value   必须要实现 Serializable 接口
     * @param timeout
     */
    public static void set(String key, Serializable value, Long timeout) {
        try {
            baseRedisCacheUtil.baseRedisTemplate.opsForValue().set(key, value, timeout, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error(e.getMessage());
        }

    }

    /**
     * 将java以json字符串方式对象存入redis中，设置过期时间
     *
     * @param key
     * @param value   必须要实现 Serializable 接口
     * @param timeout
     */
    public static void set(String key, Object value, Long timeout) {
        try {
            baseRedisCacheUtil.stringRedisTemplate.opsForValue().set(key, JSONObject.toJSONString(value), timeout, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error(e.getMessage());
        }

    }


    /**
     * 获取与指定键相关的值
     *
     * @param key
     * @return
     */
    public static Serializable get(String key) {
        Serializable serializable = null;
        try {
            serializable = baseRedisCacheUtil.baseRedisTemplate.opsForValue().get(key);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return serializable;
    }

    /**
     * 获取与指定键相关的值
     *
     * @param key
     * @return
     */
    public static <T> T get(String key, Class<T> clazz) {
        String jsonString = null;
        try {
            jsonString = baseRedisCacheUtil.stringRedisTemplate.opsForValue().get(key);
            if(StringUtils.isEmpty(jsonString)){
                return null;
            }
            return JSONObject.parseObject(jsonString, clazz);
        } catch (Exception e) {
            log.error("获取失败 e={}",e);
        }
        return null;
    }

    /**
     * 将值放入List中
     * @param key
     * @param value
     */
    public static void rightPush(String key, String value) {
        try {
            baseRedisCacheUtil.redisTemplate.opsForList().rightPush(key, value);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }

    /**
     * 获取list所有值
     * @param key
     */
    public static List<String> range(String key) {
        try {
          return baseRedisCacheUtil.redisTemplate.opsForList().range(key, 0, -1);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return null;
    }

    /**
     * 设置某个键的过期时间
     *
     * @param key 键值
     * @param ttl 过期秒数
     */
    public static boolean expire(String key, Long ttl) {
        boolean flag = true;
        try {
            flag = baseRedisCacheUtil.baseRedisTemplate.expire(key, ttl, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return flag;
    }

    /**
     * 判断某个键是否存在
     *
     * @param key 键值
     */
    public static boolean hasKey(String key) {
        boolean flag = false;
        try {
            flag = baseRedisCacheUtil.baseRedisTemplate.hasKey(key);
        } catch (Exception e) {
            log.error(e.getMessage());

        }
        return flag;
    }

    /**
     * 查询某个键的过期时间
     */
    public static Long getExpire(String key, final TimeUnit unit) {
        return baseRedisCacheUtil.baseRedisTemplate.getExpire(key, unit);
    }

    /**
     * 向集合添加元素
     *
     * @param key
     * @param value
     * @return 返回值为设置成功的value数
     */
    public static Long addSet(String key, Serializable... value) {
        Long flag = 0L;
        try {

            flag = baseRedisCacheUtil.baseRedisTemplate.opsForSet().add(key, value);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return flag;
    }

    /**
     * 获取集合中的某个元素
     *
     * @param key
     * @return 返回值为redis中键值为key的value的Set集合
     */
    public static Set<Serializable> getSetMembers(String key) {
        Set<Serializable> serializables = new HashSet<Serializable>();
        try {

            serializables = baseRedisCacheUtil.baseRedisTemplate.opsForSet().members(key);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return serializables;
    }

    /**
     * 将给定分数的指定成员添加到键中存储的排序集合中
     *
     * @param key
     * @param value
     * @param score
     * @return
     */
    public static Boolean addSet(String key, Serializable value, double score) {
        boolean flag = false;
        try {

            flag = baseRedisCacheUtil.baseRedisTemplate.opsForZSet().add(key, value, score);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return flag;
    }

    /**
     * 返回指定排序集中给定成员的分数
     *
     * @param key
     * @param value
     * @return
     */
    public static Double getSetScore(String key, Serializable value) {
        double flag = 0;
        try {

            flag = baseRedisCacheUtil.baseRedisTemplate.opsForZSet().score(key, value);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return flag;
    }

    /**
     * 删除指定的键
     *
     * @param key
     * @return
     */
    public static Boolean delete(String key) {
        boolean flag = false;
        try {

            flag = baseRedisCacheUtil.baseRedisTemplate.delete(key);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return flag;
    }

    /**
     * 删除多个键
     *
     * @param keys
     * @return
     */
    public static Long delete(Collection<String> keys) {
        Long flag = 0L;
        try {
            flag = baseRedisCacheUtil.baseRedisTemplate.delete(keys);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return flag;
    }

    /**
     * hash类型的incr and get
     * <p>
     * 注意集群部署时候的初始值和步长（N）
     *
     * @param key
     * @param field
     * @param increment
     * @return
     * <AUTHOR>
     */
    public static Long hIncrBy(String key, Object field, long increment) {
        return baseRedisCacheUtil.baseRedisTemplate.opsForHash().increment(key, field, increment);
    }

    public static Long hIncrByForString(String key, Object field, long increment) {
        return baseRedisCacheUtil.stringRedisTemplate.opsForHash().increment(key, field, increment);
    }

    public static Long hIncrByForLong(String key, Object field, long increment) {
        return baseRedisCacheUtil.longRedisTemplate.opsForHash().increment(key, field, increment);
    }

    public static Long hIncrByForInt(String key, Object field, long increment) {
        return baseRedisCacheUtil.intRedisTemplate.opsForHash().increment(key, field, increment);
    }

    /**
     * hash类型的incr and get ,步长为1
     *
     * @param key
     * @param field
     * @param increment
     * @return
     * <AUTHOR>
     */
    public static Long hIncrByOne(String key, Object field) {
        return hIncrBy(key, field, 1L);
    }

    public static Long hIncrByOneForString(String key, Object field) {
        return hIncrByForString(key, field, 1L);
    }

    public static Long hIncrByOneForLong(String key, Object field) {
        return hIncrByForLong(key, field, 1L);
    }

    public static Long hIncrByOneForInt(String key, Object field) {
        return hIncrByForInt(key, field, 1L);
    }

    /**
     * 设置HASH值
     *
     * @param key
     * @param hashKey
     * @param value
     * <AUTHOR>
     */
    public static void hPutForString(String key, String hashKey, String value) {
        baseRedisCacheUtil.stringRedisTemplate.opsForHash().put(key, hashKey, value);
    }

    public static void hPutForLong(String key, String hashKey, long value) {
        baseRedisCacheUtil.longRedisTemplate.opsForHash().put(key, hashKey, value);
    }

    public static void hPutForInt(String key, String hashKey, int value) {
        baseRedisCacheUtil.intRedisTemplate.opsForHash().put(key, hashKey, value);
    }

    public static void hPut(String key, String hashKey, String value) {
        baseRedisCacheUtil.baseRedisTemplate.opsForHash().put(key, hashKey, value);
    }

    /**
     * 设置一组HASH值
     *
     * @param key
     * @param maps
     * <AUTHOR>
     */
    public static void hPutAll(String key, Map<String, String> maps) {
        baseRedisCacheUtil.baseRedisTemplate.opsForHash().putAll(key, maps);
    }

    public static void hPutAllForString(String key, Map<String, String> maps) {
        baseRedisCacheUtil.stringRedisTemplate.opsForHash().putAll(key, maps);
    }

    /**
     * 设置一组HASH值
     *
     * @param key
     * @param maps
     * <AUTHOR>
     */
    public static void hPutAllForObject(String key, Map<String, Object> objectMap) {
        baseRedisCacheUtil.baseRedisTemplate.opsForHash().putAll(key, objectMap);
    }

    /**
     * 设置HASH值
     *
     * @param key
     * @param maps
     * <AUTHOR>
     */
    public static void hPutForObject(String key, String hashKey, Object value) {
        baseRedisCacheUtil.baseRedisTemplate.opsForHash().put(key, hashKey, value);
    }

    /**
     * 仅当hashKey不存在时才设置
     *
     * @param key
     * @param hashKey
     * @param value
     * @return
     * <AUTHOR>
     */
    public static Boolean hPutIfAbsent(String key, String hashKey, String value) {
        return baseRedisCacheUtil.baseRedisTemplate.opsForHash().putIfAbsent(key, hashKey, value);
    }

    /**
     * 删除一个或多个哈希表字段
     *
     * @param key
     * @param fields
     * @return
     * <AUTHOR>
     */
    public static Long hDelete(String key, Object... fields) {
        return baseRedisCacheUtil.baseRedisTemplate.opsForHash().delete(key, fields);
    }

    /**
     * 查看哈希表 key 中，指定的字段是否存在
     *
     * @param key
     * @param field
     * @return
     * <AUTHOR>
     */
    public static boolean hExists(String key, String field) {
        return baseRedisCacheUtil.baseRedisTemplate.opsForHash().hasKey(key, field);
    }

    public static boolean hExistsForString(String key, String field) {
        return baseRedisCacheUtil.stringRedisTemplate.opsForHash().hasKey(key, field);
    }

    /**
     * 获取存储在哈希表中指定字段的值
     *
     * @param key
     * @param field
     * @return
     * <AUTHOR>
     */
    public static Object hGet(String key, String field) {
        return baseRedisCacheUtil.baseRedisTemplate.opsForHash().get(key, field);
    }

    /**
     * 获取所有给定字段的值
     *
     * @param key
     * @return
     * <AUTHOR>
     */
    public static Map<Object, Object> hGetAll(String key) {
        return baseRedisCacheUtil.baseRedisTemplate.opsForHash().entries(key);
    }

    /**
     * redis模糊查询
     *
     * @param prefix
     * @return
     */
    public static Set<String> getCacheKeys(String prefix) {
        return baseRedisCacheUtil.baseRedisTemplate.keys(prefix + "*");
    }

    @PostConstruct
    public void init() {
        baseRedisCacheUtil = this;
    }

}
