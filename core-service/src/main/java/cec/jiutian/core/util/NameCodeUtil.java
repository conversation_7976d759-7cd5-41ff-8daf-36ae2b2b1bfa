package cec.jiutian.core.util;

import cec.jiutian.core.comn.errorCode.BaseErrorCode;
import cec.jiutian.core.exception.MesErrorCodeException;
import cec.jiutian.core.feign.NameCodeServiceFeign;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/2/4
 */
@Slf4j
@Component
public class NameCodeUtil {
    private final NameCodeServiceFeign nameCodeServiceFeign;

    public NameCodeUtil(NameCodeServiceFeign nameCodeServiceFeign) {
        this.nameCodeServiceFeign = nameCodeServiceFeign;
    }

    public List<String> getNameCode(String ruleName, int num, Map<String, String> variableMap) {
        List<String> nameCode = nameCodeServiceFeign.getNameCode(ruleName, num, variableMap);
        if (nameCode == null) {
            // 当返回的数据为null时，只有一种可能，未配置命名规则，此时抛出具体的异常
            throw  new MesErrorCodeException("名称为" + ruleName + "的命名规则未在该系统下进行配置，请检查数据！");
        }
        return nameCode;
    }

    /**
     * <p>该方法内部调用了getNameCode方法，封装了获取失败后的报错，即调用此方法不需要判断返回的数组是否有值，若无值则必定会抛错</p>
     *
     * @param ruleName    命名规则
     * @param num         需要获取的nameCode数量
     * @param variableMap 自定义变量，若无可填null
     * @return 生成的nameCode数组
     */
    public List<String> generatorNameCode(String ruleName, int num, Map<String, String> variableMap) {
        if (variableMap == null) {
            variableMap = new HashMap<>();
        }
        List<String> nameCode = getNameCode(ruleName, num, variableMap);
        if (CollectionUtils.isEmpty(nameCode)) {
            MesErrorCodeException exception = new MesErrorCodeException(BaseErrorCode.FEIGN_GET_NAME_FAIL.getCode());
            log.error("{}", exception.getMessage(), exception);
            throw exception;
        }
        return nameCode;
    }

    /**
     * 根据变量和规则名获取单个主键
     *
     * @param ruleName    命名规则名称
     * @param variableMap 规则中的变量
     * @return 单个code
     */
    public String generatorNameCode(String ruleName, Map<String, String> variableMap) {
        return generatorNameCode(ruleName, 1, variableMap).get(0);
    }

    /**
     * 根据命名规则返回单个code，如果未查询到code会抛出错误。
     *
     * @param ruleName 命名规则名称
     * @return 单个code
     */
    public String generatorNameCode(String ruleName) {
        return generatorNameCode(ruleName, 1, new HashMap<>()).get(0);
    }

    /**
     * <p>该方法内部调用了getNameCode方法，封装了获取失败后的报错，即调用此方法不需要判断返回的数组是否有值，若无值则必定会抛错</p>
     *
     * @param ruleName 命名规则
     * @param num      需要获取的nameCode数量
     * @return 生成的nameCode数组
     */
    public List<String> generatorNameCode(String ruleName, int num) {
        return generatorNameCode(ruleName, num, new HashMap<>());
    }
}
