package cec.jiutian.core.util;

import cec.jiutian.core.feign.PrintRecordServiceFeign;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @author: bait
 * @create: 2022-07-15 14:49
 * @description: 打印记录
 **/
@Slf4j
@Component
public class PrintRecordUtils {

    private final PrintRecordServiceFeign printRecordServiceFeign;

    public PrintRecordUtils(PrintRecordServiceFeign printRecordServiceFeign) {
        this.printRecordServiceFeign = printRecordServiceFeign;
    }

    /**
     * @param printRecordCreateStr 打印记录创建Json字符串
     * @return
     */
    public String createPrintRecord(String printRecordCreateStr) {
        return printRecordServiceFeign.createPrintRecord(printRecordCreateStr);
    }

}
