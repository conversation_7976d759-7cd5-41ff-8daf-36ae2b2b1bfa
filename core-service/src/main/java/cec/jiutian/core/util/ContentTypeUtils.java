package cec.jiutian.core.util;

import cec.jiutian.core.comn.constant.ContentTypeConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;

@Slf4j
public class ContentTypeUtils {

    public static String getContentType(Class constantClass, String fileType) {
        String contentType = null;
        while (null != constantClass) {

            contentType = getContentType2(constantClass, fileType);
            if (null != contentType) {
                return contentType;
            }
            // 获取所有的接口信息
            Class<?>[] interfaces = constantClass.getInterfaces();
            for (int j = 0; j < interfaces.length; j++) {

                contentType = getContentType2(interfaces[j], fileType);
                if (null != contentType) {
                    return contentType;
                }
            }
            constantClass = constantClass.getSuperclass();
        }
        return ContentTypeConstant.DEFAULT;
    }

    private static String getContentType2(Class constantClass, String fileType) {
        String contentType = null;
        Field[] fileds = constantClass.getDeclaredFields();
        for (Field field : fileds) {
            //设置字段可访问， 否则无法访问private修饰的变量值
            field.setAccessible(true);
            try {
                // 获取指定对象的当前字段的值
                String fieldProp = field.getName();
                if (StringUtils.equalsIgnoreCase(fileType, fieldProp)) {
                    contentType = field.get(constantClass).toString();
                    break;
                }
            } catch (IllegalAccessException e) {
                log.error("{}", e.getMessage(), e);
                return null;
            }
        }
        return contentType;
    }

//    public static void main(String[] args) {
//        System.out.println(ContentTypeUtils.getContentType(ContentTypeConstant.class, "doc"));
//    }
}
