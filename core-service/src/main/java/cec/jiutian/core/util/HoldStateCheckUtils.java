package cec.jiutian.core.util;

import cec.jiutian.core.entity.AbstractDomain;
import cec.jiutian.core.exception.MesErrorCodeException;
import cec.jiutian.security.base.entity.BaseEntity;
import cec.jiutian.security.base.entity.HoldEntity;
import cec.jiutian.core.base.BaseDomainService;
import cec.jiutian.core.comn.constant.WipInventoryConstant;
import cec.jiutian.core.comn.errorCode.BaseErrorCode;
import cec.jiutian.core.comn.util.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.Serializable;

/*
 * <AUTHOR>
 * @Date 15:14 2022-5-26
 **/
@Slf4j
@Component
public class HoldStateCheckUtils {
    private final static String ON_HOLD = WipInventoryConstant.HoldState.STATE_ONHOLD;
    private final static String NOT_ON_HOLD = WipInventoryConstant.HoldState.STATE_NOT_ONHOLD;

    /**
     * Domain中Entity的 冻结校验
     */
    public static <V extends BaseEntity, D extends AbstractDomain<V>> void checkOnHold(D d) {
        V entity = d.getEntity();
        if (null == entity) {
            return;
        }
        HoldEntity holdEntity = new HoldEntity();
        if (!BeanUtils.ClassFieldContain(entity.getClass(), HoldEntity.class)) {
            return;
        }
        BeanUtils.copyProperties(entity, holdEntity);
        if (!ON_HOLD.equals(holdEntity.getHoldState())) {
            MesErrorCodeException exception = new MesErrorCodeException(BaseErrorCode.DATA_IS_NOT_ON_HOLD.getCode());
            log.error("{}", exception.getMessage(), exception);
            throw exception;
        }
    }

    /**
     * Domain中Entity的 非冻结校验
     */
    public static <V extends BaseEntity, D extends AbstractDomain<V>> void checkNotOnHold(D d) {
        V entity = d.getEntity();
        if (null == entity) {
            return;
        }
        HoldEntity holdEntity = new HoldEntity();
        if (!BeanUtils.ClassFieldContain(entity.getClass(), HoldEntity.class)) {
            return;
        }
        BeanUtils.copyProperties(entity, holdEntity);
        if (!NOT_ON_HOLD.equals(holdEntity.getHoldState())) {
            MesErrorCodeException exception = new MesErrorCodeException(BaseErrorCode.DATA_IS_ON_HOLD.getCode());
            log.error("{}", exception.getMessage(), exception);
            throw exception;
        }
    }

    /**
     * Domain中Entity的 冻结信息填写
     */
    public static <V extends BaseEntity, D extends AbstractDomain<V>> void setOnHoldInfo(D d) {
        V entity = d.getEntity();
        if (null == entity) {
            return;
        }
        HoldEntity holdEntity = new HoldEntity();
        if (!BeanUtils.ClassFieldContain(entity.getClass(), HoldEntity.class)) {
            return;
        }
        BeanUtils.copyProperties(entity, holdEntity);
        holdEntity.setHoldState(ON_HOLD);
        holdEntity.setHoldDate(BizParamUtils.getLastEventTime());
        holdEntity.setHoldReasonDescription((String) BizParamUtils.getParam("holdReasonDescription"));
        BeanUtils.copyProperties(holdEntity, entity);
        d.setAuditField();
    }

    /**
     * Domain中Entity的 解冻信息填写
     */
    public static <V extends BaseEntity, D extends AbstractDomain<V>> void setNotOnHoldInfo(D d) {
        V entity = d.getEntity();
        if (null == entity) {
            return;
        }
        HoldEntity holdEntity = new HoldEntity();
        if (!BeanUtils.ClassFieldContain(entity.getClass(), HoldEntity.class)) {
            return;
        }
        BeanUtils.copyProperties(entity, holdEntity);
        holdEntity.setHoldState(NOT_ON_HOLD);
        holdEntity.setHoldDate(null);
        holdEntity.setHoldReasonDescription(null);
        BeanUtils.copyProperties(holdEntity, entity);
        d.setAuditField();
    }

    /**
     * 对Domain做冻结操作
     *
     * @param pk      实体的PK。
     * @param service 主表数据对应的service的实例化对象
     * @param <V>     主表数据类型
     * @param <PK>    主表数据主键类型
     * @param <S>     主表数据对应的service类型
     */
    public static <V extends BaseEntity, D extends AbstractDomain<V>, PK extends Serializable, S extends BaseDomainService<V, D, PK>> D doOnHold(PK pk, S service) {
        D d = service.checkExistById(pk);
        checkNotOnHold(d);
        setOnHoldInfo(d);
        service.update(d);
        return d;
    }

    /**
     * 对Domain做解冻操作
     *
     * @param pk      实体的PK。
     * @param service 主表数据对应的service的实例化对象
     * @param <V>     主表数据类型
     * @param <PK>    主表数据主键类型
     * @param <S>     主表数据对应的service类型
     */
    public static <V extends BaseEntity, D extends AbstractDomain<V>, PK extends Serializable, S extends BaseDomainService<V, D, PK>> D doNotOnHold(PK pk, S service) {
        D d = service.checkExistById(pk);
        checkOnHold(d);
        setNotOnHoldInfo(d);
        service.update(d);
        return d;
    }

}