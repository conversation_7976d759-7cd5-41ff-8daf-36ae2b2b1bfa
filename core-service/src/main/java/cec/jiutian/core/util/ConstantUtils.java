package cec.jiutian.core.util;

import cec.jiutian.core.comn.constant.FileTypeConstant;
import cec.jiutian.core.comn.util.StringUtils;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;

@Slf4j
public class ConstantUtils {

    /**
     * 判断是否是已有常量
     *
     * @param fileType
     * @return
     */
    public static Boolean contains(Class constantClass, String fileType) {

        Boolean existBoolean = false;

        while (null != constantClass) {

            existBoolean = checkFiledType(constantClass, fileType);
            if (existBoolean) {
                return existBoolean;
            }
            // 获取所有的接口信息
            Class<?>[] interfaces = constantClass.getInterfaces();
            for (int j = 0; j < interfaces.length; j++) {

                existBoolean = checkFiledType(interfaces[j], fileType);
                if (existBoolean) {
                    return existBoolean;
                }
            }
            constantClass = constantClass.getSuperclass();
        }
        return existBoolean;
    }

    public static Boolean checkFiledType(Class constantClass, String fileType) {
        Boolean existBoolean = false;
        Field[] fileds = constantClass.getDeclaredFields();
        for (Field field : fileds) {
            //设置字段可访问， 否则无法访问private修饰的变量值
            field.setAccessible(true);
            try {
                // 获取指定对象的当前字段的值
                String fieldVal = field.get(constantClass).toString();
                if (StringUtils.equalsIgnoreCase(fileType, fieldVal)) {
                    existBoolean = true;
                    break;
                }
            } catch (IllegalAccessException e) {
                log.error("{}", e.getMessage(), e);
                return existBoolean;
            }
        }
        return existBoolean;
    }

    public static String getFiledTypes(Class constantClass) {
        StringBuilder sb = new StringBuilder();
        Field[] fileds = constantClass.getDeclaredFields();
        for (Field field : fileds) {
            //设置字段可访问， 否则无法访问private修饰的变量值
            field.setAccessible(true);
            try {
                // 获取指定对象的当前字段的值
                if (field.get(constantClass) instanceof String[]) {
                    String[] strings = (String[]) field.get(constantClass);
                    sb.append(String.join(" ", strings));
                } else {
                    String fieldVal = field.get(constantClass).toString();
                    sb.append(fieldVal + " ");
                }
            } catch (IllegalAccessException e) {
                log.error("{}", e.getMessage(), e);
            }
        }
        return sb.toString();
    }

    public static String getFileTypes(Class constantClass) {

        StringBuilder sb = new StringBuilder();

        while (null != constantClass) {

            String fieldVal = getFiledTypes(constantClass);
            sb.append(fieldVal + " ");
            // 获取所有的接口信息
            Class<?>[] interfaces = constantClass.getInterfaces();
            for (int j = 0; j < interfaces.length; j++) {

                String fieldVal1 = getFiledTypes(constantClass);

                sb.append(fieldVal1 + " ");
            }
            constantClass = constantClass.getSuperclass();
        }
        return sb.toString();
    }

    public static Boolean contains(String fileType) {
        return StringUtils.containsAny(fileType, FileTypeConstant.AUDIO_TYPE)
                || StringUtils.containsAny(fileType, FileTypeConstant.IMAGE_TYPE)
                || StringUtils.containsAny(fileType, FileTypeConstant.VIDEO_TYPE)
                || StringUtils.containsAny(fileType, FileTypeConstant.OFFICE_TYPE);
    }
}
