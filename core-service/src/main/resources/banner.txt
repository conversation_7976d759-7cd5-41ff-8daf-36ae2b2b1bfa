${AnsiColor.BLUE}
 ▄▄▄▄▄▄▄▄▄▄▄  ▄▄▄▄▄▄▄▄▄▄▄  ▄▄▄▄▄▄▄▄▄▄▄  ▄▄▄▄▄▄▄▄▄▄▄  ▄▄▄▄▄▄▄▄▄▄▄
▐░░░░░░░░░░░▌▐░░░░░░░░░░░▌▐░░░░░░░░░░░▌▐░░░░░░░░░░░▌▐░░░░░░░░░░░▌
▐░█▀▀▀▀▀▀▀▀▀ ▐░█▀▀▀▀▀▀▀▀▀ ▐░█▀▀▀▀▀▀▀▀▀  ▀▀▀▀▀█░█▀▀▀  ▀▀▀▀█░█▀▀▀▀
▐░▌          ▐░▌          ▐░▌                ▐░▌         ▐░▌
▐░▌          ▐░█▄▄▄▄▄▄▄▄▄ ▐░▌                ▐░▌         ▐░▌
▐░▌          ▐░░░░░░░░░░░▌▐░▌                ▐░▌         ▐░▌
▐░▌          ▐░█▀▀▀▀▀▀▀▀▀ ▐░▌                ▐░▌         ▐░▌
▐░▌          ▐░▌          ▐░▌                ▐░▌         ▐░▌
▐░█▄▄▄▄▄▄▄▄▄ ▐░█▄▄▄▄▄▄▄▄▄ ▐░█▄▄▄▄▄▄▄▄▄  ▄▄▄▄▄█░▌         ▐░▌
▐░░░░░░░░░░░▌▐░░░░░░░░░░░▌▐░░░░░░░░░░░▌▐░░░░░░░▌         ▐░▌
 ▀▀▀▀▀▀▀▀▀▀▀  ▀▀▀▀▀▀▀▀▀▀▀  ▀▀▀▀▀▀▀▀▀▀▀  ▀▀▀▀▀▀▀           ▀

  _____           _        ___    ____    _____        ___        _____
 |  ___|   __ _  | |__    / _ \  / ___|  |___ /       / _ \      |___ /
 | |_     / _` | | '_ \  | | | | \___ \    |_ \      | | | |       |_ \
 |  _|   | (_| | | |_) | | |_| |  ___) |  ___) |  _  | |_| |  _   ___) |
 |_|      \__,_| |_.__/   \___/  |____/  |____/  (_)  \___/  (_) |____/



 ${AnsiColor.BRIGHT_RED}
 Spring Boot Version: ${spring-boot.version}
${AnsiColor.WHITE}