<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>
    <groupId>cec.jiutian</groupId>
    <artifactId>fabos-security</artifactId>
    <version>3.2.0</version>
    <packaging>pom</packaging>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.2.3</version>
    </parent>

    <properties>

        <fabos-security.version>3.2.0</fabos-security.version>
        <checkstyle.plugin.version>3.1.2</checkstyle.plugin.version>
        <checkstyle.version>8.9</checkstyle.version>
        <java.version>17</java.version>
        <lombok.version>1.18.22</lombok.version>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <persistence.api.version>1.0.2</persistence.api.version>
        <spring.cloud.version>2023.0.0</spring.cloud.version>
        <spring.test.version>2.2.5.RELEASE</spring.test.version>
        <swagger.version>2.8.0</swagger.version>
        <swagger.starter.version>1.7.0.RELEASE</swagger.starter.version>
        <mysql-connector-java.version>8.0.29</mysql-connector-java.version>
        <postgresql.version>42.4.0</postgresql.version>
        <maven.release.plugin.version>3.0.0</maven.release.plugin.version>
        <spring.cloudAlibaba.version>2023.0.1.0</spring.cloudAlibaba.version>
        <ojdbc.version>23.3.0.23.09</ojdbc.version>

    </properties>

    <modules>
        <module>license</module>
        <module>fabos-eureka-server</module>
        <module>fabos-gateway-server</module>
        <module>base-interface</module>
        <module>core-comn</module>
        <module>core-service</module>
        <module>fabos-gateway-service</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring.cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring.cloudAlibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>com.spring4all</groupId>
                <artifactId>swagger-spring-boot-starter</artifactId>
                <version>${swagger.starter.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-test</artifactId>
                <version>${spring.test.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger2</artifactId>
                <version>${swagger.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.persistence</groupId>
                <artifactId>persistence-api</artifactId>
                <version>${persistence.api.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-checkstyle-plugin</artifactId>
                    <version>${checkstyle.plugin.version}</version>
                    <dependencies>
                        <!--指定依赖的checkstyle版本-->
                        <dependency>
                            <groupId>com.puppycrawl.tools</groupId>
                            <artifactId>checkstyle</artifactId>
                            <version>${checkstyle.version}</version>
                        </dependency>
                    </dependencies>
                    <!--指定配置文件-->
                    <configuration>
                        <configLocation>http://**************:9997/check/fabos_checks.xml</configLocation>
                        <encoding>UTF-8</encoding>
                        <consoleOutput>true</consoleOutput>
                        <failsOnError>true</failsOnError>
                        <linkXRef>false</linkXRef>
                    </configuration>
                    <executions>
                        <execution>
                            <id>verify</id>
                            <phase>verify</phase>
                            <goals>
                                <goal>check</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.9.0</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-release-plugin</artifactId>
                <version>${maven.release.plugin.version}</version>
                <configuration>
                    <autoVersionSubmodules>true</autoVersionSubmodules>
                    <tagNameFormat>security-@{project.version}-RELEASE</tagNameFormat>
                    <preparationGoals>clean verify</preparationGoals>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.owasp</groupId>
                <artifactId>dependency-check-maven</artifactId>
                <version>8.2.1</version>
                <executions>
                    <execution>
                        <configuration>
                            <skip>true</skip>
                        </configuration>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <distributionManagement>
        <repository>
            <id>releases</id>
            <name>Nexus Release Repository</name>
            <url>http://**************:9091/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <name>Nexus Snapshot Repository</name>
            <url>http://**************:9091/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <scm>
        <connection>scm:git:http://*************/fabos/fabos-security.git</connection>
        <developerConnection>scm:git:http://*************/fabos/fabos-security.git</developerConnection>
        <url>http://*************/fabos/fabos-security.git</url>
        <tag>HEAD</tag>
    </scm>
</project>
