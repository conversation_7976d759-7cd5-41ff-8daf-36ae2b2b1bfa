app.id = fabos-gateway-server
spring.application.name=fabos-gateway-server
management.endpoints.web.exposure.include = *
apollo.bootstrap.enabled = true
apollo.bootstrap.eagerLoad.enabled = true
build.version = 3.0.1
#??Spring boot????web????????gateway???? spring-boot-starter-webflux??web?????
spring.main.web-application-type=reactive

#æµè¯ä½¿ç¨ ReadBodyPredicateFactory
#spring.cloud.gateway.routes[0].id=api-a
#spring.cloud.gateway.routes[0].uri= lb://mes-service-base-alarm
#spring.cloud.gateway.routes[0].predicates[0]=Path=/api-base-alarm/**
#spring.cloud.gateway.routes[0].predicates[1].name= ReadBodyPredicateFactory
#spring.cloud.gateway.routes[0].predicates[1].args.inClass=#{T(String)}
#spring.cloud.gateway.routes[0].predicates[1].args.predicate=#{@readBodyPredicate}
#spring.cloud.gateway.routes[0].filters[0]=RewritePath=/api-base-alarm(?<segment>/?.*), ${segment}
