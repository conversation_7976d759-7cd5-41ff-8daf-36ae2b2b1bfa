package cec.jiutian.gateway.filter;

import brave.Tracer;
import cec.jiutian.gateway.message.TransactionStreamClient;
import cec.jiutian.gateway.transaction.Transaction;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.cloud.gateway.filter.NettyWriteResponseFilter;
import org.springframework.cloud.gateway.support.ServerWebExchangeUtils;
import org.springframework.core.Ordered;
import org.springframework.http.HttpStatus;
import org.springframework.http.codec.HttpMessageReader;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.web.reactive.function.server.HandlerStrategies;
import org.springframework.web.reactive.function.server.ServerRequest;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.net.URI;
import java.util.Date;
import java.util.List;

import static org.springframework.cloud.gateway.support.ServerWebExchangeUtils.CACHED_SERVER_HTTP_REQUEST_DECORATOR_ATTR;


@Component
@Slf4j
//@EnableBinding(TransactionStreamClient.class)
public class TransactionFilter implements GlobalFilter, Ordered {

    @Value("${build.version}")
    private String buildVersion;

    @Value("${transaction.log.enabled:false}")
    private Boolean enabled;

    private  TransactionStreamClient transactionStreamClient;

    private final Tracer tracer;

    private static final List<HttpMessageReader<?>> messageReaders = HandlerStrategies
            .withDefaults().messageReaders();

    private static final String CACHE_REQUEST_BODY_OBJECT_KEY = "cachedRequestBodyObject";

    public TransactionFilter(Tracer tracer) {
        this.tracer = tracer;
    }

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {

        ServerHttpRequest request = exchange.getRequest();

        URI requestUri = request.getURI();
        //只记录 http 请求(包含 https)
        String schema = requestUri.getScheme();
        if ((!"http".equals(schema) && !"https".equals(schema))){
            return chain.filter(exchange);
        }

        String traceId = tracer.currentSpan().context().traceIdString();
        Date startDate = new Date();
        exchange.getAttributes().put("startDate",startDate);
        log.debug("start to receive request, request url is :" + request.getURI()  + "/" + request.getRemoteAddress() + ", trace Id is " + traceId);
        ServerHttpResponse response = exchange.getResponse();
        response.getHeaders().set("TraceID",traceId);
        response.getHeaders().set("Server-Version",buildVersion);

        String contentType = request.getHeaders().getFirst("Content-Type");
        log.debug("X-Real-IP is : "+ request.getHeaders().getFirst("X-Real-IP"));
        log.debug("X-Forwarded-For is : "+ request.getHeaders().getFirst("X-Forwarded-For"));
        Object cachedBody = exchange.getAttribute(CACHE_REQUEST_BODY_OBJECT_KEY);
        Transaction transaction = new Transaction();
        //      send transaction log only for POST method
        if (contentType != null && !"multipart/form-data".startsWith(contentType) && enabled) {
            if (cachedBody == null) {
                return ServerWebExchangeUtils.cacheRequestBodyAndRequest(exchange, (serverHttpRequest) -> {
                    final ServerRequest serverRequest = ServerRequest.create(exchange.mutate()
                            .request(serverHttpRequest).build(), messageReaders);
                    return serverRequest.bodyToMono((String.class))
                            .doOnNext(objectValue -> {
                                exchange.getAttributes()
                                        .put(CACHE_REQUEST_BODY_OBJECT_KEY, objectValue);
                            }).then(Mono.defer(() -> {
                                ServerHttpRequest cachedRequest =
                                        exchange.getAttribute(CACHED_SERVER_HTTP_REQUEST_DECORATOR_ATTR);
                                Assert.notNull(cachedRequest, "cache request shouldn't be null");
                                exchange.getAttributes().remove(CACHED_SERVER_HTTP_REQUEST_DECORATOR_ATTR);

//                              在return chain.filter 之前的代码为 Pre filter 执行的逻辑,之后为Post filter 执行的逻辑
                                return chain.filter(exchange.mutate().request(cachedRequest).build()).then(Mono.defer(() -> {

                                    assembleTransaction(exchange, transaction);
                                    log.debug("trace Id is " + traceId + ", cachedBody is null, requestBody is "+transaction.getRequestPayload());
//                                    transactionStreamClient.output().send(MessageBuilder.withPayload(transaction).build());
                                    return Mono.empty();
                                }));
                            }));
                });
            } else {
                return chain.filter(exchange).then(Mono.defer(() -> {

                    assembleTransaction(exchange, transaction);
                    log.debug("trace Id is " + traceId + ", cachedBody is not null, requestBody is "+transaction.getRequestPayload());
//                    transactionStreamClient.output().send(MessageBuilder.withPayload(transaction).build());
                    return Mono.empty();
                }));
            }
        } else {
            return chain.filter(exchange);
        }
    }

    @Override
    public int getOrder() {
        return NettyWriteResponseFilter.WRITE_RESPONSE_FILTER_ORDER - 1;
    }


    private void assembleTransaction(ServerWebExchange exchange,  Transaction transaction)  {
        String requestPayload = exchange.getAttribute(CACHE_REQUEST_BODY_OBJECT_KEY);
        Date endDate = new Date();
        Date startDate = exchange.getAttribute("startDate")!=null?(Date)exchange.getAttribute("startDate"):new Date();
        float responseTime = (endDate.getTime() - startDate.getTime());
        String functionName = "Transaction";
        String requestUrl = exchange.getRequest().getURI().getRawPath();
        if(StringUtils.isNotEmpty(requestUrl)) {
            String[] split = requestUrl.split("/",3);
            functionName = split[1];
        }

        transaction.setCaller(exchange.getRequest().getHeaders().getFirst("Initiator"));
        transaction.setEndDate(endDate);
        transaction.setErrorIndicator(HttpStatus.OK==exchange.getResponse().getStatusCode()?false:true);
        transaction.setFunctionName(functionName);
        transaction.setRequestPayload(requestPayload);
        transaction.setRequestUrl(requestUrl);
        transaction.setStartDate(startDate);
        transaction.setTransactionId(tracer.currentSpan().context().traceIdString());
        transaction.setRemoteIp(exchange.getRequest().getHeaders().getFirst("X-Real-IP"));
        transaction.setClientVersion(exchange.getRequest().getHeaders().getFirst("Client-Version"));
        transaction.setServerVersion(buildVersion);
        transaction.setRemoteURI(exchange.getRequest().getHeaders().getFirst("X-http-Referer"));
        transaction.setResponseTime(responseTime);
        transaction.setResponseStatusCode(exchange.getResponse().getStatusCode().toString());
    }

}
