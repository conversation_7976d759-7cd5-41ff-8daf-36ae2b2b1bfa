package cec.jiutian.gateway.filter;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTDecodeException;
import com.auth0.jwt.exceptions.TokenExpiredException;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfig;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfigChangeListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.PathMatcher;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Slf4j
@Component
public class WebRequestFilter implements GlobalFilter, Ordered {
    private static final String SECRET_KEY = "3f7e6855fd9454956d2ead0b1e122482";

    @ApolloConfig
    private Config appConfig;

    private static final String AUTHENTICATION_HEADER = "Authorization";
    private static final String AUTHENTICATION_PREFIX = "Bearer ";
    private static final String EXCLUDE_KEY = "mes.gateway.excludePatterns";
    private static String excludeURI = "";

    @Value("${mes.gateway.excludePatterns}")
    public void setExcludeURI(String excludeURI) {
        WebRequestFilter.excludeURI = excludeURI;
    }

    @ApolloConfigChangeListener(interestedKeys = {EXCLUDE_KEY})
    private void onChange(ConfigChangeEvent changeEvent) {
        excludeURI = appConfig.getProperty(EXCLUDE_KEY, "");
    }

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        String requestPath = exchange.getRequest().getURI().getRawPath();

        if(isMatchExclude(requestPath)) {
            return chain.filter(exchange) ;
        }
        if(!isMatchInclude(requestPath)) {
            exchange.getResponse().setStatusCode(HttpStatus.UNAUTHORIZED);
            exchange.getResponse().getHeaders().set("Error-Message", "Authorization to the service interface cannot be accessed.");
            return exchange.getResponse().setComplete();
//            throw new RuntimeException("Authorization to the service interface cannot be accessed.");
        } else {
            String authorization = exchange.getRequest().getHeaders().getFirst(AUTHENTICATION_HEADER);
            if(null == authorization) {
                exchange.getResponse().setStatusCode(HttpStatus.UNAUTHORIZED);
                exchange.getResponse().getHeaders().set("Error-Message", "Authorization does not exist, please confirm.");
                return exchange.getResponse().setComplete();
//                throw new RuntimeException("Authorization does not exist, please confirm.");
            }
            if(!authorization.startsWith("Bearer ")){
                exchange.getResponse().setStatusCode(HttpStatus.UNAUTHORIZED);
                exchange.getResponse().getHeaders().set("Error-Message", "Please enter the correct Authorization.");
                return exchange.getResponse().setComplete();
//                throw new RuntimeException("Please enter the correct Authorization.");
            }
            String authorizationKey = authorization.replace(AUTHENTICATION_PREFIX,"");
            try {
                String userId = JWT.decode(authorizationKey).getAudience().get(0);
                if(StringUtils.isNotBlank(userId)) {
                    JWTVerifier jwtVerifier = JWT.require(Algorithm.HMAC256(SECRET_KEY)).build();
                    jwtVerifier.verify(authorizationKey);
                }
            } catch (JWTDecodeException e) {
                exchange.getResponse().setStatusCode(HttpStatus.UNAUTHORIZED);
                exchange.getResponse().getHeaders().set("Error-Message","Authorization verify failure, cannot be accessed.");
                return exchange.getResponse().setComplete();
//                throw new RuntimeException("Authorization verify failure, cannot be accessed.");
            } catch (TokenExpiredException e) {
                exchange.getResponse().setStatusCode(HttpStatus.UNAUTHORIZED);
                exchange.getResponse().getHeaders().set("Error-Message","Authorization expired, expiration time is：" + JWT.decode(authorizationKey).getExpiresAt());
                return exchange.getResponse().setComplete();
//                throw new RuntimeException("Authorization expired, expiration time is：" + JWT.decode(authorizationKey).getExpiresAt());
            }
        }
        return chain.filter(exchange);
    }

    @Override
    public int getOrder() {
        return -100;
    }

    private List<String> includePatterns() {
        List<String> list = new ArrayList<>();
        list.add("/api-*/**");
        list.add("/mes-service-*/**");
        return list;
    }

    private boolean isMatchExclude(String uri){
        PathMatcher matcher = new AntPathMatcher();
        List<String> excludeResources = excludeResources();
        if (CollectionUtils.isNotEmpty(excludeResources)) {
            for (String pattern : excludeResources) {
                if(matcher.match(pattern, uri)) {
                    return true;
                }
            }
        }
        return false;
    }

    private boolean isMatchInclude(String uri){
        PathMatcher matcher = new AntPathMatcher();
        for (String pattern : includePatterns()) {
            if(matcher.match(pattern, uri)) {
                return true;
            }
        }
        return false;
    }


    public List<String> excludeResources() {
        if(StringUtils.isNotBlank(excludeURI)) {
            return Arrays.asList(excludeURI.split(","));
        }
        return null;
    }

    public List<String> includeResources() {
        String includeURI = appConfig.getProperty("mes.gateway.includePatterns", "");
        if(StringUtils.isNotBlank(includeURI)) {
            return Arrays.asList(includeURI.split(","));
        }
        return null;
    }
}
