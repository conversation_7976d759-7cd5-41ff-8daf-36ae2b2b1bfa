package cec.jiutian.gateway.transaction;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> Y
 * @version 1.0
 * @date 2019/6/19 9:16 PM
 */
@Data
public class Transaction {
    private static final long serialVersionUID = 2158467915795729405L;

    private String caller;

    private String requestPayload;

    private String responsePayload;

    private String requestUrl;

    private String responseStatusCode;

    private Boolean errorIndicator = false;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date startDate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date endDate;

    private float responseTime;

    private String transactionId;

    private String functionName;

    private String remoteIp;

    private String clientVersion;

    private String serverVersion;

    private String remoteURI;
}
