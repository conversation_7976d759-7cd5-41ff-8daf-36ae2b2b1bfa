package cec.jiutian;

import ch.qos.logback.classic.LoggerContext;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryCustomizer;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.Bean;

import java.util.TimeZone;

@SpringBootApplication
@EnableDiscoveryClient
@EnableApolloConfig
@Slf4j
public class FabosGatewayServerApplication {

    @Autowired

    public static void main(String[] args) {
        TimeZone.setDefault(TimeZone.getTimeZone("Asia/Shanghai"));
        SpringApplication.run(FabosGatewayServerApplication.class, args);
        log.info("Gateway is already started");
    }

//    测试router
//    @Bean
//    public RouteLocator routes(RouteLocatorBuilder builder) {
//        return builder.routes()
//                .route("1", r -> r.path("/api-base-alarm/**")
//                        .and()
//                        .readBody(String.class, requestBody -> true)
//                        .filters(f -> f.filter(logRequestBodyGatewayFilterFactory.apply(new LogRequestBodyGatewayFilterFactory.Config())))
//                        .uri("lb://MES-SERVICE-BASE-ALARM"))
//                .build();
//    }


    @Bean
    MeterRegistryCustomizer<MeterRegistry> configurer(@Value("${spring.application.name}") String applicationName){
        return registry -> registry.config().commonTags("application", applicationName);
    }

    @Bean
    public boolean removeLogAppender(@Value("${logback.detach.appender:FILE}") String appenderName) {
        // 第一步：获取日志上下文
        LoggerContext lc = (LoggerContext) LoggerFactory.getILoggerFactory();
        // 第二步：获取日志对象 （日志是有继承关系的，关闭上层，下层如果没有特殊说明也会关闭）
        ch.qos.logback.classic.Logger rootLogger = lc.getLogger("root");
        // 第三步：移除 appender
        return rootLogger.detachAppender(appenderName);
    }
}
