package cec.jiutian.security.base.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;

/**
 * <AUTHOR>
 * @date 2020/11/13
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel("主数据基类")
@Data
public abstract class MasterEntity extends BaseEntity {
    @ApiModelProperty("此UUID和modifyFlag一同用于数据修改时的签出签入操作，理论上UUID为空时modifyFlag为N，UUID有值时modifyFlag为Y")
    @Column(name = "UUID")
    private String uuid;

    @ApiModelProperty("此modifyFlag和UUID一同用于数据修改时的签出签入操作，执行签出后该字段变为Y，签入后该字段变为N，默认为N")
    @Column(name = "MDFY_FLG")
    private String modifyFlag = "N";

    @ApiModelProperty("数据废弃标识符")
    @Column(name = "OBSLT_FLG")
    private String obsoleteFlag = "N";
}
