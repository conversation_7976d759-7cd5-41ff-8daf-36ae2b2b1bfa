package cec.jiutian.security.base.dto;

import cec.jiutian.security.base.po.DynamicSqlColumnPO;
import cec.jiutian.security.base.po.DynamicSqlConditionPO;
import cec.jiutian.security.base.po.DynamicSqlLinkPO;
import cec.jiutian.security.base.po.DynamicSqlOrderPO;
import cec.jiutian.security.base.po.DynamicSqlTablePO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;

@Data
@ApiModel("dynamic Sql Config Result")
public class DynamicSqlConfigResultDTO extends BaseOpDTO {

    @ApiModelProperty(name = "identifier", notes = "自增ID")
    private Long identifier;

    @ApiModelProperty(name = "sql Id", notes = "sql 唯一id")
    private String sqlId;

    @ApiModelProperty(name = "description", notes = "功能描述")
    private String description;

    @ApiModelProperty(name = "column list", notes = "映射列表")
    private List<DynamicSqlColumnPO> columnList;

    @ApiModelProperty(name = "table list", notes = "table列表")
    private List<DynamicSqlTablePO> tableList;

    @ApiModelProperty(name = "link list", notes = "连接列表")
    private List<DynamicSqlLinkPO> linkList;

    @ApiModelProperty(name = "condition list", notes = "条件列表")
    private List<DynamicSqlConditionPO> conditionList;

    @ApiModelProperty(name = "order list", notes = "排序列表")
    private List<DynamicSqlOrderPO> orderList;

}
