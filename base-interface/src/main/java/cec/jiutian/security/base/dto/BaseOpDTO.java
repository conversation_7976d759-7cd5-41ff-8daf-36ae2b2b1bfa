package cec.jiutian.security.base.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
@Data
public class BaseOpDTO implements Serializable {

    /**
     * 失效原因
     */
    @ApiModelProperty("失效原因")
    private String disableReasonText;

    /**
     * 废弃原因
     */
    @ApiModelProperty("废弃原因")
    private String obsoleteReasonText;

    /**
     * 最后操作备注
     */
    @ApiModelProperty("最后操作备注")
    private String lastEventComment;

    /**
     * 最后操作人
     */
    @ApiModelProperty(value = "最后操作人", required = true)
    private String lastEventUser;

}
