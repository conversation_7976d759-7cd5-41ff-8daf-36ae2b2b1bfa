package cec.jiutian.security.base.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import jakarta.validation.constraints.NotNull;

@Data
@ApiModel("dynamic Sql Config Delete")
public class DynamicSqlConfigDeleteDTO extends BaseOpDTO {

    @NotNull
    @ApiModelProperty(name = "Identifier", notes = "自增ID")
    private Long identifier;
}
