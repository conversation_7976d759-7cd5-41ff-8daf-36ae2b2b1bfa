package cec.jiutian.security.base.dto;

import cec.jiutian.security.base.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2023/3/30
 */
@Data
@ApiModel(value = "dynamic sql table", description = "sql table")
public class DynamicSqlTableDTO extends BaseEntity {

    @NotBlank
    @ApiModelProperty(name = "table name", notes = "table名称")
    private String tableName;

    @NotBlank
    @ApiModelProperty("table别称，前端生成，添加表时计算获得T开头后面顺序+1")
    private String tableAlias;

    @ApiModelProperty(name = "sort num", notes = "排序")
    private Long sortNum;
}
