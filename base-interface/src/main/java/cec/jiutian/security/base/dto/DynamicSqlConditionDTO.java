package cec.jiutian.security.base.dto;

import cec.jiutian.security.base.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

@Data
@ApiModel(value = "dynamic sql condition", description = "sql condition")
public class DynamicSqlConditionDTO extends BaseEntity {

    @NotBlank
    @ApiModelProperty(name = "table Name", notes = "表名称")
    private String tableName;

    @ApiModelProperty(name = "table Alias", notes = "表别名")
    private String tableAlias="";

    @NotBlank
    @ApiModelProperty(name = "column_name", notes = "表字段名称")
    private String columnName;

    @NotBlank
    @ApiModelProperty(name = "formula", notes = "公式")
    private String formula;

    @ApiModelProperty(name = "left Brackets", notes = "左括号")
    private String leftBrackets="";

    @ApiModelProperty(name = "right brackets", notes = "右括号")
    private String rightBrackets="";

    @ApiModelProperty(name = "next condition", notes = "下一个连接符号 and、or")
    private String nextCondition="";

    @NotBlank
    @ApiModelProperty(name = "value", notes = "值")
    private String value;

    @ApiModelProperty(name = "sort num", notes = "排序")
    private Long sortNum;



    /**
     * 字段类别
     */
    private Object fieldType;
}
