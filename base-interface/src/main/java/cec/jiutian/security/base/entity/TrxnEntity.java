package cec.jiutian.security.base.entity;

import cec.jiutian.security.base.annotation.AutoId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 事务实体，具备事务处理的实体类
 * <AUTHOR>
 * @date 2023/5/4
 */
@Data
public abstract class TrxnEntity implements Serializable {
    private static final long serialVersionUID = 3723094072357385619L;
    @AutoId
    private String gid;
    /**
     * 最后修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastTrxnTs;
    /**
     * 事务ID，由日志模块产生
     */
    private String lastTrxnId;
}
