package cec.jiutian.security.base.query.dto;

import cec.jiutian.security.base.dto.BaseQueryDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "dynamic sql config query", description = "sql config query")
public class DynamicSqlConfigQueryDTO extends BaseQueryDTO {


    @ApiModelProperty(name = "sql Id", notes = "sql 唯一id")
    private String sqlId;

    @ApiModelProperty(name = "description", notes = "功能描述")
    private String description;

}
