package cec.jiutian.security.base.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.Date;

/**
 * <AUTHOR> @date 2022/04/28
 * 生效禁用实体，普通表实体无需继承此实体，只需保证此实体中的字段在表实体中都存在
 */
@Data
public class ValidEntity {

    /**
     * 状态的启用状态，启用或禁用,ENUM=EnableStatus
     */
    @ApiModelProperty("状态的启用状态，启用或禁用")
    @Column(name = "VLD_FLG")
    private String validFlag;

    /**
     * 生效日期
     */
    @ApiModelProperty("生效日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @Column(name = "EFCTV_DT")
    private Date effectiveDate;

    /**
     * 生效账号ID UUID
     */
    @ApiModelProperty("生效账号ID")
    @Column(name = "EFCTV_ACNT_ID")
    private String effectiveAccountIdentifier;

    /**
     * 生效账号姓名
     */
    @ApiModelProperty("生效账号姓名")
    @Column(name = "EFCTV_ACNT_NM")
    private String effectiveAccountName;

    /**
     * 失效日期
     */
    @ApiModelProperty("失效日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "Asia/Shanghai")
    @Column(name = "DSBL_DT")
    private Date disableDate;

    /**
     * 失效账号ID  UUID
     */
    @ApiModelProperty("失效账号ID")
    @Column(name = "DSBL_ACNT_ID")
    private String disableAccountIdentifier;

    /**
     * 失效账号姓名
     */
    @ApiModelProperty("失效账号姓名")
    @Column(name = "DSBL_ACNT_NM")
    private String disableAccountName;

    /**
     * 失效原因
     */
    @ApiModelProperty("失效原因")
    @Column(name = "DSBL_RSN_TX")
    private String disableReasonText;

}
