package cec.jiutian.security.base.po;

import cec.jiutian.security.base.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Data
@Entity
@Table(name = "fd_table_service_id_config")
@ApiModel(value = "table service id config", description = "table service id config")
public class TableServiceIdConfigPO extends BaseEntity {

    @Id
    @Column(name = "id")
    private Long identifier;

    @Column(name = "service_id")
    @ApiModelProperty(name = "service id", notes = "服务id")
    private String serviceName;

    @Column(name = "mac_address")
    @ApiModelProperty(name = "mac address", notes = "mac名称")
    private String macAddress;

    @Column(name = "port")
    @ApiModelProperty(name = "port", notes = "端口号")
    private String port;
}
