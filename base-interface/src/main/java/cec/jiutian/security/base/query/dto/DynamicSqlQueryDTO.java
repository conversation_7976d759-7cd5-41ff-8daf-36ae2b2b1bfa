package cec.jiutian.security.base.query.dto;

import cec.jiutian.security.base.dto.BaseQueryDTO;
import cec.jiutian.security.base.po.DynamicSqlColumnPO;
import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "dynamic sql query", description = "dynamic sql query")
public class DynamicSqlQueryDTO extends BaseQueryDTO {


    @ApiModelProperty(name = "sql Id", notes = "sql 唯一id")
    private String sqlId;

    private String conditionScript;

    private List<DynamicSqlColumnPO> columnList;

    private JSONObject jsonObjectQuery;

}
