package cec.jiutian.security.base.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/3/30
 */
@Data
@ApiModel(value = "dynamic sql order", description = "sql order")
public class DynamicSqlOrderDTO implements Serializable {

    private static final long serialVersionUID = 2264360183100907369L;

    @ApiModelProperty(name = "table Alias", notes = "表别名")
    private String tableAlias="";

    @NotBlank
    @ApiModelProperty(name = "table name", notes = "表名称")
    private String tableName;

    @NotBlank
    @ApiModelProperty(name = "column_name", notes = "字段")
    private String columnName;

    @ApiModelProperty(name = "sort", notes = "排序，默认是正序")
    private Boolean sort;

}
