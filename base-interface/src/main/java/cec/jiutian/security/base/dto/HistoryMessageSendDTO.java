package cec.jiutian.security.base.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.Date;
import java.util.List;

@ApiModel("HistoryMessageSendDTO")
@Data
public class HistoryMessageSendDTO {
    private List<HistoryResultDTO> historyResultDTOList;
    private String operationUUID;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "Asia/Shanghai")
    private Date operationTime;
    private String operationUserName;
    private String operationEventName;
    private String organizationIdentifier;
}
