package cec.jiutian.security.base.po;

import cec.jiutian.security.base.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

@Data
@Entity
@Table(name = "fd_dymic_sql_column")
@ApiModel(value = "dynamic sql column", description = "dynamic sql column entity")
public class DynamicSqlColumnPO extends BaseEntity {

    @Id
    @Column(name = "id")
    @ApiModelProperty(name = "Identifier", notes = "自增ID")
    private Long identifier;

    @Column(name = "sql_id")
    @ApiModelProperty(name = "sql Id", notes = "sql 唯一id")
    private String sqlId;

    @Column(name = "column_name")
    @ApiModelProperty(name = "column name", notes = "table字段名称")
    private String columnName;

    @Transient
    @ApiModelProperty(name = "column type", notes = "table字段类型")
    private String columnType;

    @Column(name = "field")
    @ApiModelProperty(name = "field", notes = "java对象字段")
    private String field;

    @Column(name = "table_name")
    @ApiModelProperty(name = "table name", notes = "表名称")
    private String tableName;

    @Column(name = "table_alias")
    @ApiModelProperty(name = "table Alias", notes = "表别名")
    private String tableAlias;

    @Column(name = "sort_num")
    @ApiModelProperty(name = "sort num", notes = "排序")
    private Integer sortNum;
}
