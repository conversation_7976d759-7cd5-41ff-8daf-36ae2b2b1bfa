package cec.jiutian.security.base.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/11/13
 */
@Data
@ApiModel("实体类基类，包含审计字段。若业务实体类具有审计字段，可继承此类")
public abstract class BaseEntity implements Serializable {
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "Asia/Shanghai")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss.SSS")
    @Column(name = "CRTE_TM")
    private Date createTime;

    //创建作业员
    @Column(name = "CRTE_USR")
    private String createUser;

    //事件名称
    @Column(name = "LST_EVNT_NM")
    private String lastEventName;

    //事件时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "Asia/Shanghai")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss.SSS")
    @Column(name = "LST_EVNT_TM")
    private Date lastEventTime;

    //事件作业员
    @Column(name = "LST_EVNT_USR")
    private String lastEventUser;

    //事件备注
    @Column(name = "LST_EVNT_CMNT")
    private String lastEventComment;
}
