package cec.jiutian.security.base.po;

import cec.jiutian.security.base.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

@Data
@Entity
@Table(name = "fd_table_id_config")
@ApiModel(value = "table id config", description = "table id config")
public class TableIdConfigPO extends BaseEntity {

    @Id
    @Column(name = "id")
    @ApiModelProperty(name = "Identifier", notes = "自增ID")
    private Long identifier;

    @Column(name = "table_name")
    @ApiModelProperty(name = "table name", notes = "table名称")
    private String tableName;

    @Column(name = "sequence_name")
    @ApiModelProperty(name = "sequence_name", notes = "对应序列名称")
    private String sequenceName;

    @Column(name = "now_sequence")
    @ApiModelProperty(name = "now sequence", notes = "当前序列")
    private Long nowSequence;

    @Column(name = "id_type")
    @ApiModelProperty(name = "identifier type", notes = "id类型 GENERAL:虚拟序列，SEQUENCE:真实序列")
    private String identifierType;

    @Transient
    private Long serviceId;
}
