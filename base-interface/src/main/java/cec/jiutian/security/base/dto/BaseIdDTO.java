package cec.jiutian.security.base.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <p>该类用于以自增主键为参数的接口，参数固定为identifier，且为必传参数</p>
 * <p>根据SRP原则，该类不允许继承和修改</p>
 * <AUTHOR>
 * @date 2022/5/11
 */
@Data
@ApiModel("BaseIdDTO")
public final class BaseIdDTO extends BaseOpDTO implements Serializable {
    private static final long serialVersionUID = -6533295758081978208L;

    @ApiModelProperty("自增主键")
    @NotNull
    private Long identifier;
}
