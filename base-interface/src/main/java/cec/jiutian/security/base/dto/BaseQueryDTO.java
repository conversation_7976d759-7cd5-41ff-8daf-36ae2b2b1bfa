package cec.jiutian.security.base.dto;

import cec.jiutian.security.base.enumeration.EnableState;
import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/8/26
 * @description 查询DTO基类
 */
@Data
public class BaseQueryDTO {

    private Boolean isPage;

    /**
     * 分页条数，为0时不分页
     */
    private int pageSize;

    /**
     * 分页页数，从1开始
     */
    private int pageNum;

    /**
     * 数据域字段，暂时有工厂/仓库两种类型
     */
    private String entityCode;

    /**
     * 数据域字段，用于筛选的具体值
     */
    private String dataDomain;

    /**
     * 随机排序字段和类型，格式见{@link SortParam}
     */
    @ApiModelProperty("随机排序字段和类型，格式见SortParam")
    private List<SortParam> sort;

    /**
     * 通用查询条件
     */
    @ApiModelProperty("通用查询条件")
    private String generalQueryCondition;


    /*   前端传参参考
        params: {
        dictionaryCode: 'RightElementType',
                filter: {
            typeCode: ['1'],
        },
        columns: ['typeCode']
    },*/

    @ApiModelProperty("前端需要过滤的行的值,如果为null则返回所有行的值，如果有值则只返回满足filter中配置行数据的值")
    private JSONObject filter;

    @ApiModelProperty("前端需要过滤的列的值，如果为null则返回所有列字段，如果有值则只返回columns中的包含的列值")
    private List<String> columns;

    private String enabled = EnableState.Enabled.toString();

    private String disabled = EnableState.Disabled.toString();

    @Data
    public static class SortParam {
        private String field;
        private String sortType;

        public SortParam(String field, String sortType) {
            this.field = field;
            this.sortType = sortType;
        }
    }

    @Data
    @ApiModel("通用查询参数")
    public static class GeneralQueryParam {
        private String field;
        private String jdbcType;
        private String value;

        public GeneralQueryParam(String field, String jdbcType, String value) {
            this.field = field;
            this.jdbcType = jdbcType;
            this.value = value;
        }
    }
}
