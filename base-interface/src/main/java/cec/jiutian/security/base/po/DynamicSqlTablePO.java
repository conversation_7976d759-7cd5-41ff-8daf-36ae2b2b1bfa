package cec.jiutian.security.base.po;

import cec.jiutian.security.base.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Data
@Entity
@Table(name = "fd_dymic_sql_table")
@ApiModel(value = "dynamic sql table", description = "dynamic sql table entity")
public class DynamicSqlTablePO extends BaseEntity {

    /**
     * Identifier;自增ID
     */
    @Id
    @Column(name = "id")
    @ApiModelProperty(name = "Identifier", notes = "自增ID")
    private Long identifier;

    @Column(name = "sql_id")
    @ApiModelProperty(name = "sql Id", notes = "sql 唯一id")
    private String sqlId;

    @Column(name = "table_name")
    @ApiModelProperty(name = "table name", notes = "table名称")
    private String tableName;

    @Column(name = "table_alias")
    @ApiModelProperty(name = "table alias", notes = "table别称")
    private String tableAlias;

    @Column(name = "sort_num")
    @ApiModelProperty(name = "sort num", notes = "排序")
    private Integer sortNum;
}
