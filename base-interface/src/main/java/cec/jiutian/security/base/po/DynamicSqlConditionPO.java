package cec.jiutian.security.base.po;

import cec.jiutian.security.base.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Data
@Entity
@Table(name = "fd_dymic_sql_condition")
@ApiModel(value = "dynamic sql condition", description = "dynamic sql condition entity")
public class DynamicSqlConditionPO extends BaseEntity {

    /**
     * Identifier;自增ID
     */
    @Id
    @Column(name = "id")
    @ApiModelProperty(name = "Identifier", notes = "自增ID")
    private Long identifier;

    @Column(name = "sql_id")
    @ApiModelProperty(name = "sql Id", notes = "sql 唯一id")
    private String sqlId;

    @Column(name = "table_name")
    @ApiModelProperty(name = "table name", notes = "表名称")
    private String tableName;

    @Column(name = "table_alias")
    @ApiModelProperty(name = "table Alias", notes = "表别名")
    private String tableAlias;

    @Column(name = "column_name")
    @ApiModelProperty(name = "column name", notes = "表字段名称")
    private String columnName;

    @Column(name = "formula")
    @ApiModelProperty(name = "formula", notes = "公式")
    private String formula;

    @Column(name = "value")
    @ApiModelProperty(name = "value", notes = "值")
    private String value;

    @Column(name = "left_brackets")
    @ApiModelProperty(name = "left Brackets", notes = "左括号")
    private String leftBrackets;

    @Column(name = "right_brackets")
    @ApiModelProperty(name = "right brackets", notes = "右括号")
    private String rightBrackets;

    @Column(name = "next_condition")
    @ApiModelProperty(name = "next condition", notes = "下一个连接符号 and、or")
    private String nextCondition;

    @Column(name = "sort_num")
    @ApiModelProperty(name = "sort num", notes = "排序")
    private Integer sortNum;
}
