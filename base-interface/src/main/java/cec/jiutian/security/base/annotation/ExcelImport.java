package cec.jiutian.security.base.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface ExcelImport {
    /**
     * 子表名称，默认为主表名+detail
     */
    String subTableName() default "";


    /**
     * 当前表是否有历史表
     */
    boolean historyTableFlag() default true;

    /**
     * 唯一键字段
     */
    String[] uniqueColumns() default {};

    /**
     * 非空列字段
     */
    String[] notNullColumns() default {};


}
