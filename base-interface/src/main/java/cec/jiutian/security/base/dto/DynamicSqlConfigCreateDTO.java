package cec.jiutian.security.base.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import java.util.Date;
import java.util.List;

@Data
@ApiModel("dynamic Sql Config Create")
public class DynamicSqlConfigCreateDTO extends BaseOpDTO {

    @NotBlank
    @ApiModelProperty(name = "sql Id", notes = "sql 唯一id")
    private String sqlId;

    @ApiModelProperty(name = "description", notes = "功能描述")
    private String description;

    @NotEmpty
    @ApiModelProperty(name = "column list", notes = "映射列表")
    private List<DynamicSqlColumnDTO> columnList;

    @NotEmpty
    @ApiModelProperty(name = "table list", notes = "table列表")
    private List<DynamicSqlTableDTO> tableList;

    @ApiModelProperty(name = "link list", notes = "连接列表")
    private List<DynamicSqlLinkDTO> linkList;

    @ApiModelProperty(name = "condition list", notes = "条件列表")
    private List<DynamicSqlConditionDTO> conditionList;

    @ApiModelProperty(name = "order list", notes = "排序列表")
    private List<DynamicSqlOrderDTO> orderList;

    private String createUser;

    private Date createTime;

    private Date lastEventTime;

}
