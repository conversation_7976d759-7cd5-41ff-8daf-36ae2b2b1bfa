package cec.jiutian.security.base.po;

import cec.jiutian.security.base.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Data
@Entity
@Table(name = "fd_dymic_sql_link")
@ApiModel(value = "dynamic sql link", description = "dynamic sql link entity")
public class DynamicSqlLinkPO extends BaseEntity {

    /**
     * Identifier;自增ID
     */
    @Id
    @Column(name = "id")
    @ApiModelProperty(name = "Identifier", notes = "自增ID")
    private Long identifier;

    @Column(name = "sql_id")
    @ApiModelProperty(name = "sql Id", notes = "对应sqlId")
    private String sqlId;

    @Column(name = "table_name")
    @ApiModelProperty(name = "table alias", notes = "table名称")
    private String tableName;

    @Column(name = "table_alias")
    @ApiModelProperty(name = "table Alias", notes = "表别名")
    private String tableAlias;

    @Column(name = "column_names")
    @ApiModelProperty(name = "column name", notes = "表字段名称多个使用','分割")
    private String columnNames;

    @Column(name = "link")
    @ApiModelProperty(name = "table link", notes = "table连接")
    private String link;

    @Column(name = "to_table_name")
    @ApiModelProperty(name = "to table name", notes = "被连接表名")
    private String toTableName;

    @Column(name = "to_table_alias")
    @ApiModelProperty(name = "to Table Alias", notes = "被连接表别名")
    private String toTableAlias;

    @Column(name = "to_column_names")
    @ApiModelProperty(name = "to column", notes = "被连接字段名称使用','分割")
    private String toColumnNames;

    @Column(name = "sort_num")
    @ApiModelProperty(name = "sort num", notes = "排序")
    private Integer sortNum;

}
