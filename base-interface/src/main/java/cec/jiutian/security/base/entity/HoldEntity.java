package cec.jiutian.security.base.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.Date;

/**
 * @description: 冻结功能实体，普通表实体无需继承此实体，只需保证此实体中的字段在表实体中都存在
 * @author: chenjx
 * @date: 20220526
 */
@Data
public class HoldEntity {
    /**
     * Hold State;载具冻结状态
     */
    @Column(name = "HLD_ST")
    @ApiModelProperty(name = "Hold State", notes = "载具冻结状态")
    private String holdState;

    /**
     * Hold Date;冻结时间
     */
    @Column(name = "HLD_DT")
    @ApiModelProperty(name = "Hold Date", notes = "冻结时间")
    private Date holdDate;

    /**
     * Hold Reason Description;冻结原因
     */
    @Column(name = "HLD_RSN_DS")
    @ApiModelProperty(name = "Hold Reason Description", notes = "冻结原因")
    private String holdReasonDescription;

}
