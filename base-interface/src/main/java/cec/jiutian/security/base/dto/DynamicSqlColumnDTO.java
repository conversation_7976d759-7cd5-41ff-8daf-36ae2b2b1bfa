package cec.jiutian.security.base.dto;

import cec.jiutian.security.base.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

@Data
@ApiModel(value = "dynamic sql column", description = "sql column")
public class DynamicSqlColumnDTO extends BaseEntity {

    @NotBlank
    @ApiModelProperty(name = "column_name", notes = "table字段名称")
    private String columnName;

    @ApiModelProperty(name = "field", notes = "java对象字段")
    private String field;

    @ApiModelProperty(name = "column type", notes = "字段类型")
    private String fieldType;

    @ApiModelProperty(name = "table name", notes = "表名称")
    private String tableName;

    @ApiModelProperty(name = "table Alias", notes = "表别名")
    private String tableAlias="";
}
