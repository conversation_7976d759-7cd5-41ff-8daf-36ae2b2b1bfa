package cec.jiutian.security.base.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.Date;

/**
 * @description: 废弃功能实体，普通表实体无需继承此实体，只需保证此实体中的字段在表实体中都存在
 * @author: chenjx
 * @date: ********
 */
@Data
public class ObsoleteEntity {
    /**
     * Obsolete Flag;废弃标记
     */
    @Column(name = "OBSLT_FLG")
    @ApiModelProperty(name = "Obsolete Flag", notes = "废弃标记")
    private String obsoleteFlag;

    /**
     * Obsolete Account Identifier;废弃人员ID
     */
    @Column(name = "OBSLT_ACNT_ID")
    @ApiModelProperty(name = "Obsolete Account Identifier", notes = "废弃人员ID")
    private String obsoleteAccountIdentifier;

    /**
     * Obsolete Account Name;废弃人员姓名
     */
    @Column(name = "OBSLT_ACNT_NM")
    @ApiModelProperty(name = "Obsolete Account Name", notes = "废弃人员姓名")
    private String obsoleteAccountName;

    /**
     * Obsolete Time;废弃时间
     */
    @Column(name = "OBSLT_TM")
    @ApiModelProperty(name = "Obsolete Time", notes = "废弃时间")
    private Date obsoleteTime;

    /**
     * Obsolete Reason Text;废弃原因
     */
    @Column(name = "OBSLT_RSN_TX")
    @ApiModelProperty(name = "Obsolete Reason Text", notes = "废弃原因")
    private String obsoleteReasonText;
}
