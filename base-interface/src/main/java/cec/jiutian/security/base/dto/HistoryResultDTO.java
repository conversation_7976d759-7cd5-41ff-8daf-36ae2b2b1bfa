package cec.jiutian.security.base.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;

@ApiModel("HistoryResultDTO")
@Data
public class HistoryResultDTO implements Serializable {
    private String oldTableName;
    private String oldPrimaryKeyAndValue;
    private String oldContentText;
    private String newTableName;
    private String newPrimaryKeyAndValue;
    private String newContentText;
    private String commandType;
    private Long historyDataSetDefinitionIdentifier;
    private String dataSetUUID;
    private String dataSetName;
    private Long levelNumber;
}
