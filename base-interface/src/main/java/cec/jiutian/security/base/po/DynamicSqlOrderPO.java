package cec.jiutian.security.base.po;

import cec.jiutian.security.base.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @date 2023/3/30
 */
@Data
@Entity
@Table(name = "fd_dymic_sql_order")
@ApiModel(value = "dynamic sql order", description = "dynamic sql order entity")
public class DynamicSqlOrderPO extends BaseEntity {

    private static final long serialVersionUID = 2264360183100907369L;

    /**
     * Identifier;自增ID
     */
    @Id
    @Column(name = "id")
    @ApiModelProperty(name = "Identifier", notes = "自增ID")
    private Long identifier;

    @Column(name = "sql_id")
    @ApiModelProperty(name = "sql Id", notes = "sql 唯一id")
    private String sqlId;

    @Column(name = "table_alias")
    @ApiModelProperty(name = "table Alias", notes = "表别名")
    private String tableAlias;

    @Column(name = "table_name")
    @ApiModelProperty(name = "table name", notes = "表名称")
    private String tableName;

    @Column(name = "column_name")
    @ApiModelProperty(name = "column_name", notes = "字段")
    private String columnName;

    @Column(name = "sort")
    @ApiModelProperty(name = "sort", notes = "排序，默认是正序")
    private Boolean sort;

    @Column(name = "sort_num")
    @ApiModelProperty(name = "sort", notes = "排序编号")
    private Integer sortNum;

}
