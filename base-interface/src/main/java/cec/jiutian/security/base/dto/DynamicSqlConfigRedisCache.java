package cec.jiutian.security.base.dto;

import lombok.Data;
import java.io.Serializable;

@Data
public class DynamicSqlConfigRedisCache implements Serializable {
    private static final long serialVersionUID = 640746512350735319L;

    //redis中存储流程文件为string类型
    private String configString;

    public DynamicSqlConfigRedisCache() {
        super();
    }

    public DynamicSqlConfigRedisCache(String configString) {
        super();
        this.configString = configString;
    }

}
