package cec.jiutian.security.base.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;

@Data
@ApiModel("dynamic Sql Config Update")
public class DynamicSqlConfigUpdateDTO extends BaseOpDTO {

    @NotNull
    @ApiModelProperty(name = "identifier", notes = "自增ID")
    private Long identifier;

    @NotEmpty
    @ApiModelProperty(name = "column list", notes = "映射列表")
    private List<DynamicSqlColumnDTO> columnList;

    @NotEmpty
    @ApiModelProperty(name = "table list", notes = "table列表")
    private List<DynamicSqlTableDTO> tableList;

    @ApiModelProperty(name = "link list", notes = "连接列表")
    private List<DynamicSqlLinkDTO> linkList;

    @ApiModelProperty(name = "condition list", notes = "条件列表")
    private List<DynamicSqlConditionDTO> conditionList;

    @ApiModelProperty(name = "order list", notes = "排序列表")
    private List<DynamicSqlOrderDTO> orderList;

}
