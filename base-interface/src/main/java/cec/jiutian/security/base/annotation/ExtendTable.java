package cec.jiutian.security.base.annotation;

import org.springframework.core.annotation.AliasFor;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @date 2023/4/21
 */
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Target(ElementType.TYPE)
public @interface ExtendTable {
    @AliasFor("value")
    String name() default "";

    String value() default "";

    //Class<?> nameGenerator() default NameGenerator.class;
}
