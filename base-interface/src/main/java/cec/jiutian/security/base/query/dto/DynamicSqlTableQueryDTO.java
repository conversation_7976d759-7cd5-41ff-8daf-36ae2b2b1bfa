package cec.jiutian.security.base.query.dto;

import cec.jiutian.security.base.dto.BaseQueryDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "dynamic sql table query", description = "dynamic sql table query")
public class DynamicSqlTableQueryDTO extends BaseQueryDTO {


    @ApiModelProperty(name = "table Name", notes = "table表名称")
    private String tableName;

}
