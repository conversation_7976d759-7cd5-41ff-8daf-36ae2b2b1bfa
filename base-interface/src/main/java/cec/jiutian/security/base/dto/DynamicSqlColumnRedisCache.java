package cec.jiutian.security.base.dto;

import lombok.Data;
import java.io.Serializable;

@Data
public class DynamicSqlColumnRedisCache implements Serializable {
    private static final long serialVersionUID = 640746512350735319L;

    //redis中存储流程文件为string类型
    private String columnListString;

    public DynamicSqlColumnRedisCache() {
        super();
    }

    public DynamicSqlColumnRedisCache(String columnListString) {
        super();
        this.columnListString = columnListString;
    }

}
