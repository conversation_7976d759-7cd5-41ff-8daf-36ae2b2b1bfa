package cec.jiutian.security.base.po;

import cec.jiutian.security.base.annotation.UniqueColumn;
import cec.jiutian.security.base.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Data
@Entity
@Table(name = "fd_dymic_sql_config")
@ApiModel(value = "dynamic sql config", description = "dynamic sql config entity")
public class DynamicSqlConfigPO extends BaseEntity {

    /**
     * Identifier;自增ID
     */
    @Id
    @Column(name = "id")
    @ApiModelProperty(name = "Identifier", notes = "自增ID")
    private Long identifier;

    @UniqueColumn
    @Column(name = "sql_id")
    @ApiModelProperty(name = "sql Id", notes = "sql 唯一id")
    private String sqlId;

    @Column(name = "description")
    @ApiModelProperty(name = "description", notes = "功能描述")
    private String description;


    @Column(name = "sql_script")
    @ApiModelProperty(name = "sql Script", notes = "sql脚本")
    private String sqlScript;

    @Column(name = "select_block")
    @ApiModelProperty(name = "sql select block", notes = "sql的查询模块脚本")
    private String selectBlock;

    @Column(name = "condition_block")
    @ApiModelProperty(name = "sql condition block", notes = "sql的查询条件模块脚本")
    private String conditionBlock;

    @Column(name = "sort_block")
    @ApiModelProperty(name = "sql sort block", notes = "sql的排序模块脚本")
    private String sortBlock;

    @Column(name = "status")
    @ApiModelProperty(name = "status", notes = "状态")
    private String status;
}
