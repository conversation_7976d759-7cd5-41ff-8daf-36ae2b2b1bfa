package cec.jiutian.security.base.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.NotNull;
import java.util.Date;

/**
 * 历史记录查询参数DTO基类，增加了日期范围的基础参数，可选择性进行校验
 *
 * <AUTHOR>
 * @date 2021/5/21
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel("BaseHistoryQueryDTO")
@Data
public class BaseHistoryQueryDTO extends BaseQueryDTO {
    @NotNull
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("最后操作时间的时间查询范围")
    private Date startLastEventTime;
    @NotNull
    @ApiModelProperty("最后操作时间的时间查询范围")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endLastEventTime;
    @ApiModelProperty("精确匹配最后操作时间")
    @JsonFormat(pattern = "yyyy-MM-dd hh:mm:ss.SSS")
    private Date lastEventTime;
}
