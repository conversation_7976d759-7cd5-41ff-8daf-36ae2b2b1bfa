package cec.jiutian.security.base.dto;

import cec.jiutian.security.base.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/30
 */
@Data
@ApiModel(value = "dynamic sql link", description = "sql link")
public class DynamicSqlLinkDTO extends BaseEntity {

    @NotBlank
    @ApiModelProperty(name = "column", notes = "表字段名称数组,数组字段和被连接字段index一一对应")
    private List<String> columns;

    @ApiModelProperty(name = "table Alias", notes = "表别名")
    private String tableAlias="";

    @NotBlank
    @ApiModelProperty(name = "table name", notes = "table名称")
    private String tableName;

    @ApiModelProperty(name = "table link", notes = "table连接 LEFT JOIN、INNER JOIN、RIGHT JOIN")
    private String link;

    @ApiModelProperty(name = "to table name", notes = "被连接表名")
    private String toTableName;

    @ApiModelProperty(name = "to table Alias", notes = "被连接表别名")
    private String toTableAlias="";

    @ApiModelProperty(name = "to column", notes = ",数组字段和被连接字段index一一对应")
    private List<String> toColumns;

    @ApiModelProperty(name = "to table where", notes = "被连接表条件")
    private String toTableCondition="";

    @ApiModelProperty(name = "sort num", notes = "排序")
    private Long sortNum;

}
