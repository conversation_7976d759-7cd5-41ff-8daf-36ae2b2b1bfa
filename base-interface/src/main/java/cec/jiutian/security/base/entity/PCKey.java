package cec.jiutian.security.base.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Transient;

/**
 * @title: PCKey.java
 * @package cec.jiutian.modeler.po
 * @description: 功能说明（jira task id）
 * @author: <EMAIL>
 * @date: 2022-4-15 10:36
 * @version: 2.5.3
 */
@Data
public class PCKey extends BaseEntity {
    /**
     * 模块ID
     */
    @Id
    @Column(name = "ID")
    private Long identifier;

    /**
     * 父级层级ID
     */
    @Column(name = "PID")
    private Long parentIdentifier;

    /**
     * 节点类型，parent/child
     */
    @Transient
    private String nodeType;

    /**
     * 子节点个数
     */
    @Transient
    private Integer childCount;

    /**
     * 是否返回null数据
     */
    @Transient
    private Boolean showNullFlag = true;

    /**
     * 是否返回审计字段
     */
    @Transient
    private Boolean showAuditFlag = false;

}
