package cec.jiutian.gateway.filter;

import cec.jiutian.gateway.filter.XssAttackException;
import org.owasp.encoder.Encode;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpRequestDecorator;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

//@Component
public class XssFilter implements GlobalFilter, Ordered {

    // XSS检测正则（包含常见攻击模式）
    private static final Pattern[] XSS_PATTERNS = {
            Pattern.compile("<script>(.*?)</script>", Pattern.CASE_INSENSITIVE),
            Pattern.compile("javascript:", Pattern.CASE_INSENSITIVE),
            Pattern.compile("onload(.*?)=", Pattern.CASE_INSENSITIVE)
    };

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();

        // 清洗请求参数
        request = new RequestBodyCleanWrapper(exchange, request);

        // 清洗请求头
        request = new HeaderCleanWrapper(request);

        return chain.filter(exchange.mutate().request(request).build());
    }

    @Override
    public int getOrder() {
        return -100; // 最高优先级执行
    }

    // 请求参数包装器
    private class RequestBodyCleanWrapper extends ServerHttpRequestDecorator {
        private final ServerWebExchange exchange;

        public RequestBodyCleanWrapper(ServerWebExchange exchange, ServerHttpRequest delegate) {
            super(delegate);
            this.exchange = exchange;
        }

        @Override
        public Flux<DataBuffer> getBody() {
            return super.getBody().collectList().flatMap(dataBuffers -> {
                if (dataBuffers.isEmpty()) {
                    return Mono.just(exchange.getResponse().bufferFactory().allocateBuffer());
                }
                DataBuffer joinedDataBuffer = DataBufferUtils.join(Flux.fromIterable(dataBuffers)).block();
                String bodyStr = StandardCharsets.UTF_8.decode(joinedDataBuffer.asByteBuffer()).toString();
                String cleanedBodyStr = cleanXSS(bodyStr);
                DataBuffer cleanedDataBuffer = exchange.getResponse().bufferFactory().wrap(cleanedBodyStr.getBytes(StandardCharsets.UTF_8));
                return Mono.just(cleanedDataBuffer);
            }).flux();
        }

        private String cleanXSS(String value) {
            if (value == null) return null;

            // 高危标签直接阻断
            for (Pattern pattern : XSS_PATTERNS) {
                if (pattern.matcher(value).find()) {
                    throw new XssAttackException("检测到XSS攻击内容");
                }
            }

            // 转义HTML标签
            return Encode.forHtmlContent(value);
        }
    }

    // 请求头包装器
    private class HeaderCleanWrapper extends ServerHttpRequestDecorator {
        public HeaderCleanWrapper(ServerHttpRequest delegate) {
            super(delegate);
        }

        @Override
        public HttpHeaders getHeaders() {
            HttpHeaders headers = new HttpHeaders();
            getDelegate().getHeaders().forEach((key, values) ->
                    headers.put(key, values.stream()
                            .map(Encode::forHtmlContent)
                            .collect(Collectors.toList()))
            );
            return headers;
        }
    }
}
