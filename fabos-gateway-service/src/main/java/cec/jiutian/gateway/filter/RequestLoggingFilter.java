package cec.jiutian.gateway.filter;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

@Component
public class RequestLoggingFilter implements GlobalFilter, Ordered {

    private static final Logger logger = LoggerFactory.getLogger("API-MONITOR");
    private static final String START_TIME_KEY = "X-Request-Start-Time";

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        // 记录请求开始时间
        exchange.getAttributes().put(START_TIME_KEY, System.currentTimeMillis());

        // 记录请求基础信息
        logRequestInfo(exchange.getRequest());

        // 在过滤器链中添加错误处理
        return chain.filter(exchange)
                .doOnError(throwable ->
                        logger.error("Request Failed: Path={}, Error={}",
                                exchange.getRequest().getPath(),
                                throwable.getMessage())
                );

    }

    private void logRequestInfo(ServerHttpRequest request) {
        String path = request.getURI().getPath();
        String method = request.getMethod().name();
        String query = request.getURI().getQuery();
        String headers = request.getHeaders().toString();

        logger.debug("Request => Method: {}, Path: {}, Query: {}, Headers: {}",
                method, path, query, headers);
    }

    private void logResponseInfo(ServerWebExchange exchange) {
        Long startTime = exchange.getAttribute(START_TIME_KEY);
        long duration = startTime != null ?
                System.currentTimeMillis() - startTime : 0;

        ServerHttpResponse response = exchange.getResponse();
        int status = response.getStatusCode() != null ?
                response.getStatusCode().value() : 500;

        logger.info("Response <= Path: {}, Status: {}, Duration: {}ms",
                exchange.getRequest().getURI().getPath(),
                status,
                duration);
    }

    @Override
    public int getOrder() {
        return Ordered.LOWEST_PRECEDENCE; // 最后执行
    }
}
