package cec.jiutian.gateway.event;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.event.RefreshRoutesEvent;
import org.springframework.cloud.gateway.route.Route;
import org.springframework.cloud.gateway.route.RouteLocator;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

@Component
public class RouteRefreshLogger implements ApplicationListener<RefreshRoutesEvent> {

    private static final Logger log = LoggerFactory.getLogger(RouteRefreshLogger.class);
    
    @Autowired
    private RouteLocator routeLocator;

    @Override
    public void onApplicationEvent(RefreshRoutesEvent event) {
        // 获取当前生效路由列表
        Flux<Route> routes = routeLocator.getRoutes();
        
        routes.collectList().subscribe(routeList -> {
            log.info("[路由刷新] 当前生效路由数量：{}", routeList.size());
            for (Route route : routeList) {
                log.debug("路由ID: {}, 目标URI: {}, 断言配置: {}",
                        route.getId(),
                        route.getUri(),
                        route.getPredicate()
                );
            }
        });
    }
}
