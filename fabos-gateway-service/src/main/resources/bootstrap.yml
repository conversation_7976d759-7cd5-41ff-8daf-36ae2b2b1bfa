server:
  port: 8080
spring:
  application:
    name: fabos-gateway
  profiles:
    active: dev
  cloud:
    nacos:
      discovery:
        username: nacos
        password: nacos
#        server-addr: 127.0.0.1:8848
        server-addr: 172.16.200.101:8848
        namespace: fabos-business-component  # 根据实际情况配置命名空间
        group: dev
      config:
        username: nacos
        password: nacos
#        server-addr: 127.0.0.1:8848
        server-addr: 172.16.200.101:8848
        namespace: fabos-business-component
        group: dev
        file-extension: json
    gateway:
      discovery:
        locator:
          enabled: true # 开启服务发现

logging:
  level:
    org.springframework.cloud.gateway: DEBUG # 开启网关内部日志
    com.example.gateway.RouteRefreshLogger: INFO

xss:
  exclude-paths:
#    - /api/richtext/save  # 富文本编辑接口
#    - /thirdparty/callback # 第三方回调接口

