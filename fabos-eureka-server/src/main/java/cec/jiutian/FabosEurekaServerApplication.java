package cec.jiutian;

import ch.qos.logback.classic.LoggerContext;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.netflix.eureka.server.EnableEurekaServer;
import org.springframework.context.annotation.Bean;

import java.util.TimeZone;

@SpringBootApplication
@EnableApolloConfig
@EnableEurekaServer
@Slf4j
public class FabosEurekaServerApplication {

	public static void main(String[] args) {
        TimeZone.setDefault(TimeZone.getTimeZone("Asia/Shanghai"));
		SpringApplication.run(FabosEurekaServerApplication.class, args);
		log.info("Eureka started");
	}

	@Bean
	public boolean removeLogAppender(@Value("${logback.detach.appender:FILE}") String appenderName) {
		// 第一步：获取日志上下文
		LoggerContext lc = (LoggerContext) LoggerFactory.getILoggerFactory();
		// 第二步：获取日志对象 （日志是有继承关系的，关闭上层，下层如果没有特殊说明也会关闭）
		ch.qos.logback.classic.Logger rootLogger = lc.getLogger("root");
		// 第三步：移除 appender
		return rootLogger.detachAppender(appenderName);
	}

}
